package com.facishare.crm.fmcg.sales.mq.factory;

import com.beust.jcommander.internal.Maps;
import com.facishare.crm.fmcg.sales.apiname.RebateApiNames;
import com.facishare.crm.fmcg.sales.exception.ApiException;
import com.facishare.crm.fmcg.sales.factory.SpringContextHolder;
import com.facishare.crm.fmcg.sales.mq.action.abstraction.IObjectDataAction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> liu<PERSON>yu
 * @create : 2023-10-27 16:47
 **/
@Slf4j
@Component
public class ObjectDataActionFactory {
    public static final Map<String, String> OBJECT_DATA_ACTION_BEAN_MAP;
    public static final Set<String> PROCESSED_OBJECT;

    static {
        OBJECT_DATA_ACTION_BEAN_MAP = Maps.newHashMap();
        OBJECT_DATA_ACTION_BEAN_MAP.put("EnterpriseRelationObj", "enterpriseRelationObjAction");
        OBJECT_DATA_ACTION_BEAN_MAP.put(RebateApiNames.OBJECT_API_NAME, "rebateObjAction");
        PROCESSED_OBJECT = OBJECT_DATA_ACTION_BEAN_MAP.keySet();
    }

    public static IObjectDataAction getBeanByApiName(String apiName) {
        String beanName = OBJECT_DATA_ACTION_BEAN_MAP.get(apiName);
        if (!SpringContextHolder.containsBean(beanName)) {
            log.error("no ObjectDataAction found for {}", apiName);
            throw new ApiException(100001, "no ObjectDataAction found for " + apiName);
        }
        return SpringContextHolder.getBean(beanName);
    }

    public static boolean processedObject(String apiName) {
        return PROCESSED_OBJECT.contains(apiName);
    }
}
