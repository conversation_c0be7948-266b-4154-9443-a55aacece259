package com.facishare.crm.fmcg.sales.mq.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.sales.apiname.CommonApiNames;
import com.facishare.crm.fmcg.sales.apiname.PaymentApiNames;
import com.facishare.crm.fmcg.sales.apiname.RebateApiNames;
import com.facishare.crm.fmcg.sales.mq.action.abstraction.IObjectDataAction;
import com.facishare.crm.fmcg.sales.mq.consumer.model.MessageBodyObj;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.contract.paas.data.PaasDataFindById;
import com.fmcg.framework.http.contract.paas.data.PaasDataIncrementUpdate;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Component("rebateObjAction")
public class RebateObjAction implements IObjectDataAction {

    public RebateObjAction(){
        System.out.println("RebateObjAction");
    }

    @Resource
    private PaasDataProxy paasDataProxy;

    @Override
    public void action(MessageBodyObj messageBodyObj) {
        if (Objects.equals(messageBodyObj.getTriggerType(), "i")) {
            log.info("listen create RebateObj: {}", JSON.toJSONString(messageBodyObj));
            afterCreation(messageBodyObj);
        }
    }

    private void afterCreation(MessageBodyObj messageBodyObj) {
        if (StringUtils.isAnyEmpty(messageBodyObj.getTenantId(), messageBodyObj.getDataId())) {
            return;
        }
        int tenantId = Integer.parseInt(messageBodyObj.getTenantId());
        String rebateObjId = messageBodyObj.getDataId();

        refreshPaymentObjRebateAmount(tenantId, rebateObjId);
    }

    public void refreshPaymentObjRebateAmount(int tenantId, String rebateObjId) {
        Map<String, Object> rechargePaymentObj;
        if (Objects.isNull(rechargePaymentObj = getRechargePaymentObj(tenantId, rebateObjId))) {
            return;
        }
        if (!isOrderGoodsMeetingRecharge(tenantId, rechargePaymentObj)) {
            return;
        }

        PaasDataIncrementUpdate.Arg updateArg = new PaasDataIncrementUpdate.Arg();
        updateArg.setData(rechargePaymentObj);
        PaasDataIncrementUpdate.Result updateResult = paasDataProxy.incrementUpdate(tenantId, -10000, PaymentApiNames.OBJECT_API_NAME, updateArg);
        if (updateResult.getErrCode() != 0) {
            log.info("update rebate_amount of PaymentObj failed: {}", updateResult.getErrMessage());
        }
    }

    private Map<String, Object> getRechargePaymentObj(int tenantId, String rebateObjId) {
        PaasDataFindById.Arg arg = new PaasDataFindById.Arg();
        arg.setDescribeApiName(RebateApiNames.OBJECT_API_NAME);
        arg.setDataId(rebateObjId);
        arg.setSelectFields(Lists.newArrayList(CommonApiNames.NAME, RebateApiNames.REBATE_SOURCE_API_NAME, RebateApiNames.REBATE_SOURCE_ID,
                RebateApiNames.SUM_AMOUNT));

        PaasDataFindById.Result result = paasDataProxy.findById(tenantId, -10000, arg);
        log.info("query RebateObj data result: {}", JSON.toJSONString(result));
        if (result.getCode() != 0) {
            return null;
        }
        JSONObject rebateObj = result.getData().getObjectData();
        if (MapUtils.isEmpty(rebateObj)) {
            log.info("no RebateObj data is found, id: {}", rebateObjId);
            return null;
        }

        if (!Objects.equals(rebateObj.getString(RebateApiNames.REBATE_SOURCE_API_NAME), PaymentApiNames.OBJECT_API_NAME) ||
                StringUtils.isEmpty(rebateObj.getString(RebateApiNames.REBATE_SOURCE_ID))) {
            log.info("no related PaymentObj data is found");
            return null;
        }
        return ImmutableMap.of(
                CommonApiNames.ID, rebateObj.getString(RebateApiNames.REBATE_SOURCE_ID),
                PaymentApiNames.REBATE_AMOUNT, rebateObj.getString(RebateApiNames.SUM_AMOUNT));
    }

    private boolean isOrderGoodsMeetingRecharge(int tenantId, Map<String, Object> rechargePaymentObj) {
        String paymentObjId = rechargePaymentObj.get(CommonApiNames.ID).toString();
        PaasDataFindById.Arg arg = new PaasDataFindById.Arg();
        arg.setDescribeApiName(PaymentApiNames.OBJECT_API_NAME);
        arg.setDataId(paymentObjId);
        arg.setSelectFields(Lists.newArrayList(CommonApiNames.NAME, PaymentApiNames.ORDER_GOODS_MEETING_FLAG, PaymentApiNames.ACTIVITY_ID,
                PaymentApiNames.REBATE_AMOUNT));

        PaasDataFindById.Result result = paasDataProxy.findById(tenantId, -10000, arg);
        log.info("query PaymentObj data result: {}", JSON.toJSONString(result));
        if (result.getCode() != 0) {
            return false;
        }
        JSONObject paymentObj = result.getData().getObjectData();
        if (MapUtils.isEmpty(paymentObj)) {
            log.info("no PaymentObj is found, id: {}", paymentObjId);
            return false;
        }

        if (!paymentObj.getBooleanValue(PaymentApiNames.ORDER_GOODS_MEETING_FLAG) || StringUtils.isEmpty(paymentObj.getString(PaymentApiNames.ACTIVITY_ID))) {
            log.info("It`s not orderGoodsMeeting recharge");
            return false;
        }
        BigDecimal rebateAmount = paymentObj.getBigDecimal(PaymentApiNames.REBATE_AMOUNT);
        Object actualRebateAmount = Optional.ofNullable(rechargePaymentObj.get(PaymentApiNames.REBATE_AMOUNT)).orElse("0");
        if (Objects.nonNull(rebateAmount) && rebateAmount.compareTo(new BigDecimal(actualRebateAmount.toString())) == 0) {
            log.info("need not to update rebate_amount of PaymentObj");
            return false;
        }
        log.info("need to update rebate_amount of PaymentObj, from {} to {}", rebateAmount, actualRebateAmount);
        return true;
    }
}
