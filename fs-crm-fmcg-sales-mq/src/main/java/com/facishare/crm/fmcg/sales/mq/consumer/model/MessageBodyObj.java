package com.facishare.crm.fmcg.sales.mq.consumer.model;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @create : 2023-09-23 10:28
 **/
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class MessageBodyObj {
    private JSONObject beforeTriggerData;
    private JSONObject afterTriggerData;
    private String dataId;
    private String apiName;
    private String eventId;
    private String msgId;
    private String triggerType;
    private String tenantId;
    private String userId;

    public MessageBodyObj(String tenantId, String userId, String msgId, JSONObject message) {
        this.beforeTriggerData = message.getJSONObject("beforeTriggerData");
        this.afterTriggerData = message.getJSONObject("afterTriggerData");
        this.dataId = message.getString("objectId");
        this.apiName = message.getString("entityId");
        this.eventId = message.getString("eventId");
        this.msgId = msgId;
        this.triggerType = message.getString("triggerType");
        this.tenantId = tenantId;
        this.userId = userId;
    }
}
