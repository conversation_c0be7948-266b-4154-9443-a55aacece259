package com.facishare.crm.fmcg.sales.mq.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.sales.apiname.AccountObjApiNames;
import com.facishare.crm.fmcg.sales.apiname.EnterpriseRelationObjApiNames;
import com.facishare.crm.fmcg.sales.mq.action.abstraction.IObjectDataAction;
import com.facishare.crm.fmcg.sales.mq.consumer.model.MessageBodyObj;
import com.facishare.crm.fmcg.sales.service.InterconnectionService;
import com.facishare.organization.paas.model.permission.PaasRoleDetail;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.PaasOrgProxy;
import com.fmcg.framework.http.PersonnelProxy;
import com.fmcg.framework.http.contract.paas.data.PaasBatchCreate;
import com.fmcg.framework.http.contract.paas.data.PaasDataCreate;
import com.fmcg.framework.http.contract.paas.data.PaasDataIncrementUpdate;
import com.fmcg.framework.http.contract.paas.data.PaasDataQueryWithFields;
import com.fmcg.framework.http.contract.paas.org.ListTenantGroupCategories;
import com.fmcg.framework.http.contract.paas.org.PaasOrgBase;
import com.fmcg.framework.http.contract.paas.org.UpsertTenantGroupData;
import com.fmcg.framework.http.contract.personnel.AsyncBulkResetPassword;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> liuhaoyu
 * @create : 2023-10-30 11:52
 **/
@Slf4j
@Component("enterpriseRelationObjAction")
public class EnterpriseRelationObjAction implements IObjectDataAction {
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private PersonnelProxy personnelProxy;
    @Resource
    private InterconnectionService interconnectionService;
    @Resource
    private PaasOrgProxy paasOrgProxy;
    @Resource
    private PaasDataProxy paasDataProxy;

    @Override
    public void action(MessageBodyObj messageBodyObj) {
        try {
            handleDownstreamData(messageBodyObj);
        } catch (Exception e) {
            log.error("handleDownstreamData exception ", e);
        }
    }

    private void handleDownstreamData(MessageBodyObj messageBodyObj) {
        String tenantId = messageBodyObj.getTenantId();
        if (!GrayRelease.isAllow("fmcg", "MQ_ENTERPRISE_RELATION_OBJ_ACTION_INIT_DATA", tenantId)) {
            return;
        }
        if (!"u".equals(messageBodyObj.getTriggerType()) || Objects.isNull(messageBodyObj.getAfterTriggerData())) {
            return;
        }
        log.info("messageBodyObj {}", JSON.toJSONString(messageBodyObj));

        JSONObject afterTriggerData = messageBodyObj.getAfterTriggerData();
        //监听复制状态和CRM开通状态
        if (!"2".equals(afterTriggerData.getString(EnterpriseRelationObjApiNames.COPY_STATUS))
                && !"2".equals(afterTriggerData.getString(EnterpriseRelationObjApiNames.CRM_OPEN_STATUS))) {
            return;
        }

        handleDownstreamData(tenantId, messageBodyObj.getDataId());
    }

    public void handleDownstreamData(String tenantId, String dataId) {
        IObjectData enterpriseRelationData = serviceFacade.findObjectData(User.systemUser(tenantId), dataId, EnterpriseRelationObjApiNames.OBJECT_API_NAME);
        if (Objects.isNull(enterpriseRelationData)) {
            log.info("not found enterpriseRelationData");
            return;
        }
        log.info("enterpriseRelationData {}", JSON.toJSONString(enterpriseRelationData));
        //模板企业开通的下游企业（复制成功+开通成功）
        //非模板企业开通的下游企业（开通成功）
        if (enterpriseRelationData.containsField(EnterpriseRelationObjApiNames.TEMPLATE_EA) && Objects.nonNull(enterpriseRelationData.get(EnterpriseRelationObjApiNames.TEMPLATE_EA))
                && !"2".equals(enterpriseRelationData.get(EnterpriseRelationObjApiNames.COPY_STATUS, String.class))) {
            return;
        }
        if (!"2".equals(enterpriseRelationData.get(EnterpriseRelationObjApiNames.CRM_OPEN_STATUS, String.class))) {
            return;
        }

        if (enterpriseRelationData.containsField(EnterpriseRelationObjApiNames.ENTERPRISE_ACCOUNT) && Objects.nonNull(enterpriseRelationData.get(EnterpriseRelationObjApiNames.ENTERPRISE_ACCOUNT))) {
            //查询客户数据
            String accountId = enterpriseRelationData.get(EnterpriseRelationObjApiNames.MAPPER_ACCOUNT_ID, String.class);
            IObjectData accountData = serviceFacade.findObjectData(User.systemUser(tenantId), accountId, AccountObjApiNames.OBJECT_API_NAME);
            //获取下游ei
            String downstreamEa = enterpriseRelationData.get(EnterpriseRelationObjApiNames.ENTERPRISE_ACCOUNT, String.class);
            int downstreamEi = eieaConverter.enterpriseAccountToId(downstreamEa);
            //拿到下游企业的CRM角色列表
            List<PaasRoleDetail> roleList = interconnectionService.getRoleList(downstreamEi, "CRM");
            Map<String, String> roleNameToRoleCode = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(roleList)) {
                for (PaasRoleDetail paasRoleDetail : roleList) {
                    roleNameToRoleCode.put(paasRoleDetail.getRoleName(), paasRoleDetail.getRoleCode());
                }
            }
            try {
                //给下游的1000添加权限
                interconnectionService.addRole(downstreamEi, Lists.newArrayList("1000"), roleNameToRoleCode, Lists.newArrayList("外勤人员", "总经理/主管/内勤"), "CRM");//ingoreI18n
            } catch (Exception e) {
                log.error("给下游的1000添加权限 exception", e);
            }
            try {
                //更新1000主角色
                interconnectionService.setDefaultRole(downstreamEi, roleNameToRoleCode, Lists.newArrayList("1000"), "总经理/主管/内勤");//ingoreI18n
            } catch (Exception e) {
                log.error("更新1000主角色 exception", e);
            }
            try {
                updateCompanyOwner(downstreamEi);
            } catch (Exception e) {
                log.error("更新999999部门负责人为1000 exception", e);
            }
            try {
                //更新1000的密码
                updatePersonnel(downstreamEi);
            } catch (Exception e) {
                log.error("更新1000的密码 exception", e);
            }
            try {
                //创建下游员工
                //interconnectionService.initPersonnel(tenantId, String.valueOf(downstreamEi), accountId, roleNameToRoleCode);
            } catch (Exception e) {
                log.error("创建下游员工 exception", e);
            }
            try {
                //创建本企业客户
                //createCurAccount(String.valueOf(downstreamEi), accountData);
                //创建本企业供应商
                createCurSupplier(String.valueOf(downstreamEi), accountData);
            } catch (Exception e) {
                log.error("创建本企业供应商 exception", e);
            }
            try {
                //添加自定义企业组
                addTenantGroup(tenantId, enterpriseRelationData.getId(), accountData);
            } catch (Exception e) {
                log.error("添加自定义企业组 exception", e);
            }
            try {
                //复制模版企业中的返利单规则
                copyRebateRuleObjData(enterpriseRelationData);
            } catch (Exception e) {
                log.error("复制模版企业中的返利单规则 exception", e);
            }
            try {
                //新增默认仓库
                addDefaultWarehouse(enterpriseRelationData);
            } catch (Exception e) {
                log.error("新增默认仓库 exception", e);
            }
        }
    }

    private void updateCompanyOwner(Integer tenantId) {
        PaasDataIncrementUpdate.Arg updateArg = new PaasDataIncrementUpdate.Arg();
        Map<String, Object> params = Maps.newHashMap();
        params.put("manager_id", Lists.newArrayList("1000"));
        params.put("_id", "999999");
        updateArg.setData(params);
        log.info("updateCompanyOwner arg {}", JSON.toJSONString(updateArg));
        PaasDataIncrementUpdate.Result result = paasDataProxy.incrementUpdate(tenantId, -10000, "DepartmentObj", updateArg);
        log.info("updateCompanyOwner result {}", JSON.toJSONString(result));
    }

    private void addDefaultWarehouse(IObjectData enterpriseRelationData) {
        int downstreamEi = eieaConverter.enterpriseAccountToId(enterpriseRelationData.get("enterprise_account", String.class));
        JSONObject createData = new JSONObject();
        createData.put("name", "常温正常品仓库");//ingoreI18n
        createData.put("is_default", true);
        createData.put("warehouse_type", "company");
        createData.put("number", "C1001");
        createData.put("warehouse_products_properties", "0");
        createData.put("is_enable", "1");
        createData.put("owner", Lists.newArrayList("-10000"));
        createData.put("dept_range", Lists.newArrayList("999999"));
        PaasDataCreate.Arg createArg = new PaasDataCreate.Arg();
        createArg.setObjectData(createData);
        log.info("addDefaultWarehouse createArg {}", JSON.toJSONString(createArg));
        PaasDataCreate.Result result = paasDataProxy.create(downstreamEi, -10000, "WarehouseObj", createArg);
        log.info("addDefaultWarehouse result {}", JSON.toJSONString(result));
    }

    private void copyRebateRuleObjData(IObjectData enterpriseRelationData) {
        int downstreamEi = eieaConverter.enterpriseAccountToId(enterpriseRelationData.get("enterprise_account", String.class));
        int templateEi = eieaConverter.enterpriseAccountToId(enterpriseRelationData.get("template_ea", String.class));

        PaasDataQueryWithFields.Arg arg = new PaasDataQueryWithFields.Arg();
        arg.setFieldList(Lists.newArrayList("name", "rule_type", "source_object_api_name", "rebate_condition", "priority", "product_range_type", "active_status", "rule_content"));
        PaasDataQueryWithFields.QueryDTO query = new PaasDataQueryWithFields.QueryDTO.Builder()
                .appendFilter("life_status", "EQ", "normal")
                .appendFilter("active_status", "EQ", "enable")
                .limit(100)
                .offset(0)
                .build();
        arg.setQueryString(JSON.toJSONString(query));
        log.info("findRebateRuleObj arg {}", JSON.toJSONString(arg));
        PaasDataQueryWithFields.Result result = paasDataProxy.queryWithFields(templateEi, -10000, "RebateRuleObj", arg);
        log.info("findRebateRuleObj result {}", JSON.toJSONString(result));
        if (result.getErrCode() != 0) {
            log.info("findRebateRuleObj is error {}, {}", result.getErrCode(), result.getErrMessage());
            return;
        }
        if (CollectionUtils.isEmpty(result.getResult().getQueryResult().getDataList())) {
            log.info("findRebateRuleObj is empty");
            return;
        }
        PaasBatchCreate.Result batchCreateResult = paasDataProxy.batchCreate(downstreamEi, -10000, "RebateRuleObj", result.getResult().getQueryResult().getDataList());
        log.info("copyRebateRuleObjData batchCreateResult {}", JSON.toJSONString(batchCreateResult));
    }

    private void addTenantGroup(String tenantId, String enterpriseRelationId, IObjectData accountData) {
        PaasOrgBase.Context context = new PaasOrgBase.Context(tenantId, "-10000");

        ListTenantGroupCategories.Arg queryArg = new ListTenantGroupCategories.Arg();
        queryArg.setContext(context);
        ListTenantGroupCategories.Result listTenantGroupCategoriesResult = paasOrgProxy.listTenantGroupCategories(Integer.valueOf(tenantId), -10000, queryArg);
        if (listTenantGroupCategoriesResult.getErrCode() != 0) {
            log.info("listTenantGroupCategories is error {}, {}, {}", listTenantGroupCategoriesResult.getErrCode(), listTenantGroupCategoriesResult.getErrMessage(), listTenantGroupCategoriesResult.getErrDescription());
            return;
        }
        if (CollectionUtils.isEmpty(listTenantGroupCategoriesResult.getResult())) {
            log.info("listTenantGroupCategoriesResult is empty");
            return;
        }

        Map<String, String> groupNameToGroupId = Maps.newHashMap();
        for (ListTenantGroupCategories.TenantGroupCategory tenantGroupCategory : listTenantGroupCategoriesResult.getResult()) {
            List<ListTenantGroupCategories.TenantGroup> tenantGroups = tenantGroupCategory.getTenantGroups();
            for (ListTenantGroupCategories.TenantGroup tenantGroup : tenantGroups) {
                groupNameToGroupId.put(tenantGroup.getName(), tenantGroup.getId());
            }
        }

        if (!accountData.containsField("tenant_group__c")) {
            log.info("tenant_group__c is null {}", accountData.getId());
            return;
        }
        String tenantGroupName = accountData.get("tenant_group__c", String.class);
        if (!groupNameToGroupId.containsKey(tenantGroupName)) {
            log.info("not found tenant group {}, {}", tenantGroupName, JSON.toJSONString(groupNameToGroupId));
            return;
        }
        String tenantGroupId = groupNameToGroupId.get(tenantGroupName);
        UpsertTenantGroupData.Arg updateArg = new UpsertTenantGroupData.Arg();
        updateArg.setContext(context);
        updateArg.setTenantGroupData(Lists.newArrayList(new UpsertTenantGroupData.TenantGroupData(enterpriseRelationId, tenantGroupId)));
        log.info("upsertTenantGroupData updateArg {}", JSON.toJSONString(updateArg));
        UpsertTenantGroupData.Result result = paasOrgProxy.upsertTenantGroupData(Integer.valueOf(tenantId), -10000, updateArg);
        log.info("upsertTenantGroupData result {}", JSON.toJSONString(result));
    }

    private void updatePersonnel(Integer downstreamEi) {
        AsyncBulkResetPassword.Arg arg = new AsyncBulkResetPassword.Arg();
        arg.setDataIds(Lists.newArrayList("1000"));
        arg.setEncryptionPassword("HdJSBgFomolWZzSVC8roychV0I0S73oS/Wmnp9spFENgwB2iauqOhSgoWz1+drmaSIG21CVCoZpTfANEwucaszBsyBCh7NkE8La028HvYdCm3QnjWTsGJRAx6qWtmvb/alL4do+BlCCQWPP7qMehqawpZAIu/i+9IVk8NPsfVAQ=");
        arg.setPublicKey("MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCROXqyCKxG8DrQKvrmdwiAHFJseaLHKsdzJ+61EpEGUawyLk5obn2Z2lyVVGjqT3KECk3DJtAD6Jux/m/gW2/lxspvhUO1YE1P8OZuUq5xhr/3AWuSSXCqLM2q6TEMnI2VE1BzlsRcxQVGVd4kGszzpyLXYS9ubFTTp1C2A+uZ1QIDAQAB");
        AsyncBulkResetPassword.Result result = personnelProxy.asyncBulkResetPassword(downstreamEi, -10000, arg);
        log.info("updatePersonnel password {}, {}", downstreamEi, JSON.toJSONString(result));
    }

    private void createCurSupplier(String tenantId, IObjectData accountData) {
        IObjectData objectData = new ObjectData();
        objectData.setName(accountData.getName());
        objectData.setOwner(com.google.common.collect.Lists.newArrayList("-10000"));
        objectData.setTenantId(tenantId);
        objectData.setRecordType("default__c");
        objectData.setDescribeApiName("SupplierObj");
        objectData.set("is_enable", "1");
        objectData.set("isOurEnterprise__c", "YES");
        log.info("createCurSupplier arg {}, {}", tenantId, JSON.toJSONString(objectData));
        IObjectData result = serviceFacade.saveObjectData(User.systemUser(tenantId), objectData);
        log.info("createCurSupplier result {}, {}", tenantId, JSON.toJSONString(result));
    }

    private void createCurAccount(String tenantId, IObjectData accountData) {
        IObjectData objectData = new ObjectData();
        objectData.setId(accountData.getId());
        objectData.setOwner(Lists.newArrayList("-10000"));
        objectData.setDescribeApiName(AccountObjApiNames.OBJECT_API_NAME);
        objectData.setTenantId(tenantId);
        objectData.setRecordType(accountData.getRecordType());
        objectData.setName(accountData.getName());
        objectData.set("isOurEnterprise__c", "YES");
        log.info("createCurAccount arg {}, {}", tenantId, JSON.toJSONString(objectData));
        IObjectData result = serviceFacade.saveObjectData(User.systemUser(tenantId), objectData);
        log.info("createCurAccount result {}, {}", tenantId, JSON.toJSONString(result));
    }
}
