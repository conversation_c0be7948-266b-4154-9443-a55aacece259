package com.facishare.crm.fmcg.sales.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.sales.mq.consumer.model.MessageBodyObj;
import com.facishare.crm.fmcg.sales.mq.consumer.model.TriggerMessageObj;
import com.facishare.crm.fmcg.sales.mq.errors.AbandonActionException;
import com.facishare.crm.fmcg.sales.mq.factory.ObjectDataActionFactory;
import com.facishare.crm.fmcg.sales.mq.service.FMCGSnActionService;
import com.facishare.paas.appframework.core.exception.ObjectDataNotFoundException;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.Objects;

@Slf4j
@Component
@SuppressWarnings("Duplicates")
public class ObjectDataMqConsumer implements ApplicationListener<ContextRefreshedEvent> {
    private static final String CONFIG_NAME = "fs-fmcg-object-mq";
    private static final String NAME_SERVER_KEY = "OBJECT_V2_NAMESERVER";
    private static final String TOPIC_KEY = "OBJECT_V2_TOPICS";
    private static final String GROUP_KEY = "OBJECT_V2_GROUP_CONSUMER_CRM_SALES";

    private AutoConfMQPushConsumer consumer;

    @Resource
    private FMCGSnActionService fmcgSnActionService;

    @PostConstruct
    public void init() {
        log.info("object-data consumer start init.");

        MessageListenerConcurrently listener = (messages, context) -> {
            for (MessageExt message : messages) {
                try {
                    this.process(message);
                } catch (AbandonActionException ex) {
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                } catch (Exception ex) {
                    log.error("mq exception : ", ex);
                    return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        };
        try {
            this.consumer = new AutoConfMQPushConsumer(CONFIG_NAME, listener);
            this.consumer.setGroupNameKey(GROUP_KEY);
            this.consumer.setConsumeTopicKey(TOPIC_KEY);
            this.consumer.setNameServerKey(NAME_SERVER_KEY);

        } catch (Exception ex) {
            log.error("init object-data consumer error : ", ex);
        }
    }

    private void process(MessageExt message) {
        TriggerMessageObj data = JSON.parseObject(new String(message.getBody()), TriggerMessageObj.class);
        for (JSONObject object : data.getBody()) {
            String apiName = object.getString("entityId");
            String objectId = object.getString("objectId");

            TraceContext context = TraceContext.get();
            if (Strings.isNullOrEmpty(context.getTraceId())) {
                context.setTraceId(String.format("CRM_SALES_OBJECT_MQ.%s.%s.%s.%s.%s", data.getTenantId(), apiName, objectId, message.getMsgId(), message.getReconsumeTimes()));
            }

            try {
                MessageBodyObj messageBodyObj = new MessageBodyObj(data.getTenantId(), data.getUserId(), message.getMsgId(), object);
                try {
                    fmcgSnActionService.recordSnStatusByAction(messageBodyObj);
                } catch (ObjectDataNotFoundException e) {
                    log.info(String.format("recordSnStatusByAction objectDataNotFound : %s, %s, %s, %s", data.getTenantId(), apiName, objectId, message.getMsgId()), e);
                } catch (Exception e) {
                    log.error(String.format("recordSnStatusByAction exception : %s, %s, %s, %s", data.getTenantId(), apiName, objectId, message.getMsgId()), e);
                }
                if (ObjectDataActionFactory.processedObject(messageBodyObj.getApiName())) {
                    ObjectDataActionFactory.getBeanByApiName(messageBodyObj.getApiName()).action(messageBodyObj);
                }
            } finally {
                TraceContext.remove();
            }
        }
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (Objects.nonNull(this.consumer) && event.getApplicationContext().getParent() == null) {
            this.consumer.start();
            log.info("object-data consumer started.");
        }
    }

    @PreDestroy
    public void shutDown() {
        if (Objects.nonNull(consumer)) {
            this.consumer.close();
            log.info("object-data consumer closed.");
        }
    }
}