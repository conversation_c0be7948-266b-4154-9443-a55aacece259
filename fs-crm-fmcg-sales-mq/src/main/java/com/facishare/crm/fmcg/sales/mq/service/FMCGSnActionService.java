package com.facishare.crm.fmcg.sales.mq.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.sales.apiname.CommonApiNames;
import com.facishare.crm.fmcg.sales.apiname.FMCGSerialNumberStatusApiNames;
import com.facishare.crm.fmcg.sales.business.InterconnectionBusiness;
import com.facishare.crm.fmcg.sales.cache.DataLevelI18NTranslateCache;
import com.facishare.crm.fmcg.sales.cache.DataLevelI18nFieldSupportLanguagesCache;
import com.facishare.crm.fmcg.sales.cache.FMCGSNOpenStatusCache;
import com.facishare.crm.fmcg.sales.cache.FMCGSerialNumberActionCache;
import com.facishare.crm.fmcg.sales.dao.mongo.entity.ConditionEntity;
import com.facishare.crm.fmcg.sales.dao.mongo.entity.ConditionReturnEntity;
import com.facishare.crm.fmcg.sales.dao.mongo.entity.FMCGSerialNumberActionEntity;
import com.facishare.crm.fmcg.sales.dao.mongo.entity.StorageLocationEntity;
import com.facishare.crm.fmcg.sales.model.sn.FMCGSerialNumberQueryOpenStatus;
import com.facishare.crm.fmcg.sales.mq.consumer.model.MessageBodyObj;
import com.facishare.organization.paas.model.PaaSResult;
import com.facishare.organization.paas.model.permission.GetMultiEmployeeRoleRelationEntitiesByEmployeeIDs;
import com.facishare.organization.paas.model.permission.RoleListDto;
import com.facishare.organization.paas.model.permission.RoleRelationEntityDto;
import com.facishare.organization.paas.service.PaaSPermissionService;
import com.facishare.organization.paas.util.PaasArgumentUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.ActionContextUtil;
import com.facishare.uc.api.model.enterprise.arg.GetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetSimpleEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.common.release.GrayRelease;
import com.fxiaoke.enterpriserelation2.arg.ListAppOuterRolesByAppIdArg;
import com.fxiaoke.enterpriserelation2.arg.ListUserOuterRolesByAppIdArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.data.RoleInfoData;
import com.fxiaoke.enterpriserelation2.result.data.UserRoleInfoData;
import com.fxiaoke.enterpriserelation2.service.AppOuterRoleService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> liuhaoyu
 * @create : 2023-09-19 10:08
 **/
@Service
@Slf4j
public class FMCGSnActionService {
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private PaaSPermissionService paaSPermissionService;
    @Resource
    private FMCGSerialNumberActionCache fmcgSerialNumberActionCache;
    @Resource
    private EnterpriseEditionService enterpriseEditionService;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private InterconnectionBusiness interconnectionBusiness;
    @Resource
    private FMCGSNOpenStatusCache fmcgsnOpenStatusCache;
    @Autowired
    private AppOuterRoleService appOuterRoleService;
    @Resource
    private DataLevelI18nFieldSupportLanguagesCache dataLevelI18nFieldSupportLanguagesCache;
    @Resource
    private DataLevelI18NTranslateCache dataLevelI18NTranslateCache;

    public void recordSnStatusByAction(MessageBodyObj messageBodyObj) {
        if (!GrayRelease.isAllow("fmcg", "OPEN_RECORD_FMCG_SERIAL_NUMBER_STATUS", messageBodyObj.getTenantId())) {
            return;
        }
        FMCGSerialNumberQueryOpenStatus.Result openStatus = fmcgsnOpenStatusCache.getNoException(messageBodyObj.getTenantId());

        //upstreamTenantId目前使用地方  1、查询actions 2、处理渠道类型（查询业务类型）3、save码状态 4、数据级多语
        String upstreamTenantId = openStatus.getUpstreamTenantId();
        if (!Objects.equals(1, openStatus.getValue())) {
            return;
        }
        if (Strings.isNullOrEmpty(upstreamTenantId)) {
            upstreamTenantId = messageBodyObj.getTenantId();
        }
        List<FMCGSerialNumberActionEntity> actions = fmcgSerialNumberActionCache.getNoException(upstreamTenantId);
        if (CollectionUtils.isEmpty(actions)) {
            log.info("not found actions for {}", upstreamTenantId);
            return;
        }
        matchAction(messageBodyObj, actions, upstreamTenantId);
    }

    private void matchAction(MessageBodyObj messageBodyObj, List<FMCGSerialNumberActionEntity> actions, String upstreamTenantId) {
        String apiName = messageBodyObj.getApiName();
        IObjectData objectData = null;
        IObjectDescribe describe = null;
        String triggerType = messageBodyObj.getTriggerType();
        for (FMCGSerialNumberActionEntity action : actions) {
            if (!Objects.equals(1, action.getActionStatus())
                    || !apiName.equals(action.getTriggerObject()) || !triggerType.equals(action.getTriggerAction())) {
                continue;
            }
            if (Objects.isNull(objectData)) {
                objectData = serviceFacade.findObjectData(User.systemUser(messageBodyObj.getTenantId()), messageBodyObj.getDataId(), apiName);
            }
            if (Objects.isNull(describe)) {
                describe = serviceFacade.findObject(messageBodyObj.getTenantId(), apiName);
            }
            boolean isMatch = false;
            try {
                isMatch = matchConditions(action, messageBodyObj, objectData, describe);
            } catch (Exception e) {
                log.error("matchAction matchConditions is error", e);
                log.info("matchAction matchConditions is error, {}, {}", action.getId(), action.getActionName());
            }
            if (isMatch) {
                log.info("matchAction action, {}, {}", action.getId(), action.getActionName());
                recordSnStatus(messageBodyObj.getTenantId(), action, objectData, describe, upstreamTenantId);
                return;
            }
        }
    }

    private void recordSnStatus(String tenantId, FMCGSerialNumberActionEntity action, IObjectData objectData, IObjectDescribe describe, String upstreamTenantId) {
        if (!Strings.isNullOrEmpty(action.getDesignatedTenantIdField())) {
            if (!objectData.containsField(action.getDesignatedTenantIdField()) || Objects.isNull(objectData.get(action.getDesignatedTenantIdField()))) {
                log.info("designatedTenantIdField value is null");
                return;
            }
            tenantId = objectData.get(action.getDesignatedTenantIdField(), String.class);
        }

        Map<String, Object> common = Maps.newHashMap();
        common.put("node_name_id", action.getActionName());
        common.put("action_id", action.getId());
        common.put("current_state", action.getUpdateStatus());
        common.put("node_type", action.getActionType());
        common.put("access_type", action.getAccessType());
        common.put("business_object_name", Objects.nonNull(describe) ? describe.getDisplayName() : null);
        common.put("business_object", action.getTriggerObject());
        common.put("business_object_number", objectData.getName());
        common.put("business_object_id", objectData.getId());
        common.put("business_occurrence_time", objectData.getCreateTime());
        if (!Strings.isNullOrEmpty(action.getBusinessOccurrenceTimeField()) && Objects.nonNull(objectData.get(action.getBusinessOccurrenceTimeField()))) {
            common.put("business_occurrence_time", objectData.get(action.getBusinessOccurrenceTimeField(), Long.class));
        }
        common.put("current_tenant_name", getEnterpriseData(Integer.valueOf(tenantId)).getEnterpriseName());
        common.put("current_tenant_id", tenantId);

        try {
            handleChannelType(tenantId, common, action, upstreamTenantId);
        } catch (Exception e) {
            log.error("recordSnStatus handleChannelType is error", e);
        }
        try {
            handleWarehouseField(tenantId, action, objectData, describe, common);
        } catch (Exception e) {
            log.error("recordSnStatus handleWarehouseField is error", e);
        }
        try {
            handleAccountField(tenantId, action, objectData, common);
        } catch (Exception e) {
            log.error("recordSnStatus handleAccountField is error", e);
        }
        try {
            handPersonnelField(action, tenantId, objectData, describe, common);
        } catch (Exception e) {
            log.error("recordSnStatus handPersonnelField is error", e);
        }
        log.info("recordSnStatus common {}, {}, {}, {}", tenantId, objectData.getDescribeApiName(), objectData.getId(), JSON.toJSONString(common));
        generateAndRecordNewSnStatusData(tenantId, upstreamTenantId, action, objectData, common);
    }

    private void generateAndRecordNewSnStatusData(String tenantId, String upstreamTenantId, FMCGSerialNumberActionEntity action, IObjectData objectData, Map<String, Object> common) {
        List<StorageLocationEntity> snStorageLocations = action.getSnStorageLocations();
        if (CollectionUtils.isEmpty(snStorageLocations)) {
            return;
        }
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        Map<String, String> statusAbnormalInfo = Maps.newHashMap();
        try {
            statusAbnormalInfo = collectStatusAbnormalInfo(tenantId, action, objectData, describeMap);
        } catch (Exception e) {
            log.error("collectStatusAbnormalInfo is error ", e);
        }
        List<IObjectData> objectList = Lists.newArrayList();
        for (StorageLocationEntity snStorageLocation : snStorageLocations) {
            List<IObjectData> details = findObjectsByLocation(tenantId, snStorageLocation, objectData, describeMap);
            if (CollectionUtils.isEmpty(details)) {
                continue;
            }
            String storageField = snStorageLocation.getStorageField();
            for (IObjectData detail : details) {
                List<String> snIds = Lists.newArrayList();
                if ("array".equals(snStorageLocation.getStorageType()) && CollectionUtils.isNotEmpty(detail.get(storageField, List.class))) {
                    snIds = JSON.parseObject(JSON.toJSONString(detail.get(storageField)), new TypeReference<List<String>>() {
                    });
                } else if ("string".equals(snStorageLocation.getStorageType()) && !Strings.isNullOrEmpty(detail.get(storageField, String.class))) {
                    snIds = Lists.newArrayList(detail.get(storageField, String.class));
                }
                if (CollectionUtils.isEmpty(snIds)) {
                    continue;
                }
                log.info("recordSnStatus snIds {}", JSON.toJSONString(snIds));
                String productId = detail.get(snStorageLocation.getProductField(), String.class);
                List<IObjectData> snStatusData = generateNewSnStatusData(tenantId, snIds, productId, common, statusAbnormalInfo, upstreamTenantId);
                objectList.addAll(snStatusData);
            }
        }
        if (CollectionUtils.isEmpty(objectList)) {
            return;
        }
        //fill statusAbnormalInfo 可以移到这里
        try {
            fillDataLevelI18N(upstreamTenantId, tenantId, objectList);
        } catch (Exception e) {
            log.error("data level i18n fill failure");
        }

        List<IObjectData> snStatus = serviceFacade.bulkSaveObjectData(objectList, User.systemUser(upstreamTenantId));
        log.info("recordSnStatus size : {}", snStatus.size());
    }

    /**
     * 现在只支持单/多行文本
     */
    private void fillDataLevelI18N(String upstreamTenantId, String tenantId, List<IObjectData> objectList) {
        if (!GrayRelease.isAllow("fmcg", "fmcg_sn_data_level_i18n_gray", tenantId)) {
            return;
        }
        Map<String, List<String>> supportLanguages = dataLevelI18nFieldSupportLanguagesCache.getNoException(upstreamTenantId, FMCGSerialNumberStatusApiNames.OBJECT_API_NAME);
        if (supportLanguages == null || supportLanguages.isEmpty()) {
            return;
        }
        Set<String> fields = supportLanguages.keySet();
        for (IObjectData objectData : objectList) {
            for (String fieldApiName : fields) {
                if (Objects.isNull(objectData.get(fieldApiName))) {
                    continue;
                }
                JSONObject translate = dataLevelI18NTranslateCache.getNoException(upstreamTenantId, FMCGSerialNumberStatusApiNames.OBJECT_API_NAME, fieldApiName, objectData.get(fieldApiName, String.class));
                if (Objects.isNull(translate)) {
                    continue;
                }
                objectData.set(String.format("%s%s", fieldApiName, "__lang"), translate);
            }
        }
    }

    private Map<String, String> collectStatusAbnormalInfo(String tenantId, FMCGSerialNumberActionEntity action, IObjectData objectData, Map<String, IObjectDescribe> describeMap) {
        Map<String, String> statusAbnormalInfo = Maps.newHashMap();
        List<StorageLocationEntity> abnormalStorageLocations = action.getAbnormalStorageLocations();
        if (CollectionUtils.isEmpty(abnormalStorageLocations)) {
            return statusAbnormalInfo;
        }
        for (StorageLocationEntity abnormalStorageLocation : abnormalStorageLocations) {
            List<IObjectData> details = findObjectsByLocation(tenantId, abnormalStorageLocation, objectData, describeMap);
            if (CollectionUtils.isEmpty(details)) {
                continue;
            }
            String storageField = abnormalStorageLocation.getStorageField();
            for (IObjectData detail : details) {
                if (!detail.containsField(storageField) || Objects.isNull(detail.get(storageField))) {
                    continue;
                }
                if ("map".equals(abnormalStorageLocation.getStorageType())) {
                    Map<String, String> info = JSON.parseObject(detail.get(storageField, String.class), new TypeReference<Map<String, String>>() {
                    });
                    statusAbnormalInfo.putAll(info);
                }
            }
        }
        return statusAbnormalInfo;
    }

    private void handleWarehouseField(String tenantId, FMCGSerialNumberActionEntity action, IObjectData objectData, IObjectDescribe describe, Map<String, Object> common) {
        if (Strings.isNullOrEmpty(action.getWarehouseField())) {
            return;
        }
        Object warehouseId = handleFindAssociationValue(tenantId, objectData, describe, action.getWarehouseField());
        String warehouseIdStr = String.valueOf(warehouseId);
        IObjectData warehouseObj = serviceFacade.findObjectData(User.systemUser(tenantId), warehouseIdStr, "WarehouseObj");
        common.put("warehouse_name", Objects.nonNull(warehouseObj) ? warehouseObj.getName() : null);
        common.put("warehouse_id", warehouseIdStr);
    }

    private void handPersonnelField(FMCGSerialNumberActionEntity action, String tenantId, IObjectData objectData, IObjectDescribe describe, Map<String, Object> common) {
        String personalField = action.getPersonalField();
        List<ConditionReturnEntity> conditionReturnEntityList = getConditionReturnEntityList(action, FMCGSerialNumberActionEntity.F_PERSONAL_FIELD);
        Object personalFieldValue = null;
        String personalObjApiName = null;
        if (CollectionUtils.isNotEmpty(conditionReturnEntityList)) {
            ConditionReturnEntity entity = matchConditionReturnEntity(conditionReturnEntityList, tenantId, objectData, describe);
            if (Objects.isNull(entity)) {
                return;
            }
            personalFieldValue = handleConditionReturnValue(entity, tenantId, objectData, describe);
            if ("field".equals(entity.getReturnValueType())) {
                personalObjApiName = handleFindAssociationObjApiName(describe, String.valueOf(entity.getReturnValue()));
            }
        } else if (!Strings.isNullOrEmpty(personalField)) {
            personalFieldValue = handleFindAssociationValue(tenantId, objectData, describe, personalField);
            if (!Strings.isNullOrEmpty(action.getPersonalObjApiName())) {
                personalObjApiName = action.getPersonalObjApiName();
            } else if (!Strings.isNullOrEmpty(action.getDesignatedPersonalObjApiNameField())) {
                if (Objects.isNull(objectData.get(action.getDesignatedPersonalObjApiNameField()))) {
                    return;
                }
                personalObjApiName = objectData.get(action.getDesignatedPersonalObjApiNameField(), String.class);
            }
        }

        if (Objects.isNull(personalFieldValue) || Strings.isNullOrEmpty(personalObjApiName)) {
            return;
        }

        common.put(FMCGSerialNumberStatusApiNames.PERSONNEL_ID, personalFieldValue);
        common.put(FMCGSerialNumberStatusApiNames.PERSONNEL_OBJECT_API_NAME, personalObjApiName);
        if ("PersonnelObj".equals(personalObjApiName)) {
            handlePersonnelInfo(tenantId, String.valueOf(personalFieldValue), common);
        } else if ("ContactObj".equals(personalObjApiName)) {
            handleContactInfo(tenantId, String.valueOf(personalFieldValue), common);
        } else if ("PublicEmployeeObj".equals(personalObjApiName)) {
            handlePublicEmployeeObj(tenantId, String.valueOf(personalFieldValue), common);
        }
    }

    private Map getFieldInfoByDescribe(IObjectDescribe describe, String fieldApiName) {
        Map fields = describe.get("fields", Map.class);
        if (!fields.containsKey(fieldApiName)) {
            log.info("This field does not exist {}", fieldApiName);
            return null;
        }
        Object fieldInfo = fields.get(fieldApiName);
        return JSONObject.parseObject(JSONObject.toJSONString(fieldInfo), Map.class);
    }

    private Object handleConditionReturnValue(ConditionReturnEntity entity, String tenantId, IObjectData objectData, IObjectDescribe describe) {
        if (Objects.isNull(entity)) {
            return null;
        }
        Object returnValue = entity.getReturnValue();
        String returnValueType = entity.getReturnValueType();
        if ("fixed".equals(returnValueType)) {
            return returnValue;
        } else if ("field".equals(returnValueType)) {
            return handleFindAssociationValue(tenantId, objectData, describe, String.valueOf(returnValue));
        }
        return null;
    }

    private ConditionReturnEntity matchConditionReturnEntity(List<ConditionReturnEntity> conditionReturnEntityList, String tenantId, IObjectData objectData, IObjectDescribe describe) {
        if (Objects.isNull(objectData)) {
            return null;
        }
        for (ConditionReturnEntity entity : conditionReturnEntityList) {
            //todo: lhy 无conditionPattern的应该直接匹配成功
            if (Strings.isNullOrEmpty(entity.getConditionPattern())) {
                continue;
            }
            List<ConditionEntity> conditions = entity.getConditions();
            //按动作中的rowNo映射，对应行所需的条件
            Map<Integer, ConditionEntity> rowNoConditionMap = conditions.stream()
                    .collect(Collectors.toMap(ConditionEntity::getRowNo, o -> o));

            String[] ors = entity.getConditionPattern().split("or");
            for (String or : ors) {
                or = or.trim();
                or = or.substring(1, or.length() - 1);
                List<Integer> and = Stream.of(or.split("and"))
                        .map(o -> Integer.valueOf(o.trim()))
                        .collect(Collectors.toList());
                boolean isMatch = matchCreateCondition(tenantId, and, objectData, describe, rowNoConditionMap);
                if (isMatch) {
                    return entity;
                }
            }
        }
        return null;
    }

    private List<ConditionReturnEntity> getConditionReturnEntityList(FMCGSerialNumberActionEntity action, String key) {
        Map<String, List<ConditionReturnEntity>> fieldStorageConditions = action.getFieldStorageConditions();
        if (fieldStorageConditions == null) {
            return null;
        }
        return fieldStorageConditions.get(key);
    }

    private void handlePublicEmployeeObj(String tenantId, String personalFieldValue, Map<String, Object> common) {
        IObjectData publicEmployeeObj = serviceFacade.findObjectData(User.systemUser(tenantId), personalFieldValue, "PublicEmployeeObj");
        common.put(FMCGSerialNumberStatusApiNames.PERSONNEL_NAME, publicEmployeeObj.getName());
        String outerTenantId = publicEmployeeObj.get("outer_tenant_id", String.class);
        if (Strings.isNullOrEmpty(outerTenantId)) {
            return;
        }
        UserRoleInfoData mainRole = getMainRole(Integer.parseInt(tenantId), Long.parseLong(outerTenantId), Long.parseLong(personalFieldValue));
        if (Objects.isNull(mainRole)) {
            log.info("handlePublicEmployeeObj mainRoleCode is null outerTenantId {} personalFieldValue {}", outerTenantId, personalFieldValue);
            return;
        }
        common.put(FMCGSerialNumberStatusApiNames.PERSONNEL_HOST_ROLE, mainRole.getRoleCode());
        RoleInfoData roleInfoByRoleCode = getRoleInfoByRoleCode(Integer.valueOf(tenantId), mainRole.getRoleCode());
        if (Objects.isNull(roleInfoByRoleCode)) {
            log.info("handlePublicEmployeeObj roleInfoByRoleCode is null roleCode {}", mainRole.getRoleCode());
            return;
        }
        common.put(FMCGSerialNumberStatusApiNames.PERSONNEL_HOST_ROLE_NAME, roleInfoByRoleCode.getRoleName());
    }

    private UserRoleInfoData getMainRole(Integer tenantId, Long outerTenantId, Long outerUid) {
        RestResult<List<UserRoleInfoData>> result = listUserOuterRolesByAppId(tenantId, outerTenantId, outerUid);
        if (!result.isSuccess()) {
            log.info("getMainRoleId error : {}, {}", result.getErrCode(), result.getErrMsg());
            return null;
        }
        if (CollectionUtils.isEmpty(result.getData())) {
            log.info("getMainRoleId data is empty : {}", JSON.toJSONString(result));
            return null;
        }
        List<UserRoleInfoData> collect = result.getData().stream()
                .filter(UserRoleInfoData::getMajorRole)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            log.info("getMainRoleId collect is empty : {}", JSON.toJSONString(result));
            return null;
        }
        return collect.get(0);
    }

    private RestResult<List<UserRoleInfoData>> listUserOuterRolesByAppId(Integer tenantId, Long outerTenantId, Long outerUid) {
        HeaderObj headerObj = HeaderObj.newInstance(tenantId);
        ListUserOuterRolesByAppIdArg arg = new ListUserOuterRolesByAppIdArg();
        arg.setTenantId(tenantId);
        arg.setOuterTenantId(outerTenantId);
        arg.setOuterUid(outerUid);
        return appOuterRoleService.listUserOuterRolesByAppId(headerObj, arg);
    }

    private RoleInfoData getRoleInfoByRoleCode(Integer tenantId, String roleCode) {
        RestResult<List<RoleInfoData>> result = listAppOuterRolesByAppId(tenantId);
        if (!result.isSuccess()) {
            log.info("getRoleInfoByRoleCode error : {}, {}", result.getErrCode(), result.getErrMsg());
            return null;
        }
        if (CollectionUtils.isEmpty(result.getData())) {
            log.info("getRoleInfoByRoleCode data is empty : {}", JSON.toJSONString(result));
            return null;
        }
        List<RoleInfoData> collect = result.getData().stream()
                .filter(roleInfoData -> roleInfoData.getRoleCode().equals(roleCode))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            log.info("getRoleInfoByRoleCode collect is empty : {}", JSON.toJSONString(result));
            return null;
        }
        return collect.get(0);
    }

    private RestResult<List<RoleInfoData>> listAppOuterRolesByAppId(Integer tenantId) {
        HeaderObj headerObj = HeaderObj.newInstance(tenantId);
        ListAppOuterRolesByAppIdArg arg = new ListAppOuterRolesByAppIdArg();
        arg.setTenantId(tenantId);
        return appOuterRoleService.listAppOuterRolesByAppId(headerObj, arg);
    }

    private void handleContactInfo(String tenantId, String personalFieldValue, Map<String, Object> common) {
        IObjectData contactObj = serviceFacade.findObjectData(User.systemUser(tenantId), personalFieldValue, "ContactObj");
        common.put(FMCGSerialNumberStatusApiNames.PERSONNEL_NAME, contactObj.getName());
        IObjectDescribe contactObjDescribe = serviceFacade.findObject(tenantId, "ContactObj");
        Map fields = contactObjDescribe.get("fields", Map.class);
        if (fields.containsKey("field_2m89p__c")) {
            Object contactTypeInfo = fields.get("field_2m89p__c");
            Map contactTypeInfoMap = JSONObject.parseObject(JSONObject.toJSONString(contactTypeInfo), Map.class);
            Object options = contactTypeInfoMap.get("options");
            JSONArray optionsArray = JSON.parseArray(JSON.toJSONString(options));
            Map<String, String> optionsMap = Maps.newHashMap();
            for (Object o : optionsArray) {
                JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(o));
                optionsMap.put(jsonObject.getString("value"), jsonObject.getString("label"));
            }
            common.put(FMCGSerialNumberStatusApiNames.PERSONNEL_HOST_ROLE, contactObj.get("field_2m89p__c", String.class));
            common.put(FMCGSerialNumberStatusApiNames.PERSONNEL_HOST_ROLE_NAME, optionsMap.get(contactObj.get("field_2m89p__c", String.class)));
        }
    }

    //todo: lhy 使用新的匹配方式 sn status channel type ref
    private void handleChannelType(String tenantId, Map<String, Object> common, FMCGSerialNumberActionEntity action, String upstreamTenantId) {
        List<Integer> channelType = action.getChannelType();
        if (CollectionUtils.isEmpty(channelType)) {
            return;
        }
        if (channelType.size() == 1) {
            common.put("channel_type", channelType.get(0));
        } else {
            IObjectData accountInfoInUpstream = interconnectionBusiness.getAccountInfoInUpstream(tenantId, upstreamTenantId);
            if (Objects.isNull(accountInfoInUpstream)) {
                return;
            }
            String recordType = String.valueOf(accountInfoInUpstream.get(CommonApiNames.RECORD_TYPE));
            if ("dealer__c".equals(recordType)) {
                common.put("channel_type", 0);
            } else if ("secondary_dealer__c".equals(recordType)) {
                common.put("channel_type", 1);
            } else if ("mollercular_company__c".equals(recordType)) {
                common.put("channel_type", 4);
            }
        }
    }

    private List<IObjectData> generateNewSnStatusData(String tenantId, List<String> snIds, String productId, Map<String, Object> common, Map<String, String> statusAbnormalInfo, String upstreamTenantId) {
        List<IObjectData> snStatusData = Lists.newArrayList();
        for (String snId : snIds) {
            IObjectData objectData = new ObjectData(new HashMap(common));
            objectData.setOwner(Lists.newArrayList("-10000"));
            objectData.setDescribeApiName(FMCGSerialNumberStatusApiNames.OBJECT_API_NAME);
            objectData.setTenantId(upstreamTenantId);
            objectData.setIsPublic(true);
            objectData.setRecordType("default__c");
            objectData.set(FMCGSerialNumberStatusApiNames.FMCG_SERIAL_NUMBER_ID, snId);
            if (statusAbnormalInfo.containsKey(snId)) {
                objectData.set(FMCGSerialNumberStatusApiNames.WHETHER_ABNORMAL, true);
                objectData.set(FMCGSerialNumberStatusApiNames.EXCEPTION_TYPE, statusAbnormalInfo.get(snId));
            }
            //objectData.set(FMCGSerialNumberStatusApiNames.PRODUCT_ID, productId);
            snStatusData.add(objectData);
        }
        return snStatusData;
    }

    /**
     * 记录流转下一家企业的信息
     */
    private void handleAccountField(String tenantId, FMCGSerialNumberActionEntity action, IObjectData objectData, Map<String, Object> common) {
        if (Strings.isNullOrEmpty(action.getAccountField()) || Objects.isNull(objectData.get(action.getAccountField()))) {
            return;
        }
        String accountId = objectData.get(action.getAccountField(), String.class);
        common.put("account_id", accountId);
        if (Strings.isNullOrEmpty(accountId)) {
            return;
        }
        IFilter accountFilter = new Filter();
        accountFilter.setFieldName("mapper_account_id");
        accountFilter.setOperator(Operator.EQ);
        accountFilter.setFieldValues(Lists.newArrayList(accountId));
        List<IObjectData> outTenantInfos = findObjectsByFilter(tenantId, "EnterpriseRelationObj", Lists.newArrayList(accountFilter), Lists.newArrayList());
        if (CollectionUtils.isEmpty(outTenantInfos)) {
            log.info("not found EnterpriseRelationObj account {}, {}", tenantId, accountId);
            return;
        }
        IObjectData outTenantInfo = outTenantInfos.get(0);
        if (Objects.isNull(outTenantInfo) || !outTenantInfo.containsField("enterprise_account")) {
            return;
        }
        if (Objects.isNull(outTenantInfo.get("enterprise_account")) || "null".equals(String.valueOf(outTenantInfo.get("enterprise_account")))) {
            return;
        }
        int outTenantEi = eieaConverter.enterpriseAccountToId(String.valueOf(outTenantInfo.get("enterprise_account")));
        common.put("next_business_id", String.valueOf(outTenantEi));
        common.put("next_tenant_name", getEnterpriseData(outTenantEi).getEnterpriseName());
    }

    private void handlePersonnelInfo(String tenantId, String personId, Map<String, Object> common) {
        if ("-10000".equals(personId)) {
            return;
        }
        IObjectData personnelInfo = serviceFacade.findObjectData(User.systemUser(tenantId), personId, "PersonnelObj");
        common.put(FMCGSerialNumberStatusApiNames.PERSONNEL_NAME, personnelInfo.getName());
        GetMultiEmployeeRoleRelationEntitiesByEmployeeIDs.Argument arg = PaasArgumentUtil.
                buildPaaSPermissionArgument(GetMultiEmployeeRoleRelationEntitiesByEmployeeIDs.Argument.class, Integer.valueOf(tenantId), -10000, "CRM");
        arg.setUsers(Lists.newArrayList(personId));
        PaaSResult<List<RoleRelationEntityDto>> result = paaSPermissionService.getMultiEmployeeRoleRelationEntitiesByEmployeeIDs(arg);
        if (Objects.isNull(result) || CollectionUtils.isEmpty(result.getResult())) {
            log.info("handlePersonnelInfo not found : {}, {}", tenantId, personId);
            return;
        }
        List<RoleRelationEntityDto> collect = result.getResult().stream().filter(f -> Boolean.TRUE.equals(f.getDefaultRole())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            common.put(FMCGSerialNumberStatusApiNames.PERSONNEL_HOST_ROLE, collect.get(0).getRoleCode());
        } else {
            common.put(FMCGSerialNumberStatusApiNames.PERSONNEL_HOST_ROLE, result.getResult().get(0).getRoleCode());
        }
        RoleListDto.Argument roleArg = PaasArgumentUtil.buildPaaSPermissionArgument(RoleListDto.Argument.class, Integer.valueOf(tenantId), -10000, "CRM");
        roleArg.setRoleCode(String.valueOf(common.get("personnel_host_role")));
        PaaSResult<RoleListDto.Result> resultPaaSResult = paaSPermissionService.roleList(roleArg);
        if (CollectionUtils.isNotEmpty(resultPaaSResult.getResult().getRoles())) {
            common.put(FMCGSerialNumberStatusApiNames.PERSONNEL_HOST_ROLE_NAME, resultPaaSResult.getResult().getRoles().get(0).getRoleName());
        }
    }

    private SimpleEnterpriseData getEnterpriseData(Integer tenantId) {
        GetSimpleEnterpriseDataArg argument = new GetSimpleEnterpriseDataArg();
        argument.setEnterpriseId(tenantId);
        GetSimpleEnterpriseDataResult simpleEnterpriseData = enterpriseEditionService.getSimpleEnterpriseData(argument);
        return simpleEnterpriseData.getEnterpriseData();
    }

    private Boolean matchConditions(FMCGSerialNumberActionEntity action, MessageBodyObj messageBodyObj, IObjectData objectData, IObjectDescribe describe) {
        String conditionPattern = action.getConditionPattern();
        //动作中没有配置条件则直接匹配该动作成功
        if (Strings.isNullOrEmpty(conditionPattern)) {
            return true;
        }
        if (Objects.isNull(objectData)) {
            return false;
        }

        List<ConditionEntity> conditions = action.getConditions();
        //按动作中的rowNo映射，对应行所需的条件
        Map<Integer, ConditionEntity> rowNoConditionMap = conditions.stream()
                .collect(Collectors.toMap(ConditionEntity::getRowNo, o -> o));

        //先按or拆分动作，然后处理每个or中的and条件
        String[] ors = conditionPattern.split("or");
        for (String or : ors) {
            or = or.trim();
            or = or.substring(1, or.length() - 1);
            List<Integer> and = Stream.of(or.split("and"))
                    .map(o -> Integer.valueOf(o.trim()))
                    .collect(Collectors.toList());
            boolean isMatch = false;
            switch (messageBodyObj.getTriggerType()) {
                case "i":
                    isMatch = matchCreateCondition(messageBodyObj.getTenantId(), and, objectData, describe, rowNoConditionMap);
                    break;
                case "u":
                    isMatch = matchUpdateCondition(messageBodyObj.getTenantId(), and, objectData, describe, rowNoConditionMap, messageBodyObj);
                    break;
                default:
                    break;
            }
            if (isMatch) {
                return true;
            }
        }
        return false;
    }

    private Boolean matchUpdateCondition(String tenantId, List<Integer> rowNos, IObjectData objectData, IObjectDescribe describe, Map<Integer, ConditionEntity> rowNoConditionMap, MessageBodyObj messageBodyObj) {
        for (Integer rowNo : rowNos) {
            if (!rowNoConditionMap.containsKey(rowNo)) {
                return false;
            }
            ConditionEntity conditionEntity = rowNoConditionMap.get(rowNo);
            //有些condition row在指定时机并不会触发变更，不会出现在before/afterTriggerData中，获取当前对象值进行比较
            Object source = handleFindAssociationValue(tenantId, objectData, describe, conditionEntity.getFieldName());
            //需要和mq中指定的时机值进行比较 after：本次mq变更后值
            if ("after".equals(conditionEntity.getOpportunity())) {
                JSONObject afterTriggerData = messageBodyObj.getAfterTriggerData();
                source = afterTriggerData.get(conditionEntity.getFieldName());
            } else if ("changed".equals(conditionEntity.getOpportunity())) {
                JSONObject afterTriggerData = messageBodyObj.getAfterTriggerData();
                return afterTriggerData.containsKey(conditionEntity.getFieldName());
            }
            Object target = conditionEntity.getFieldValue();
            boolean isMatch = matchOperator(source, target, conditionEntity);
            if (!isMatch) {
                return false;
            }
        }
        return true;
    }

    private Boolean matchCreateCondition(String tenantId, List<Integer> rowNos, IObjectData objectData, IObjectDescribe describe, Map<Integer, ConditionEntity> rowNoConditionMap) {
        for (Integer rowNo : rowNos) {
            if (!rowNoConditionMap.containsKey(rowNo)) {
                return false;
            }
            ConditionEntity conditionEntity = rowNoConditionMap.get(rowNo);
            Object source = handleFindAssociationValue(tenantId, objectData, describe, conditionEntity.getFieldName());
            Object target = conditionEntity.getFieldValue();
            boolean isMatch = matchOperator(source, target, conditionEntity);
            if (!isMatch) {
                return false;
            }
        }
        return true;
    }

    /**
     * 比较row中的当前值与目标值
     */
    private Boolean matchOperator(Object source, Object target, ConditionEntity conditionEntity) {
        boolean isMatch = false;
        String operator = conditionEntity.getOperator();
        String filedType = conditionEntity.getFieldType();
        if ("EQ".equals(operator)) {
            if ("string".equals(filedType)) {
                isMatch = Objects.equals(source, target);
            }
        } else if ("IN".equals(operator)) {
            if ("string".equals(filedType)) {
                List<String> list = JSON.parseObject(JSON.toJSONString(target), new TypeReference<List<String>>() {
                });
                isMatch = list.contains(String.valueOf(source));
            }
        } else if ("NEQ".equals(operator)) {
            if ("string".equals(filedType)) {
                isMatch = !Objects.equals(source, target);
            }
        } else if ("IS".equals(operator)) {
            return Objects.isNull(source);
        } else if ("ISN".equals(operator)) {
            return Objects.nonNull(operator);
        }
        return isMatch;
    }

    /**
     * 查找关联值
     * 配置格式为 查找关联/主从关系字段apiName.关联对象上字段apiName
     * 例：return_note_id.created_by
     */
    private Object handleFindAssociationValue(String tenantId, IObjectData objectData, IObjectDescribe describe, String fieldName) {
        String[] fieldNames = fieldName.split("\\.");
        Object result = null;
        if (fieldNames.length == 1) {
            result = objectData.get(fieldName);
            Object specialValue = specialProcessingField(objectData, describe, fieldName);
            if (Objects.nonNull(specialValue)) {
                result = specialValue;
            }
        } else if (fieldNames.length == 2) {
            Map fields = describe.get("fields", Map.class);
            String findAssociationField = fieldNames[0];
            Object findAssociationFieldInfo = fields.get(findAssociationField);
            Map findAssociationFieldInfoMap = JSONObject.parseObject(JSONObject.toJSONString(findAssociationFieldInfo), Map.class);
            String targetApiName = String.valueOf(findAssociationFieldInfoMap.get("target_api_name"));
            if (Strings.isNullOrEmpty(targetApiName)) {
                return null;
            }
            IObjectData targetObjectData = serviceFacade.findObjectData(User.systemUser(tenantId), String.valueOf(objectData.get(findAssociationField)), targetApiName);
            result = targetObjectData.get(fieldNames[1]);
            Object specialValue = specialProcessingField(targetObjectData, describe, fieldNames[1]);
            if (Objects.nonNull(specialValue)) {
                result = specialValue;
            }
        }
        return result;
    }

    private String handleFindAssociationObjApiName(IObjectDescribe describe, String fieldName) {
        if (CommonApiNames.OUT_OWNER.equals(fieldName)) {
            return "PublicEmployeeObj";
        }
        Map fieldInfo = getFieldInfoByDescribe(describe, fieldName);
        if (Objects.isNull(fieldInfo)) {
            return null;
        }
        String fieldType = String.valueOf(fieldInfo.get("type"));
        if ("employee".equals(fieldType)) {
            return "PersonnelObj";
        } else if ("object_reference".equals(fieldType)) {
            return String.valueOf(fieldInfo.get("target_api_name"));
        }
        return null;
    }

    /**
     * 处理特殊字段
     * 例：created_by 字段类型为人员，需要get(0)
     */
    private Object specialProcessingField(IObjectData objectData, IObjectDescribe describe, String fieldName) {
        if (CommonApiNames.CREATED_BY.equals(fieldName)) {
            return objectData.getCreatedBy();
        } else if (CommonApiNames.OUT_OWNER.equals(fieldName)) {
            if (Objects.nonNull(objectData.getOutOwner()) && !objectData.getOutOwner().isEmpty()) {
                return objectData.getOutOwner().get(0);
            }
        }
        Map fieldInfo = getFieldInfoByDescribe(describe, fieldName);
        if (Objects.isNull(fieldInfo)) {
            return null;
        }
        String fieldType = String.valueOf(fieldInfo.get("type"));
        if ("employee".equals(fieldType)) {
            if (Objects.nonNull(objectData.get(fieldName)) && !objectData.get(fieldName, List.class).isEmpty()) {
                return objectData.get(fieldName, List.class).get(0);
            }
        }
        return null;
    }

    private List<IObjectData> findObjectsByLocation(String tenantId, StorageLocationEntity location, IObjectData objectData, Map<String, IObjectDescribe> describeMap) {
        if (Strings.isNullOrEmpty(location.getMasterObjectField()) || Strings.isNullOrEmpty(location.getDetailObjectApiName())) {
            return Lists.newArrayList(objectData);
        }
        IFilter masterDataFilter = new Filter();
        masterDataFilter.setFieldName(location.getMasterObjectField());
        masterDataFilter.setOperator(Operator.EQ);
        masterDataFilter.setFieldValues(Lists.newArrayList(objectData.getId()));
        List<IObjectData> objectsByFilter = findObjectsByFilter(tenantId, location.getDetailObjectApiName(), Lists.newArrayList(masterDataFilter),
                Lists.newArrayList(CommonApiNames.ID, location.getProductField(), location.getStorageField()));
        if (CollectionUtils.isEmpty(objectsByFilter)) {
            return objectsByFilter;
        }
        IObjectDescribe detailDescribe = getObjetDescribeFromMap(tenantId, location.getDetailObjectApiName(), describeMap);
        Map fieldInfo = getFieldInfoByDescribe(detailDescribe, location.getStorageField());
        if (Objects.isNull(fieldInfo)) {
            return objectsByFilter;
        }
        String fieldType = String.valueOf(fieldInfo.get("type"));
        if ("big_text".equals(fieldType)) {
            List<String> ids = objectsByFilter.stream().map(DBRecord::getId).collect(Collectors.toList());
            return serviceFacade.findObjectDataByIds(tenantId, ids, location.getDetailObjectApiName());
        }
        return objectsByFilter;
    }

    private IObjectDescribe getObjetDescribeFromMap(String tenantId, String apiName, Map<String, IObjectDescribe> describeMap) {
        if (describeMap.containsKey(apiName)) {
            return describeMap.get(apiName);
        }
        IObjectDescribe describe = serviceFacade.findObject(tenantId, apiName);
        describeMap.put(apiName, describe);
        return describe;
    }

    private List<IObjectData> findObjectsByFilter(String tenantId, String apiName, List<IFilter> filters, List<String> fields) {
        SearchTemplateQuery queryTemplate = new SearchTemplateQuery();
        queryTemplate.setLimit(2000);
        queryTemplate.setFilters(filters);

        IActionContext actionContext = ActionContextUtil.getNewContext(tenantId);
        actionContext.setUserId(User.systemUser(tenantId).getUserId());
        actionContext.setPrivilegeCheck(false);

        return serviceFacade.findBySearchTemplateQueryWithFields(
                actionContext,
                apiName,
                queryTemplate,
                fields).getData();
    }
}
