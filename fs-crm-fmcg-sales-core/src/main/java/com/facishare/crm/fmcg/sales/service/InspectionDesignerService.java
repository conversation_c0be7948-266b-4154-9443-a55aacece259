package com.facishare.crm.fmcg.sales.service;

import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.facishare.appserver.utils.AccountUtils;
import com.facishare.crm.fmcg.sales.apiname.EnterpriseRelationObjApiNames;
import com.facishare.crm.fmcg.sales.business.*;
import com.facishare.crm.fmcg.sales.dao.mongo.entity.inspection.*;
import com.facishare.crm.fmcg.sales.dao.mongo.inspection.InspectionDesignerConfigDao;
import com.facishare.crm.fmcg.sales.dao.mongo.inspection.InspectionDesignerConfigScopeDao;
import com.facishare.crm.fmcg.sales.enums.InspectionDesignerConfigEnum;
import com.facishare.crm.fmcg.sales.enums.MagicEnum;
import com.facishare.crm.fmcg.sales.model.inspection.InspectionRule;
import com.facishare.crm.fmcg.sales.model.inspection.designer.*;
import com.facishare.crm.fmcg.sales.utils.I18nUtil;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@ServiceModule("inspection_designer")
public class InspectionDesignerService {

    @Resource
    private InspectionDesignerConfigDao inspectionDesignerConfigDao;

    @Resource
    private InspectionDesignerConfigScopeDao inspectionDesignerConfigScopeDao;

    @Resource
    private UserBusiness userBusiness;

    @Resource
    private InspectionBusiness inspectionBusiness;

    @Resource
    private InterconnectionBusiness interconnectionBusiness;

    @Resource
    private AccountBusiness accountBusiness;

    @Resource
    private CheckinBusiness checkinBusiness;
    @Resource
    private InspectionService inspectionService;

    /**
     * 稽查设计器列表
     */
    @ServiceMethod("list")
    public InspectionDesignerListVO.Result list(InspectionDesignerListVO.Arg arg, ServiceContext context) {
        List<InspectionDesignerConfigEntity> orderList = inspectionDesignerConfigDao.queryByPage(context.getTenantId(), arg.getPageSize(), (arg.getPageNumber() - 1) * arg.getPageSize());
        if (CollectionUtils.isEmpty(orderList)) {
            return InspectionDesignerListVO.Result.success(Collections.emptyList());
        }
        Map<String, JSONObject> dataMap = new HashMap<>();
        List<JSONObject> dataList = new ArrayList<>(orderList.size());
        List<String> inspectionDesignerIds = new ArrayList<>(orderList.size());
        for (InspectionDesignerConfigEntity entity : orderList) {
            String inspectionDesignerId = entity.getInspectionDesignerId();
            inspectionDesignerIds.add(inspectionDesignerId);
            JSONObject inspectionDesignerListVo = new JSONObject();
            inspectionDesignerListVo.put("inspectionDesignerId", inspectionDesignerId);
            inspectionDesignerListVo.put("createTime", entity.getCreateTime());
            dataList.add(inspectionDesignerListVo);
            dataMap.put(inspectionDesignerId, inspectionDesignerListVo);
        }
        List<InspectionDesignerConfigEnum> listPageItems = getListPageItems();
        List<String> groupKeys = listPageItems.stream().map(InspectionDesignerConfigEnum::getGroupKey).distinct().collect(Collectors.toList());
        List<String> keys = listPageItems.stream().map(InspectionDesignerConfigEnum::getKey).distinct().collect(Collectors.toList());
        List<InspectionDesignerConfigEntity> inspectionDesignerConfigEntities = inspectionDesignerConfigDao.queryByFilter(context.getTenantId(), inspectionDesignerIds, groupKeys, keys);
        for (InspectionDesignerConfigEntity entity : inspectionDesignerConfigEntities) {
            JSONObject inspectionDesignerListVo = dataMap.get(entity.getInspectionDesignerId());
            Object entityRealValue = getEntityRealValue(entity.getGroupKey(), entity.getKey(), entity.getValue());
            inspectionDesignerListVo.put(entity.getKey(), entityRealValue);
        }
        //handle other field
        for (JSONObject data : dataList) {
            data.put("typeName", InspectionDesignerConfigEnum.globalType.getItemByIndex(data.getIntValue(InspectionDesignerConfigEnum.globalType.getKey())));
            String ruleId = data.getString(InspectionDesignerConfigEnum.scanCodeJudgmentRuleId.getKey());
            if (StringUtils.isNotBlank(ruleId)) {
                InspectionRule inspectionRule = inspectionBusiness.queryInspectionRule(ruleId);
                if (Objects.nonNull(inspectionRule)) {
                    data.put("judgmentRuleName", inspectionRule.getRuleName());
                }
            }
            data.put("applicableScopeTypeName", InspectionDesignerConfigEnum.globalApplicableScopeType.getItemByIndex(data.getIntValue(InspectionDesignerConfigEnum.globalApplicableScopeType.getKey())));
        }
        return InspectionDesignerListVO.Result.success(dataList);
    }

    @ServiceMethod("getById")
    public InspectionDesignerGetVO.Result getById(InspectionDesignerGetVO.Arg arg, ServiceContext context) {
        if (StringUtils.isBlank(arg.getInspectionDesignerId())) {
            return InspectionDesignerGetVO.Result.error(I18nUtil.get(MagicEnum.inspectionDesignerIdRequired));
        }
        Map<String, JSONObject> data = inspectionBusiness.getDesignerConfigByInspectionDesignerId(context.getTenantId(), arg.getInspectionDesignerId());
        if (data == null) {
            return InspectionDesignerGetVO.Result.error(I18nUtil.get(MagicEnum.inspectionDesignerNotExist));
        }
        return InspectionDesignerGetVO.Result.success(data);
    }

    @ServiceMethod("load")
    public InspectionDesignerLoadVO.Result load(InspectionDesignerLoadVO.Arg arg, ServiceContext context) {
        String upStreamTenantId = inspectionBusiness.getUpstreamTenantIdByPublicObject(context.getTenantId());
        int globalApplicableScopeType = upStreamTenantId.equals(context.getTenantId()) ? 0 : 1;
        List<InspectionDesignerConfigEntity> inspectionDesignerConfigEntities = inspectionDesignerConfigDao.queryByPage(upStreamTenantId, InspectionDesignerConfigEnum.globalApplicableScopeType, JSONObject.toJSONString(globalApplicableScopeType), 1, 0);
        if (CollectionUtils.isEmpty(inspectionDesignerConfigEntities)) {
            return InspectionDesignerLoadVO.Result.error(I18nUtil.get(MagicEnum.noAvailableDesigner));
        }
        String inspectionDesignerId = inspectionDesignerConfigEntities.get(0).getInspectionDesignerId();
        Map<String, JSONObject> data = inspectionBusiness.getDesignerConfigByInspectionDesignerId(upStreamTenantId, inspectionDesignerId);
        if (data == null) {
            return InspectionDesignerLoadVO.Result.error(I18nUtil.get(MagicEnum.inspectionDesignerNotExist));
        }
        return InspectionDesignerLoadVO.Result.success(inspectionDesignerId, data);
    }

    @ServiceMethod("queryAccount")
    public InspectionDesignerQueryAccountVO.Result queryAccount(InspectionDesignerQueryAccountVO.Arg arg, ServiceContext context) {
        if (StringUtils.isBlank(arg.getPosition())) {
            return InspectionDesignerQueryAccountVO.Result.error(I18nUtil.get(MagicEnum.positionRequired));
        }
        InspectionDesignerQueryAccountVO.Result result = new InspectionDesignerQueryAccountVO.Result();
        String upStreamTenantId = inspectionBusiness.getUpstreamTenantIdByPublicObject(context.getTenantId());
        result.setIsDealer(inspectionService.isDealer(context.getTenantId(), upStreamTenantId));
        if (result.getIsDealer()) {
            //经销商
            IObjectData erInfo = interconnectionBusiness.getTenantInfoInUpstream(upStreamTenantId, context.getTenantId(), Lists.newArrayList(EnterpriseRelationObjApiNames.MAPPER_ACCOUNT_ID));
            if (Objects.isNull(erInfo)) {
                return InspectionDesignerQueryAccountVO.Result.error(I18nUtil.get(MagicEnum.interconnectDataNotFound));
            }
            String accountId = erInfo.get(EnterpriseRelationObjApiNames.MAPPER_ACCOUNT_ID, String.class);
            IObjectData accountInfo = accountBusiness.getAccountInfoById(upStreamTenantId, accountId, Lists.newArrayList("covering_sales_areas"));
            if (Objects.isNull(accountInfo)) {
                return InspectionDesignerQueryAccountVO.Result.error(I18nUtil.get(MagicEnum.relatedDataNotFound));
            }
            List<String> coveringSalesAreas = accountInfo.get("covering_sales_areas", List.class);
            if (CollectionUtils.isEmpty(coveringSalesAreas)) {
                return InspectionDesignerQueryAccountVO.Result.error(I18nUtil.get(MagicEnum.salesAreaNotConfiguredWithContact));
            }
            List<String> salesAreaByPoints = checkinBusiness.getSalesAreaByPoint(Integer.parseInt(upStreamTenantId), arg.getPosition().replace("#%$", ","));
            boolean inSalesArea = salesAreaByPoints.stream().anyMatch(coveringSalesAreas::contains);
            if (!inSalesArea) {
                return InspectionDesignerQueryAccountVO.Result.error(I18nUtil.get(MagicEnum.outOfBusinessScope));
            }
            return result;
        }
        result.setAccountInfoList(Lists.newArrayList());
        List<String> salesAreaByPoints = checkinBusiness.getSalesAreaByPoint(Integer.parseInt(upStreamTenantId), arg.getPosition().replace("#%$", ","));
        if (CollectionUtils.isNotEmpty(salesAreaByPoints)) {
            String salesArea = salesAreaByPoints.get(salesAreaByPoints.size() - 1);
            List<IObjectData> accountListBySalesArea = accountBusiness.getAccountListBySalesArea(upStreamTenantId, salesAreaByPoints, Lists.newArrayList("_id", "name"));
            if (CollectionUtils.isNotEmpty(accountListBySalesArea)) {
                for (IObjectData iObjectData : accountListBySalesArea) {
                    InspectionDesignerQueryAccountVO.AccountInfo accountInfo = new InspectionDesignerQueryAccountVO.AccountInfo();
                    accountInfo.setAccountId(iObjectData.getId());
                    accountInfo.setAccountName(iObjectData.getName());
                    result.getAccountInfoList().add(accountInfo);
                }
            }
        }
        return result;
    }

    @ServiceMethod("add")
    public InspectionDesignerAddVO.Result add(InspectionDesignerAddVO.Arg arg, ServiceContext context) {
        Map<String, JSONObject> data = InspectionDesignerConfigEnum.getDefaultConfigMap();
        String errorMsg = checkKeyAndWrite(data, arg.getData());
        if (StringUtils.isNotBlank(errorMsg)) {
            return InspectionDesignerAddVO.Result.error(errorMsg);
        }
        if (inspectionDesignerConfigDao.checkNameRepeat(context.getTenantId(), arg.getData().get(InspectionDesignerConfigEnum.globalName.getGroupKey()).getString(InspectionDesignerConfigEnum.globalName.getKey()))) {
            return InspectionDesignerAddVO.Result.error(I18nUtil.get(MagicEnum.nameDuplicate));
        }
        int globalApplicableScopeType = arg.getData().get(InspectionDesignerConfigEnum.globalApplicableScopeType.getGroupKey()).getIntValue(InspectionDesignerConfigEnum.globalApplicableScopeType.getKey());
        if (inspectionDesignerConfigDao.checkRepeatByConfigEnum(context.getTenantId(), InspectionDesignerConfigEnum.globalApplicableScopeType, JSONObject.toJSONString(globalApplicableScopeType), null)) {
            return InspectionDesignerAddVO.Result.error(I18nUtil.get(MagicEnum.scopeExists));
        }
        String inspectionDesignerId = IdGenerator.get();
        long creatTime = System.currentTimeMillis();
        data.get(InspectionDesignerConfigEnum.systemCreateBy.getGroupKey())
                .fluentPut(InspectionDesignerConfigEnum.systemCreateBy.getKey(), Integer.parseInt(context.getUser().getUserId()))
                .fluentPut(InspectionDesignerConfigEnum.systemModifyBy.getKey(), Integer.parseInt(context.getUser().getUserId()))
                .fluentPut(InspectionDesignerConfigEnum.systemModifyTime.getKey(), creatTime);
        data.get(InspectionDesignerConfigEnum.businessUnEffectiveUserIds.getGroupKey())
                .fluentPut(InspectionDesignerConfigEnum.businessUnEffectiveUserIds.getKey(), arg.getUnEffectiveUserIds());
        List<InspectionDesignerConfigEntity> dataList = new ArrayList<>();
        for (Map.Entry<String, JSONObject> groupEntry : data.entrySet()) {
            for (Map.Entry<String, Object> keyEntry : groupEntry.getValue().entrySet()) {
                InspectionDesignerConfigEntity item = new InspectionDesignerConfigEntity();
                item.setId(new ObjectId());
                item.setTenantId(context.getTenantId());
                item.setInspectionDesignerId(inspectionDesignerId);
                item.setGroupKey(groupEntry.getKey());
                item.setKey(keyEntry.getKey());
                //校验值的类型
                if (!InspectionDesignerConfigEnum.inspectionDesignerConfigEnumMap.get(groupEntry.getKey() + "_" + keyEntry.getKey()).getValueClass().isAssignableFrom(keyEntry.getValue().getClass())) {
                    log.info("groupKey={}, keu={}, value={}", groupEntry.getKey(), keyEntry.getKey(), keyEntry.getValue());
                    return InspectionDesignerAddVO.Result.error(String.format(I18nUtil.get(MagicEnum.undefinedGroupKey) +", %s", groupEntry.getKey()));
                }
                item.setValue(JSONObject.toJSONString(keyEntry.getValue()));
                item.setCreateTime(creatTime);
                dataList.add(item);
            }
        }
        inspectionDesignerConfigDao.batchAddInspectionDesignerConfig(dataList);
        List<InspectionDesignerConfigScopeEntity> scopeEntities = Lists.newArrayList();
        for (Integer effectiveUserId : arg.getEffectiveUserIds()) {
            InspectionDesignerConfigScopeEntity scopeEntity = new InspectionDesignerConfigScopeEntity();
            scopeEntity.setUserAccount(AccountUtils.getUserAccount(context.getEa(), effectiveUserId));
            scopeEntity.setInspectionDesignerId(inspectionDesignerId);
            scopeEntity.setModifyTime(creatTime);
            scopeEntity.setTenantId(context.getTenantId());
            scopeEntity.setUserType(data.get(InspectionDesignerConfigEnum.globalIsAllOuter.getGroupKey()).getBooleanValue(InspectionDesignerConfigEnum.globalIsAllOuter.getKey()) ? 1 : 0);
            scopeEntities.add(scopeEntity);
        }
        if (CollectionUtils.isNotEmpty(scopeEntities)) {
            inspectionDesignerConfigScopeDao.batchAdd(scopeEntities);
        }
        return InspectionDesignerAddVO.Result.success();
    }

    @ServiceMethod("update")
    public InspectionDesignerUpdateVO.Result update(InspectionDesignerUpdateVO.Arg arg, ServiceContext context) {
        if (StringUtils.isBlank(arg.getInspectionDesignerId())) {
            InspectionDesignerUpdateVO.Result.error(I18nUtil.get(MagicEnum.inspectionDesignerIdRequired));
        }
        for (InspectionDesignerUpdateVO.UpdateEntity updateEntity : arg.getUpdateEntities()) {
            if (InspectionDesignerConfigEnum.globalName.getGroupKey().equals(updateEntity.getGroupKey()) && InspectionDesignerConfigEnum.globalName.getKey().equals(updateEntity.getKey())) {
                if (inspectionDesignerConfigDao.checkNameRepeat(context.getTenantId(), updateEntity.getValue().toString(), arg.getInspectionDesignerId())) {
                    return InspectionDesignerUpdateVO.Result.error(I18nUtil.get(MagicEnum.nameDuplicate));
                }
            } else if (InspectionDesignerConfigEnum.globalApplicableScopeType.getGroupKey().equals(updateEntity.getGroupKey()) && InspectionDesignerConfigEnum.globalApplicableScopeType.getKey().equals(updateEntity.getKey())) {
                if (inspectionDesignerConfigDao.checkRepeatByConfigEnum(context.getTenantId(), InspectionDesignerConfigEnum.globalApplicableScopeType, JSONObject.toJSONString(updateEntity.getValue()), arg.getInspectionDesignerId())) {
                    return InspectionDesignerUpdateVO.Result.error(I18nUtil.get(MagicEnum.scopeExists));
                }
            }
        }
        arg.getUpdateEntities().add(new InspectionDesignerUpdateVO.UpdateEntity(InspectionDesignerConfigEnum.businessUnEffectiveUserIds.getGroupKey(), InspectionDesignerConfigEnum.businessUnEffectiveUserIds.getKey(), arg.getUnEffectiveUserIds()));
        arg.getUpdateEntities().add(new InspectionDesignerUpdateVO.UpdateEntity(InspectionDesignerConfigEnum.systemModifyTime.getGroupKey(), InspectionDesignerConfigEnum.systemModifyTime.getKey(), System.currentTimeMillis()));
        arg.getUpdateEntities().add(new InspectionDesignerUpdateVO.UpdateEntity(InspectionDesignerConfigEnum.systemModifyBy.getGroupKey(), InspectionDesignerConfigEnum.systemModifyBy.getKey(), context.getUser().getUserId()));
        //先查在修改
        Collection<String> groupKeys = Sets.newHashSet(InspectionDesignerConfigEnum.businessUnEffectiveUserIds.getGroupKey(), InspectionDesignerConfigEnum.systemModifyTime.getGroupKey(), InspectionDesignerConfigEnum.globalApplicableScopeType.getGroupKey());
        List<String> keys = Lists.newArrayList(InspectionDesignerConfigEnum.businessUnEffectiveUserIds.getKey(), InspectionDesignerConfigEnum.systemModifyTime.getKey(), InspectionDesignerConfigEnum.systemModifyBy.getKey(), InspectionDesignerConfigEnum.globalApplicableScopeType.getKey());
        if (CollectionUtils.isNotEmpty(arg.getUpdateEntities())) {
            arg.getUpdateEntities().removeIf(i -> InspectionDesignerConfigEnum.systemCreateBy.getGroupKey().equals(i.getGroupKey()));
            for (InspectionDesignerUpdateVO.UpdateEntity updateEntity : arg.getUpdateEntities()) {
                groupKeys.add(updateEntity.getGroupKey());
                keys.add(updateEntity.getKey());
            }
        }
        List<InspectionDesignerConfigEntity> entities = inspectionDesignerConfigDao.queryByFilter(context.getTenantId(), Lists.newArrayList(arg.getInspectionDesignerId()), groupKeys, keys);
        Map<String, InspectionDesignerConfigEntity> entitiesMap = getMapByEntities(entities);
        List<InspectionDesignerConfigEntity> updateEntities = Lists.newArrayList();
        for (InspectionDesignerUpdateVO.UpdateEntity argUpdateEntity : arg.getUpdateEntities()) {
            InspectionDesignerConfigEntity entity = entitiesMap.get(argUpdateEntity.getGroupKey() + "_" + argUpdateEntity.getKey());
            if (entity == null) {
                entity = new InspectionDesignerConfigEntity();
                entity.setId(new ObjectId());
                entity.setTenantId(context.getTenantId());
                entity.setInspectionDesignerId(arg.getInspectionDesignerId());
                entity.setGroupKey(argUpdateEntity.getGroupKey());
                entity.setKey(argUpdateEntity.getKey());
                entity.setCreateTime(System.currentTimeMillis());
            }
            entity.setValue(JSONObject.toJSONString(argUpdateEntity.getValue()));
            updateEntities.add(entity);
        }
        if (CollectionUtils.isNotEmpty(updateEntities)) {
            inspectionDesignerConfigDao.batchAddInspectionDesignerConfig(updateEntities);
        }
        //未命中的删除
        if (CollectionUtils.isNotEmpty(arg.getUnEffectiveUserIds())) {
            List<String> userAccounts = arg.getUnEffectiveUserIds().stream().map(i -> AccountUtils.getUserAccount(context.getEa(), i)).collect(Collectors.toList());
            inspectionDesignerConfigScopeDao.batchDelete(context.getTenantId(), userAccounts);
        }
        //添加命中的
        if (CollectionUtils.isNotEmpty(arg.getEffectiveUserIds())) {
            InspectionDesignerConfigEntity isAllOuter = entitiesMap.get(InspectionDesignerConfigEnum.globalIsAllOuter.getGroupKey());
            List<InspectionDesignerConfigScopeEntity> scopeEntities = Lists.newArrayList();
            for (Integer effectiveUserId : arg.getEffectiveUserIds()) {
                InspectionDesignerConfigScopeEntity scopeEntity = new InspectionDesignerConfigScopeEntity();
                scopeEntity.setUserAccount(AccountUtils.getUserAccount(context.getEa(), effectiveUserId));
                scopeEntity.setInspectionDesignerId(arg.getInspectionDesignerId());
                scopeEntity.setModifyTime(System.currentTimeMillis());
                scopeEntity.setTenantId(context.getTenantId());
                scopeEntity.setUserType(Objects.nonNull(isAllOuter) && StringUtils.isNotBlank(isAllOuter.getValue()) && Boolean.parseBoolean(isAllOuter.getValue()) ? 1 : 0);
                scopeEntities.add(scopeEntity);
            }
            inspectionDesignerConfigScopeDao.batchAdd(scopeEntities);
        }
        return InspectionDesignerUpdateVO.Result.success();
    }

    @ServiceMethod("getConfigUserSet")
    public GetConfigUserSetVO.Result getConfigUserSet(GetConfigUserSetVO.Arg arg, ServiceContext context) {
        if (StringUtils.isBlank(arg.getInspectionDesignerId())) {
            return GetConfigUserSetVO.Result.emptyResult();
        }
        GetConfigUserSetVO.Result result = new GetConfigUserSetVO.Result();
        List<InspectionDesignerConfigEntity> entities = inspectionDesignerConfigDao.queryByFilter(context.getTenantId(), Lists.newArrayList(arg.getInspectionDesignerId()), InspectionDesignerConfigEnum.getScopeRelationGroupKeys(), InspectionDesignerConfigEnum.getScopeRelationKeys());
        Map<String, InspectionDesignerConfigEntity> entitiesMap = getMapByEntities(entities);
        Boolean isAllOuter = getMapByEntities(entitiesMap, InspectionDesignerConfigEnum.globalIsAllOuter, Boolean.class);
        List<Integer> userIds = getMapByEntities(entitiesMap, InspectionDesignerConfigEnum.globalUserIds, List.class);
        List<Integer> deptIds = getMapByEntities(entitiesMap, InspectionDesignerConfigEnum.globalDeptIds, List.class);
        List<String> roleGroupIds = getMapByEntities(entitiesMap, InspectionDesignerConfigEnum.globalRoleGroupIds, List.class);
        List<String> userGroupIds = getMapByEntities(entitiesMap, InspectionDesignerConfigEnum.globalUserGroupIds, List.class);
        List<Integer> allUserIds = new ArrayList<>();
        if (isAllOuter) {

        } else {
            allUserIds = userBusiness.mergeUserIds(Integer.parseInt(context.getTenantId()), userIds, deptIds, roleGroupIds, userGroupIds);
        }
        //获取当前有效的员工
        List<Integer> effectiveUserIds = inspectionDesignerConfigScopeDao.queryUseUserIdsByInspectionDesignerId(context.getTenantId(), arg.getInspectionDesignerId());
        result.setEffectiveUserIds(effectiveUserIds);
        List<HaveOtherConfig> haveOtherConfigUsers = getHaveOtherConfigUsers(context.getTenantId(), context.getEa(), allUserIds, arg.getInspectionDesignerId());
        result.setHaveHaveOtherConfigUserIds(haveOtherConfigUsers);
        //查询有其他规则的userId
        List<Integer> haveUserIds = haveOtherConfigUsers.stream().map(HaveOtherConfig::getUserId).collect(Collectors.toList());
        List<Integer> unEffectiveUserIds = new ArrayList<>(allUserIds);
        if (CollectionUtils.isNotEmpty(effectiveUserIds)) {
            unEffectiveUserIds.removeAll(effectiveUserIds);
            unEffectiveUserIds.removeAll(haveUserIds);
        }
        result.setUnEffectiveUserIds(unEffectiveUserIds);
        return result;
    }

    @ServiceMethod("checkConfigUserSet")
    public CheckConfigUserSetVO.Result checkConfigUserSet(CheckConfigUserSetVO.Arg arg, ServiceContext context) {

        CheckConfigUserSetVO.Result result = new CheckConfigUserSetVO.Result();
        List<Integer> allUserIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(arg.getOuterRoleIds()) || CollectionUtils.isNotEmpty(arg.getOuterTenantIds()) ||
                CollectionUtils.isNotEmpty(arg.getOuterUserIds()) || CollectionUtils.isNotEmpty(arg.getTenantGroupIds())) {
            //处理互联相关
        } else {
            //全部人员
            allUserIds = userBusiness.mergeUserIds(Integer.parseInt(context.getTenantId()), arg.getUserIds(), arg.getDeptIds(), arg.getRoleGroupIds(), arg.getUserGroupIds());
        }
        //有效的
        List<Integer> effectiveUserIds = new ArrayList<>();
        List<Integer> unEffectiveUserIds = new ArrayList<>();
        List<HaveOtherConfig> haveOtherConfigUsers = new ArrayList<>();
        List<Integer> haveUserIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(allUserIds)) {
            effectiveUserIds = Lists.newArrayList(allUserIds);
            haveOtherConfigUsers = getHaveOtherConfigUsers(context.getTenantId(), context.getEa(), allUserIds, arg.getInspectionDesignerId());
            if (CollectionUtils.isNotEmpty(haveOtherConfigUsers)) {
                haveUserIds = haveOtherConfigUsers.stream().map(HaveOtherConfig::getUserId).collect(Collectors.toList());
            }
            if (StringUtils.isNotBlank(arg.getInspectionDesignerId())) {
                //获取无效的人
                unEffectiveUserIds = inspectionDesignerConfigDao.getUnEffectiveUserIds(context.getTenantId(), arg.getInspectionDesignerId());
            }
        }
        HashSet<Integer> allUserIdsSet = new HashSet<>(allUserIds);
        effectiveUserIds.removeAll(unEffectiveUserIds);
        effectiveUserIds.removeAll(haveUserIds);
        unEffectiveUserIds.removeAll(haveUserIds);
        unEffectiveUserIds.removeIf(i -> !allUserIdsSet.contains(i));
        result.setEffectiveUserIds(effectiveUserIds);
        result.setHaveHaveOtherConfigUserIds(haveOtherConfigUsers);
        result.setUnEffectiveUserIds(unEffectiveUserIds);
        return result;
    }

    @ServiceMethod("delete")
    public DeleteVO.Result delete(DeleteVO.Arg arg, ServiceContext context) {
        if (StringUtils.isBlank(arg.getInspectionDesignerId())) {
            return DeleteVO.Result.error(I18nUtil.get(MagicEnum.inspectionDesignerIdRequired));
        }
        int dN = inspectionDesignerConfigDao.deleteByInspectionDesignerId(context.getTenantId(), arg.getInspectionDesignerId());
        int dN1 = inspectionDesignerConfigScopeDao.deleteByInspectionDesignerId(context.getTenantId(), arg.getInspectionDesignerId());
        log.info("delete dN={}, dN1={}", dN, dN1);
        return DeleteVO.Result.success();
    }

    @ServiceMethod("copy")
    public CopyVO.Result copy(CopyVO.Arg arg, ServiceContext context) {
        return CopyVO.Result.success();
    }


    private List<HaveOtherConfig> getHaveOtherConfigUsers(String tenantId, String
            ea, List<Integer> userIdList, String inspectionDesignerId) {
        List<HaveOtherConfig> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(userIdList)) {
            return result;
        }
        List<String> userAccounts = userIdList.stream().distinct().map(o -> AccountUtils.getUserAccount(ea, o)).
                collect(Collectors.toList());
        List<InspectionDesignerConfigScopeEntity> haveOtherConfigUsers = inspectionDesignerConfigScopeDao.getHaveOtherConfigUsers(tenantId, userAccounts, inspectionDesignerId);
        if (CollectionUtils.isNotEmpty(haveOtherConfigUsers)) {
            Set<String> inspectionDesignerIds = haveOtherConfigUsers.stream().map(InspectionDesignerConfigScopeEntity::getInspectionDesignerId).collect(Collectors.toSet());
            Map<String, String> stringStringMap = inspectionDesignerConfigDao.queryInspectionDesignerNameMapOfId(tenantId, inspectionDesignerIds);
            haveOtherConfigUsers.forEach(item -> {
                HaveOtherConfig haveOtherConfig = new HaveOtherConfig();
                haveOtherConfig.setInspectionDesignerId(item.getInspectionDesignerId());
                haveOtherConfig.setInspectionDesignerName(stringStringMap.get(item.getInspectionDesignerId()));
                haveOtherConfig.setUserId(AccountUtils.getUserIdByAccount(item.getUserAccount()));
                result.add(haveOtherConfig);
            });
        }
        return result;
    }

    private Map<String, InspectionDesignerConfigEntity> getMapByEntities
            (List<InspectionDesignerConfigEntity> entities) {
        Map<String, InspectionDesignerConfigEntity> result = new HashMap<>();
        for (InspectionDesignerConfigEntity entity : entities) {
            result.put(entity.getGroupKey() + "_" + entity.getKey(), entity);
        }
        return result;
    }

    private <E> E
    getMapByEntities(Map<String, InspectionDesignerConfigEntity> data, InspectionDesignerConfigEnum configEnum, Class<E> valueClass) {
        InspectionDesignerConfigEntity value = data.get(configEnum.getGroupKey() + "_" + configEnum.getKey());
        return JSONObject.parseObject(value.getValue(), valueClass);
    }

    private String checkKeyAndWrite(Map<String, JSONObject> allKeys, Map<String, JSONObject> waitVerifyKeys) {
        //校验key
        for (Map.Entry<String, JSONObject> groupEntry : waitVerifyKeys.entrySet()) {
            String groupKey = groupEntry.getKey();
            if (!allKeys.containsKey(groupKey)) {
                return String.format(I18nUtil.get(MagicEnum.undefinedGroupKey) + ", %s", groupKey);
            }
            JSONObject keyMap = allKeys.get(groupKey);
            for (Map.Entry<String, Object> keyEntry : groupEntry.getValue().entrySet()) {
                String key = keyEntry.getKey();
                if (!keyMap.containsKey(key)) {
                    return String.format(I18nUtil.get(MagicEnum.undefinedKey) + ", %s", key);
                }
                keyMap.put(key, keyEntry.getValue());
            }
        }

        String judgmentRuleId = allKeys.get(InspectionDesignerConfigEnum.scanCodeJudgmentRuleId.getGroupKey()).getString(InspectionDesignerConfigEnum.scanCodeJudgmentRuleId.getKey());
        boolean verifyJudgmentRuleIdResult = inspectionBusiness.verifyJudgmentRuleId(judgmentRuleId);
        if (!verifyJudgmentRuleIdResult) {
            return I18nUtil.get(MagicEnum.invalidJudgmentRule);
        }
        return null;
    }

    private Object getEntityRealValue(String groupKey, String key, String value) {
        InspectionDesignerConfigEnum inspectionDesignerConfigEnum = InspectionDesignerConfigEnum.inspectionDesignerConfigEnumMap.get(groupKey + "_" + key);
        return inspectionDesignerConfigEnum.convertValue(value);
    }

    private List<InspectionDesignerConfigEnum> getListPageItems() {
        List<InspectionDesignerConfigEnum> items = Lists.newArrayList();
        items.add(InspectionDesignerConfigEnum.globalType);
        items.add(InspectionDesignerConfigEnum.globalDescription);
        items.add(InspectionDesignerConfigEnum.globalApplicableScopeType);
        items.add(InspectionDesignerConfigEnum.scanCodeJudgmentRuleId);
        items.add(InspectionDesignerConfigEnum.systemCreateBy);
        items.add(InspectionDesignerConfigEnum.systemModifyBy);
        items.add(InspectionDesignerConfigEnum.systemModifyTime);
        return items;
    }
}
