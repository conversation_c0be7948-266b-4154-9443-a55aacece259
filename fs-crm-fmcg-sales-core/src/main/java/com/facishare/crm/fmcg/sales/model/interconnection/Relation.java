package com.facishare.crm.fmcg.sales.model.interconnection;

import com.fxiaoke.api.model.BaseResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR> l<PERSON><PERSON><PERSON>
 * @create : 2024-04-16 11:32
 **/
public interface Relation {
    @Data
    @ToString
    class Arg {
        private Integer tenantId;
    }

    @Data
    @ToString
    class Result extends BaseResult {
        private ResultData data;

        public static Result success(ResultData resultData) {
            Result result = new Result();
            result.setErrorCode(0);
            result.setErrorMsg("success");
            result.setData(resultData);
            return result;
        }

        public static Result error(Integer errorCode, String errorMsg) {
            Result result = new Result();
            result.setErrorCode(errorCode);
            result.setErrorMsg(errorMsg);
            return result;
        }
    }

    @Data
    @ToString
    class ResultData {
        private List<TenantInfo> relations;
    }

    @Data
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    class TenantInfo {
        private Integer tenantId;
        private String name;
        private String type;
        private String accountRecordType;
        private Integer upstreamTenantId;
    }
}
