package com.facishare.crm.fmcg.sales.business;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.sales.apiname.AccountObjApiNames;
import com.facishare.crm.fmcg.sales.apiname.EnterpriseRelationObjApiNames;
import com.facishare.crm.fmcg.sales.apiname.FMCGSerialNumberApiNames;
import com.facishare.crm.fmcg.sales.apiname.InspectionRecordObjApiNames;
import com.facishare.crm.fmcg.sales.cache.ObjectStatusCache;
import com.facishare.crm.fmcg.sales.dao.mongo.entity.inspection.InspectionDesignerConfigEntity;
import com.facishare.crm.fmcg.sales.dao.mongo.inspection.InspectionDesignerConfigDao;
import com.facishare.crm.fmcg.sales.enums.InspectionDesignerConfigEnum;
import com.facishare.crm.fmcg.sales.model.inspection.InspectionRule;
import com.facishare.crm.fmcg.sales.model.inspection.InspectionVerify;
import com.facishare.crm.fmcg.sales.utils.LocationUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fmcg.framework.http.I18nSettingProxy;
import com.fmcg.framework.http.contract.i18nsetting.FindNearestArea;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> liuhaoyu
 * @create : 2024-07-16 11:11
 **/
@Slf4j
@Component
public class InspectionBusiness extends BusinessBase {
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private InspectionDesignerConfigDao inspectionDesignerConfigDao;
    @Resource
    private ObjectStatusCache objectStatusCache;
    @Resource
    private I18nSettingProxy i18nSettingProxy;

    public FindNearestArea.ResultBody getRegionalCode(Integer tenantId, String location, List<String> address, String mapType) {
        if (StringUtils.isEmpty(location)) {
            return null;
        }
        String[] analyticLocation = LocationUtils.analyticLocation(location);
        if (analyticLocation.length < 2) {
            log.info("getRegionalCode analyticLocation Unavailable {}", location);
            return null;
        }
        FindNearestArea.Arg arg = new FindNearestArea.Arg();
        arg.setLongitude(analyticLocation[0]);
        arg.setLatitude(analyticLocation[1]);
        arg.setAddress(address);
        arg.setMapType(mapType);
        FindNearestArea.Result nearestArea = i18nSettingProxy.findNearestArea(tenantId, -10000, arg);
        if (nearestArea.getErrCode() != 0) {
            log.info("getRegionalCode nearestArea error {}, {}", nearestArea.getErrCode(), nearestArea.getErrMessage());
            return null;
        }
        return nearestArea.getResult();
    }

    public Map<String, JSONObject> getDesignerConfigByInspectionDesignerId(String tenantId, String inspectionDesignerId) {
        List<InspectionDesignerConfigEntity> entityList = inspectionDesignerConfigDao.queryByFilter(tenantId, Lists.newArrayList(inspectionDesignerId));
        if (CollectionUtils.isEmpty(entityList)) {
            return null;
        }
        Map<String, JSONObject> data = InspectionDesignerConfigEnum.getDefaultConfigMap();
        //填充默认值
        for (InspectionDesignerConfigEntity entity : entityList) {
            Object entityRealValue = getEntityRealValue(entity.getGroupKey(), entity.getKey(), entity.getValue());
            String groupKey = entity.getGroupKey();
            if (!data.containsKey(groupKey)) {
                data.put(groupKey, new JSONObject().fluentPut(entity.getKey(), entityRealValue));
            } else {
                data.get(groupKey).fluentPut(entity.getKey(), entityRealValue);
            }
        }
        return data;
    }

    public Boolean isIllegallyGoodsAccountOwner(User user, String illegallyGoodsAccountId) {
        String tenantId = user.getTenantId();
        String upstreamTenantId = getUpstreamTenantIdByPublicObject(tenantId);
        IObjectData illegallyGoodsAccountInfo = findEnterpriseRelationObjByAccountId(User.systemUser(upstreamTenantId),
                illegallyGoodsAccountId, Lists.newArrayList(EnterpriseRelationObjApiNames.ENTERPRISE_ACCOUNT));
        if (Objects.isNull(illegallyGoodsAccountInfo)) {
            log.info("illegallyGoodsAccountInfo is null : {}. {}", JSONObject.toJSONString(user), illegallyGoodsAccountId);
            return false;
        }
        String illegallyGoodsAccountEa = illegallyGoodsAccountInfo.get(EnterpriseRelationObjApiNames.ENTERPRISE_ACCOUNT, String.class);
        int illegallyGoodsAccountEi = eieaConverter.enterpriseAccountToId(illegallyGoodsAccountEa);
        return tenantId.equals(String.valueOf(illegallyGoodsAccountEi)) && "1000".equals(user.getUserId());
    }

    public String getUpstreamTenantIdByPublicObject(String tenantId) {
        ObjectStatusCache.Status status = objectStatusCache.getNoException(tenantId, InspectionRecordObjApiNames.OBJECT_API_NAME);
        return status.getPublicObject() ? status.getUpstreamTenantId() : tenantId;
    }

    public IObjectData findCurLoginAccountInfoInUpstream(InspectionVerify.Context context) {
        if (Objects.nonNull(context.getCurAccountInfoInUpstream())) {
            return context.getCurAccountInfoInUpstream();
        }

        String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(context.getTenantId()));
        IFilter tiFilter = new Filter();
        tiFilter.setFieldValues(Lists.newArrayList(ea));
        tiFilter.setOperator(Operator.EQ);
        tiFilter.setFieldName(EnterpriseRelationObjApiNames.ENTERPRISE_ACCOUNT);

        SearchTemplateQuery queryTemplate = new SearchTemplateQuery();
        queryTemplate.setLimit(1);
        queryTemplate.setFilters(Lists.newArrayList(tiFilter));

        List<IObjectData> result = serviceFacade.findBySearchTemplateQueryWithFields(
                ActionContextExt.of(User.systemUser(context.getUpstreamTenantId())).getContext(),
                EnterpriseRelationObjApiNames.OBJECT_API_NAME,
                queryTemplate,
                Lists.newArrayList(EnterpriseRelationObjApiNames.MAPPER_ACCOUNT_ID)).getData();

        if (CollectionUtils.isEmpty(result)) {
            return null;
        }
        String accountId = result.get(0).get(EnterpriseRelationObjApiNames.MAPPER_ACCOUNT_ID, String.class);
        IObjectData objectData = serviceFacade.findObjectData(User.systemUser(context.getUpstreamTenantId()), accountId, AccountObjApiNames.OBJECT_API_NAME);
        context.setCurAccountInfoInUpstream(objectData);
        return context.getCurAccountInfoInUpstream();
    }

    public IObjectData findEnterpriseRelationObjByAccountId(User user, String accountId, List<String> fields) {
        if (Strings.isNullOrEmpty(accountId)) {
            return null;
        }
        IFilter tiFilter = new Filter();
        tiFilter.setFieldValues(Lists.newArrayList(accountId));
        tiFilter.setOperator(Operator.EQ);
        tiFilter.setFieldName(EnterpriseRelationObjApiNames.MAPPER_ACCOUNT_ID);

        SearchTemplateQuery queryTemplate = new SearchTemplateQuery();
        queryTemplate.setLimit(1);
        queryTemplate.setFilters(Lists.newArrayList(tiFilter));

        List<IObjectData> result = serviceFacade.findBySearchTemplateQueryWithFields(
                ActionContextExt.of(user).getContext(),
                EnterpriseRelationObjApiNames.OBJECT_API_NAME,
                queryTemplate,
                fields).getData();

        if (CollectionUtils.isEmpty(result)) {
            return null;
        }
        return result.get(0);
    }

    public List<InspectionRule> queryInspectionRules(List<String> ruleIds) {
        return InspectionRule.INSPECTION_RULE_LIST.stream()
                .filter(o -> ruleIds.contains(o.getRuleId()))
                .collect(Collectors.toList());
    }

    public InspectionRule queryInspectionRule(String ruleId) {
        for (InspectionRule inspectionRule : InspectionRule.INSPECTION_RULE_LIST) {
            if (inspectionRule.getRuleId().equals(ruleId)) {
                return inspectionRule;
            }
        }
        return null;
    }

    public boolean verifyJudgmentRuleId(String ruleId) {
        if (StringUtils.isBlank(ruleId)) {
            return false;
        }
        for (InspectionRule inspectionRule : InspectionRule.INSPECTION_RULE_LIST) {
            if (inspectionRule.getRuleId().equals(ruleId)) {
                return true;
            }
        }
        return false;
    }

    public IObjectData findOneSnByName(String tenantId, String name, String queryField, List<String> fields) {
        List<IObjectData> snByName = findSnByName(tenantId, name, queryField, fields);
        if (CollectionUtils.isEmpty(snByName)) {
            return null;
        }
        return snByName.get(0);
    }

    public List<IObjectData> findSnByName(String tenantId, String name, String queryField, List<String> fields) {
        IFilter snIdFilter = new Filter();
        snIdFilter.setFieldValues(Lists.newArrayList(name));
        snIdFilter.setOperator(Operator.EQ);
        snIdFilter.setFieldName(queryField);

        SearchTemplateQuery queryTemplate = new SearchTemplateQuery();
        queryTemplate.setLimit(500);
        queryTemplate.setFilters(Lists.newArrayList(snIdFilter));

        List<IObjectData> result = serviceFacade.findBySearchTemplateQueryWithFields(
                ActionContextExt.of(User.systemUser(tenantId)).getContext(),
                FMCGSerialNumberApiNames.OBJECT_API_NAME,
                queryTemplate,
                fields).getData();
        if (CollectionUtils.isEmpty(result)) {
            return null;
        }
        return result;
    }

    private Object getEntityRealValue(String groupKey, String key, String value) {
        InspectionDesignerConfigEnum inspectionDesignerConfigEnum = InspectionDesignerConfigEnum.inspectionDesignerConfigEnumMap.get(groupKey + "_" + key);
        return inspectionDesignerConfigEnum.convertValue(value);
    }

    public List<String> findAccountEnterprise(String tenantId, List<String> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return null;
        }
        IFilter mapperAccountIdFilter = new Filter();
        mapperAccountIdFilter.setFieldValues(accountIds);
        mapperAccountIdFilter.setOperator(Operator.IN);
        mapperAccountIdFilter.setFieldName(EnterpriseRelationObjApiNames.MAPPER_ACCOUNT_ID);

        List<IObjectData> enterprises = queryWithFields(tenantId, EnterpriseRelationObjApiNames.OBJECT_API_NAME,
                Lists.newArrayList(mapperAccountIdFilter),
                Lists.newArrayList(EnterpriseRelationObjApiNames.ENTERPRISE_ID, EnterpriseRelationObjApiNames.MAPPER_ACCOUNT_ID), accountIds.size(), 0);

        if (CollectionUtils.isEmpty(enterprises)) {
            return null;
        }
        return enterprises.stream()
                .filter(o -> Objects.nonNull(o.get(EnterpriseRelationObjApiNames.ENTERPRISE_ID)))
                .map(o -> o.get(EnterpriseRelationObjApiNames.ENTERPRISE_ID, String.class))
                .distinct()
                .collect(Collectors.toList());
    }
}
