package com.facishare.crm.fmcg.sales.abs;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.sales.apiname.AccountObjApiNames;
import com.facishare.crm.fmcg.sales.apiname.CommonApiNames;
import com.facishare.crm.fmcg.sales.apiname.FMCGSerialNumberApiNames;
import com.facishare.crm.fmcg.sales.apiname.InspectionRecordObjApiNames;
import com.facishare.crm.fmcg.sales.business.SnBusiness;
import com.facishare.crm.fmcg.sales.cache.AccountCache;
import com.facishare.crm.fmcg.sales.enums.MagicEnum;
import com.facishare.crm.fmcg.sales.model.inspection.InspectionVerify;
import com.facishare.crm.fmcg.sales.utils.I18nUtil;
import com.facishare.crm.fmcg.sales.utils.MetadataUtil;
import com.facishare.fcp.util.MD5Util;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fmcg.framework.http.MengNiuProxy;
import com.fmcg.framework.http.contract.mengniu.QueryByDeliveryOrderIds;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import static com.facishare.crm.fmcg.sales.apiname.FmcgSalesOrderGrayConfig.CONFIG_GRAY_REF_FMCG;

/**
 * <AUTHOR> liuhaoyu
 * @create : 2024-07-25 10:56
 **/
@Slf4j
@Component("mengNiuHandleFmcgSerialNumberAction")
public class MengNiuHandleFmcgSerialNumberAction extends AbstractionHandleFmcgSerialNumberAction {
    public static final String ORDER_SUM_QUANTITY_API_NAME = "order_sum_quantity__c";

    public static final String CLIENT_ID;
    public static final String SK;

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private MengNiuProxy mengNiuProxy;
    @Resource
    private AccountCache accountCache;
    @Resource
    private SnBusiness snBusiness;

    static {
        CLIENT_ID = ConfigFactory.getConfig(CONFIG_GRAY_REF_FMCG).get("sales_mengniu_openapi_client_id");
        SK = ConfigFactory.getConfig(CONFIG_GRAY_REF_FMCG).get("sales_mengniu_openapi_sk");
    }

    @Override
    public void fillData(InspectionVerify.Context context) {
        //外部过来的码，再使用接口处理归属客户
        if ("external".equals(context.getCodeSource())) {
            findSnBelongAccountFromExternalByMarkCodeSource(context);
        } else {
            //系统内的码，使用交货单逻辑判断，没有交货单也用外接口处理归属客户
            if (!context.getObjectData().containsField(FMCGSerialNumberApiNames.DELIVERY_NUMBER) || Objects.isNull(context.getObjectData().get(FMCGSerialNumberApiNames.DELIVERY_NUMBER))) {
                findSnBelongAccountFromExternalByMarkCodeSource(context);
                return;
            }
            Boolean exist = findSnBelongAccountFromExternal(context);
            if (exist) {
                return;
            }
            findSnBelongAccountFromLocal(context);
        }
    }

    private void findSnBelongAccountFromExternalByMarkCodeSource(InspectionVerify.Context context) {
        String sourceCode = context.getSourceCode();
        JSONObject markCodeSource = snBusiness.queryMarkCodeSource(sourceCode);
        context.getFsStopWatch().lap("queryMarkCodeSource");
        if (Objects.isNull(markCodeSource) || Objects.isNull(markCodeSource.get("storageFlowInfoVoList"))
                || CollectionUtils.isEmpty(markCodeSource.getJSONArray("storageFlowInfoVoList"))) {
            super.fillCausesUnknown(context, I18nUtil.get(MagicEnum.belongEnterpriseUnknown));
            return;
        }
        List<JSONObject> storageFlowInfoVoList = markCodeSource.getJSONArray("storageFlowInfoVoList").toJavaList(JSONObject.class);
        for (JSONObject storageFlowInfoVo : storageFlowInfoVoList) {
            String billType = storageFlowInfoVo.getString("billType");
            if (!"203".equals(billType)) {
                continue;
            }
            String belongAccountId = findBelongAccount(context, storageFlowInfoVo);
            context.getFsStopWatch().lap("findBelongAccount");
            if (StringUtils.isEmpty(belongAccountId)) {
                super.fillCausesUnknown(context, I18nUtil.get(MagicEnum.belongEnterpriseUnknown));
            } else {
                context.getObjectData().set(InspectionRecordObjApiNames.BELONG_ACCOUNT_ID, belongAccountId);
            }
            break;
        }
    }

    private String findBelongAccount(InspectionVerify.Context context, JSONObject storageFlowInfoVo) {
        String sellOrgName = storageFlowInfoVo.getString("sellOrgName");
        if (StringUtils.isEmpty(sellOrgName)) {
            return null;
        }
        String[] split = sellOrgName.split(" ");
        if (split.length < 2) {
            return null;
        }
        String accountCode = split[1];
        List<IObjectData> dataList = snBusiness.queryWithFields(context.getUpstreamTenantId(), AccountObjApiNames.OBJECT_API_NAME,
                Lists.newArrayList(MetadataUtil.filter("account_no__c", Operator.EQ, Lists.newArrayList(accountCode))),
                Lists.newArrayList(CommonApiNames.ID));
        if (CollectionUtils.isEmpty(dataList)) {
            return null;
        }
        return dataList.get(0).getId();
    }

    private void findSnBelongAccountFromLocal(InspectionVerify.Context context) {
        String deliveryNumber = context.getObjectData().get(FMCGSerialNumberApiNames.DELIVERY_NUMBER, String.class);
        IFilter deliveryNumberFilter = new Filter();
        deliveryNumberFilter.setFieldValues(Lists.newArrayList(deliveryNumber));
        deliveryNumberFilter.setOperator(Operator.EQ);
        deliveryNumberFilter.setFieldName("delivery_number__c");

        SearchTemplateQuery queryTemplate = new SearchTemplateQuery();
        queryTemplate.setLimit(1);
        queryTemplate.setFilters(Lists.newArrayList(deliveryNumberFilter));

        List<IObjectData> result = serviceFacade.findBySearchTemplateQueryWithFields(
                ActionContextExt.of(User.systemUser(context.getUpstreamTenantId())).getContext(),
                "deliver_order__c",
                queryTemplate,
                Lists.newArrayList(CommonApiNames.ID, "customer__c", "distributor__c")).getData();
        context.getFsStopWatch().lap("searchDeliveryOrderFromLocal");

        if (CollectionUtils.isEmpty(result)) {
            super.fillCausesUnknown(context, I18nUtil.get(MagicEnum.deliveryDataNotFound));
            return;
        }

        IObjectData deliverOrderData = result.get(0);
        if (deliverOrderData.containsField("customer__c") && Objects.nonNull(deliverOrderData.get("customer__c"))) {
            context.getObjectData().set(InspectionRecordObjApiNames.BELONG_ACCOUNT_ID, deliverOrderData.get("customer__c", String.class));
            findDeliveryOrderDetail(context, result.get(0).getId());
        } else {
            super.fillCausesUnknown(context, I18nUtil.get(MagicEnum.belongEnterpriseUnknown));
        }
    }

    private void findDeliveryOrderDetail(InspectionVerify.Context context, String deliveryNumberId) {
        IFilter idFilter = new Filter();
        idFilter.setFieldValues(Lists.newArrayList(deliveryNumberId));
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldName("field_cYLCg__c");

        SearchTemplateQuery queryTemplate = new SearchTemplateQuery();
        queryTemplate.setLimit(500);
        queryTemplate.setFilters(Lists.newArrayList(idFilter));

        List<IObjectData> result = serviceFacade.findBySearchTemplateQueryWithFields(
                ActionContextExt.of(User.systemUser(context.getUpstreamTenantId())).getContext(),
                "deliver_orderdetails__c",
                queryTemplate,
                Lists.newArrayList(CommonApiNames.ID, "purchase_amount__c")).getData();
        context.getFsStopWatch().lap("findDeliveryOrderDetail");

        BigDecimal sumQuantity = result.stream()
                .map(o -> Objects.isNull(o.get("purchase_amount__c")) ? BigDecimal.ZERO : new BigDecimal(o.get("purchase_amount__c", String.class)))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        context.getObjectData().set(ORDER_SUM_QUANTITY_API_NAME, sumQuantity);
    }

    private Boolean findSnBelongAccountFromExternal(InspectionVerify.Context context) {
        String deliveryNumber = context.getObjectData().get(FMCGSerialNumberApiNames.DELIVERY_NUMBER, String.class);
        QueryByDeliveryOrderIds.Order order = searchDeliveryOrder(deliveryNumber);
        context.getFsStopWatch().lap("searchDeliveryOrderFromExternal");
        if (Objects.isNull(order)) {
            return false;
        }
        JSONObject customer = findCustomer(order, Integer.parseInt(context.getUpstreamTenantId()));
        if (StringUtils.isEmpty(customer.getString("customerId"))) {
            super.fillCausesUnknown(context, I18nUtil.get(MagicEnum.belongEnterpriseUnknown));
            return true;
        }
        context.getObjectData().set(InspectionRecordObjApiNames.BELONG_ACCOUNT_ID, customer.get("customerId"));
        BigDecimal sumQuantity = order.getItems().stream()
                .map(item -> StringUtils.isEmpty(item.getQty()) ? BigDecimal.ZERO : new BigDecimal(item.getQty()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        context.getObjectData().set(ORDER_SUM_QUANTITY_API_NAME, sumQuantity);
        return true;
    }

    private QueryByDeliveryOrderIds.Order searchDeliveryOrder(String deliveryOrderNo) {
        deliveryOrderNo = formatDeliveryOrderNo(deliveryOrderNo);
        long time = System.currentTimeMillis();
        String sign = MD5Util.toMD5Hex((CLIENT_ID + SK + time).getBytes()).toUpperCase();
        List<String> arg = Lists.newArrayList(deliveryOrderNo);
        log.info("searchDeliveryOrder arg: {}", JSON.toJSONString(arg));
        QueryByDeliveryOrderIds.Result result = mengNiuProxy.queryByDeliveryOrderIds(CLIENT_ID, sign, String.valueOf(time), arg);
        log.info("searchDeliveryOrder result: {}", JSON.toJSONString(result));
        if (result.getCode() != 200 || !result.getSuccess()) {
            return null;
        }
        List<QueryByDeliveryOrderIds.Order> data = result.getData();
        if (CollectionUtils.isEmpty(data) || Objects.isNull(data.get(0))) {
            return null;
        }
        return data.get(0);
    }

    private String formatDeliveryOrderNo(String deliveryOrderNo) {
        // 如果长度不足10位，在前面补0
        if (deliveryOrderNo.length() < 10) {
            deliveryOrderNo = String.format("%10s", deliveryOrderNo).replace(' ', '0');
        }
        return deliveryOrderNo;
    }

    private JSONObject findCustomer(QueryByDeliveryOrderIds.Order deliveryOrder, Integer tenantId) {
        JSONObject customerInfo = new JSONObject();
        //LFART 订单类型
        String type = deliveryOrder.getExt();
        //VKORG 销售组织
        String salesOrgId = deliveryOrder.getSalesOrgId();
        //VSTEL 装运点
        String transportSite = deliveryOrder.getExt9();
        //KUNNR 送达方编码
        String deliveryPartCode = deliveryOrder.getDeliveryPartCode();
        //KUNAG 售达方编码
        String soldPartCode = deliveryOrder.getSoldPartCode();

        Boolean handleVerticalStores = handleVerticalStores(tenantId, customerInfo, deliveryPartCode, soldPartCode);
        if (handleVerticalStores) {
            return customerInfo;
        }
        if ("ZTB".equals(type) || "ZUB".equals(type)) {
            virtualWarehouseCustomer(tenantId, customerInfo, deliveryPartCode);
        } else if ("6000".equals(salesOrgId) && !"R004".equals(transportSite)) {
            brandToDealerCustomer(tenantId, customerInfo, soldPartCode);
        } else {
            dealerToDistributorCustomer(tenantId, customerInfo, transportSite, deliveryPartCode);
        }
        log.info("findCustomer customerInfo {}", JSON.toJSONString(customerInfo));
        return customerInfo;
    }

    private Boolean handleVerticalStores(Integer tenantId, JSONObject result, String deliveryPartCode, String soldPartCode) {
        if (StringUtils.isEmpty(deliveryPartCode) || StringUtils.isEmpty(soldPartCode)) {
            log.info("handleVerticalStores argument is empty");
            return false;
        }

        String customerId = accountCache.get(tenantId, "seller_delivery_relation__c", deliveryPartCode, soldPartCode);
        if (StringUtils.isEmpty(customerId)) {
            log.info("handleVerticalStores account not found");
            return false;
        }
        result.put("customerId", customerId);
        result.put("verticalStoresFlag", true);
        return true;
    }

    private void dealerToDistributorCustomer(Integer tenantId, JSONObject customerInfo, String transportSite, String deliveryPartCode) {
        customerInfo.put("type", "dealerToDistributorCustomer");
        if (StringUtils.isEmpty(transportSite) || StringUtils.isEmpty(deliveryPartCode)) {
            log.info("dealerToDistributorCustomer argument is empty");
            return;
        }

        String customerId = accountCache.get(tenantId, "shipping_location__c", transportSite, null);
        if (StringUtils.isEmpty(customerId)) {
            log.info("dealerToDistributorCustomer account not found");
            return;
        }
        customerInfo.put("customerId", customerId);

        String distributorId = accountCache.get(tenantId, AccountObjApiNames.OBJECT_API_NAME, deliveryPartCode, null);
        if (StringUtils.isEmpty(distributorId)) {
            log.info("B-b account not found");
            return;
        }
        customerInfo.put("distributorId", distributorId);
    }

    private void brandToDealerCustomer(Integer tenantId, JSONObject customerInfo, String soldPartCode) {
        customerInfo.put("type", "brandToDealerCustomer");
        if (StringUtils.isEmpty(soldPartCode)) {
            log.info("brandToDealerCustomer argument is empty");
            return;
        }
        String customerId = accountCache.get(tenantId, AccountObjApiNames.OBJECT_API_NAME, soldPartCode, null);
        if (StringUtils.isEmpty(customerId)) {
            log.info("brandToDealerCustomer account not found");
            return;
        }
        customerInfo.put("customerId", customerId);
    }

    private void virtualWarehouseCustomer(Integer tenantId, JSONObject customerInfo, String deliveryPartCode) {
        customerInfo.put("type", "virtualWarehouseCustomer");
        if (StringUtils.isEmpty(deliveryPartCode)) {
            log.info("virtualWarehouseCustomer argument is empty");
            return;
        }
        String customerId = accountCache.get(tenantId, "VirtualWarehouse__c", deliveryPartCode, null);
        if (StringUtils.isEmpty(customerId)) {
            log.info("virtualWarehouseCustomer account not found");
            return;
        }
        customerInfo.put("customerId", customerId);
    }
}
