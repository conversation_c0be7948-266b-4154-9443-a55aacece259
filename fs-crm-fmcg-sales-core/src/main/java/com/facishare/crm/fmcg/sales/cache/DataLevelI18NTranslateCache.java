package com.facishare.crm.fmcg.sales.cache;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.sales.cache.model.DataLevelI18NTranslateCacheKey;
import com.facishare.crm.fmcg.sales.exception.ApiException;
import com.fmcg.framework.http.PaasAiProxy;
import com.fmcg.framework.http.contract.ai.ChatTranslate;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> l<PERSON><PERSON><PERSON>
 * @create : 2024-12-24 16:56
 **/
@Component
@Slf4j
public class DataLevelI18NTranslateCache {
    @Resource
    private PaasAiProxy paasAiProxy;
    @Resource
    private DataLevelI18nFieldSupportLanguagesCache dataLevelI18nFieldSupportLanguagesCache;

    private LoadingCache<DataLevelI18NTranslateCacheKey, JSONObject> cache;

    @PostConstruct
    public void init() {
        cache = CacheBuilder.newBuilder()
                .maximumSize(5000)
                .expireAfterWrite(1, TimeUnit.HOURS)
                .build(new CacheLoader<DataLevelI18NTranslateCacheKey, JSONObject>() {
                    @Override
                    public JSONObject load(DataLevelI18NTranslateCacheKey key) {
                        return handleTranslate(key);
                    }
                });
    }

    private JSONObject handleTranslate(DataLevelI18NTranslateCacheKey key) {
        Map<String, List<String>> supportLanguagesMap = dataLevelI18nFieldSupportLanguagesCache.getNoException(key.getTenantId(), key.getApiName());
        if (Objects.isNull(supportLanguagesMap) || !supportLanguagesMap.containsKey(key.getFieldApiName())) {
            log.info("not found supportLanguages {}", JSON.toJSONString(key));
            throw new ApiException(1000001, "not found supportLanguages");
        }
        List<String> supportLanguages = supportLanguagesMap.get(key.getFieldApiName());

        ChatTranslate.Arg arg = new ChatTranslate.Arg();
        arg.setTransEntrys(Lists.newArrayList(key.getBacklog()));
        arg.setLanguages(supportLanguages);
        ChatTranslate.Result result = paasAiProxy.chatTranslate(Integer.parseInt(key.getTenantId()), -10000, arg);
        if (result.getErrCode() != 0 || Objects.isNull(result.getResult()) || !result.getResult().getSuccess() || CollectionUtils.isEmpty(result.getResult().getResults())) {
            log.info("chatTranslate is fail {}, {}", JSON.toJSONString(arg), JSON.toJSONString(result));
            throw new ApiException(1000002, "chatTranslate is fail");
        }
        return result.getResult().getResults().get(0);
    }

    public JSONObject getNoException(String tenantId, String apiName, String fieldApiName, String backlog) {
        try {
            return cache.get(new DataLevelI18NTranslateCacheKey(tenantId, apiName, fieldApiName, backlog));
        } catch (Exception e) {
            log.error("DataLevelI18NTranslateCache getNotException is error " + tenantId + apiName, e);
        }
        return null;
    }

    public void clear(String tenantId, String apiName, String fieldApiName, String backlog) {
        DataLevelI18NTranslateCacheKey key = new DataLevelI18NTranslateCacheKey(tenantId, apiName, fieldApiName, backlog);
        if (containsKey(key)) {
            cache.invalidate(key);
        }
    }

    public boolean containsKey(DataLevelI18NTranslateCacheKey key) {
        return cache.asMap().containsKey(key);
    }
}
