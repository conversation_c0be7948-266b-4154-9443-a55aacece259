package com.facishare.crm.fmcg.sales.dao.mongo.entity;

import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Property;

import java.util.List;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @create : 2024-10-09 15:16
 **/
@Data
@ToString
public class ConditionReturnEntity {
    public static final String F_ENTITY_FIELD_NAME = "EFN";
    public static final String F_CONDITION_PATTERN = "CP";
    public static final String F_CONDITIONS = "C";
    public static final String F_RETURN_VALUE = "RV";
    public static final String F_RETURN_VALUE_TYPE = "RVT";

    @Property(F_ENTITY_FIELD_NAME)
    private String entityFieldName;
    /**
     * (0 and 1) or (2)
     */
    @Property(F_CONDITION_PATTERN)
    private String conditionPattern;
    @Embedded(F_CONDITIONS)
    private List<ConditionEntity> conditions;
    @Property(F_RETURN_VALUE)
    private Object returnValue;
    /**
     * fixed: 固定值, field: 字段(取对象上字段的值)
     */
    @Property(F_RETURN_VALUE_TYPE)
    private String returnValueType;
}
