package com.facishare.crm.fmcg.sales.dao.mongo.entity;

import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Property;

/**
 * <AUTHOR> l<PERSON><PERSON><PERSON>
 * @create : 2023-09-21 14:18
 **/
@Data
@ToString
public class StorageLocationEntity {
    public static final String F_MASTER_OBJECT_FIELD = "MOF";
    public static final String F_DETAIL_OBJECT_API_NAME = "DOAN";
    public static final String F_STORAGE_FIELD = "SF";
    public static final String F_PRODUCT_FIELD = "PF";
    public static final String F_STORAGE_TYPE = "ST";
    @Property(F_MASTER_OBJECT_FIELD)
    private String masterObjectField;
    @Property(F_DETAIL_OBJECT_API_NAME)
    private String detailObjectApiName;
    /**
     * 存储码的字段
     */
    @Property(F_STORAGE_FIELD)
    private String storageField;
    /**
     * 存储产品id的字段
     */
    @Property(F_PRODUCT_FIELD)
    private String productField;
    /**
     * 码的存储类型  array：数组，string：字符串，map：由string解析
     */
    @Property(F_STORAGE_TYPE)
    private String storageType;
}
