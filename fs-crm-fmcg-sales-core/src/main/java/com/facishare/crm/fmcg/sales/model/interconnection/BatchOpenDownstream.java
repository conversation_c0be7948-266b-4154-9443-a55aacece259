package com.facishare.crm.fmcg.sales.model.interconnection;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.api.model.BaseResult;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023-10-26 14:43
 **/
public interface BatchOpenDownstream {
    @Data
    @ToString
    class Arg {
        private ArgBody arg;
    }


    @Data
    @ToString
    class ArgBody {
        private String n;
        private String m;
        private List<String> accountIds;
        private List<String> outerRoleIds;
        private String designated;
    }

    @Data
    @ToString
    class Result extends BaseResult {
    }
}
