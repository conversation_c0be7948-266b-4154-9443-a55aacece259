package com.facishare.crm.fmcg.sales.model.init;

import com.fxiaoke.api.model.BaseResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2025/03/04 14:25
 */
public interface InitFmcgSalesField {

    @Data
    class Arg implements Serializable {
        /**
         * 企业id
         */
        private String tenantId;
        /**
         * 是否在对象detail布局中显示字段：默认true-显示，false-隐藏
         */
        private boolean showInLayout = true;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class Result extends BaseResult {

    }
}
