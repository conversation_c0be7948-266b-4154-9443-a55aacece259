package com.facishare.crm.fmcg.sales.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.sales.model.function.BatchCreateObject;
import com.facishare.crm.fmcg.sales.model.function.BatchIncrementUpdate;
import com.facishare.crm.fmcg.sales.model.function.FindByFilters;
import com.facishare.crm.fmcg.sales.model.function.FunctionBase;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.ActionContextUtil;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.contract.paas.data.PaasBatchCreate;
import com.fmcg.framework.http.contract.paas.data.PaasDataBatchIncrementUpdate;
import com.fxiaoke.common.release.GrayRelease;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
@ServiceModule("function_support")
public class FunctionSupportService {
    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private PaasDataProxy paasDataProxy;

    public String checkBaseArg(FunctionBase.Arg arg) {
        if (StringUtils.isBlank(arg.getTenantId())) {
            return "tenantId is null";
        }
        if (StringUtils.isBlank(arg.getObjectApiName())) {
            return "objectApiName is null";
        }
        return null;
    }

    @ServiceMethod("findByFilters")
    public FindByFilters.Result findByFilters(FindByFilters.Arg arg, ServiceContext serviceContext) {
        FindByFilters.Result result = new FindByFilters.Result();
        String errorMsg = checkBaseArg(arg);
        if (StringUtils.isNotBlank(errorMsg)) {
            return FunctionBase.Result.error(result, errorMsg);
        }
        List<IFilter> filters = new ArrayList<>();
        collectFilter(arg.getFilters(), filters);
        try {
            List<IObjectData> objectDataList = findObjectsByFilter(arg.getTenantId(), arg.getObjectApiName(), filters, arg.getFields(), arg.getFindAll(), arg.getOffset(), arg.getLimit());
            if (CollectionUtils.isEmpty(objectDataList)) {
                return FindByFilters.Result.success(Collections.emptyList());
            }
            List<JSONObject> dataList = objectDataList.stream().map(objectData -> {
                if (objectData instanceof ObjectData) {
                    return new JSONObject(((ObjectData) objectData).getContainerDocument());
                }
                return JSON.parseObject(objectData.toJsonString());
            }).collect(Collectors.toList());
            return FindByFilters.Result.success(dataList);
        } catch (Exception e) {
            log.error("functionSupport findByFilters exception ", e);
            return FunctionBase.Result.error(result, "Service internal error");
        }
    }

    private void collectFilter(List<FindByFilters.FilterDTO> filtersDTO, List<IFilter> filters) {
        if (CollectionUtils.isNotEmpty(filtersDTO)) {
            for (FindByFilters.FilterDTO filter : filtersDTO) {
                IFilter filterTmp = new Filter();
                filterTmp.setFieldName(filter.getFieldName());
                filterTmp.setOperator(Operator.valueOf(filter.getOperator()));
                filterTmp.setFieldValues(filter.getFieldValue());
                if (Objects.nonNull(filter.getIsMasterField())) {
                    filterTmp.setIsMasterField(filter.getIsMasterField());
                }
                if (Objects.nonNull(filter.getValueType())) {
                    filterTmp.setValueType(filter.getValueType());
                }
                filters.add(filterTmp);
            }
        }
    }

    private List<IObjectData> findObjectsByFilter(String tenantId, String apiName, List<IFilter> filters, List<String> fields, Boolean findAll, Integer offset, Integer limit) {
        if (findAll) {
            offset = 0;
            limit = 2000;
        }
        List<IObjectData> result = new ArrayList<>();
        IActionContext actionContext = ActionContextUtil.getNewContext(tenantId);
        actionContext.setUserId(User.systemUser(tenantId).getUserId());
        actionContext.setPrivilegeCheck(false);
        SearchTemplateQuery queryTemplate = new SearchTemplateQuery();
        queryTemplate.setFilters(filters);
        List<IObjectData> currentData;
        do {
            queryTemplate.setLimit(limit);
            queryTemplate.setOffset(offset);
            currentData = serviceFacade.findBySearchTemplateQueryWithFields(
                    actionContext,
                    apiName,
                    queryTemplate,
                    fields).getData();
            result.addAll(currentData);
            offset += limit;
        } while (findAll && currentData.size() == limit);
        return result;
    }

    @ServiceMethod("batchIncrementUpdate")
    public BatchIncrementUpdate.Result batchUpdate(BatchIncrementUpdate.Arg arg, ServiceContext serviceContext) {
        BatchIncrementUpdate.Result result = new BatchIncrementUpdate.Result();
        String errorMsg = checkBaseArg(arg);
        if (StringUtils.isNotBlank(errorMsg)) {
            return FunctionBase.Result.error(result, errorMsg);
        }
        if (CollectionUtils.isEmpty(arg.getDataList())) {
            return FunctionBase.Result.error(result, "dataList is empty");
        }
        try {
            PaasDataBatchIncrementUpdate.Arg batchArg = new PaasDataBatchIncrementUpdate.Arg();
            batchArg.addAll(arg.getDataList());
            PaasDataBatchIncrementUpdate.Result batchResult = paasDataProxy.batchIncrementUpdate(Integer.parseInt(arg.getTenantId()), -10000, arg.getObjectApiName(), batchArg);
            if (batchResult.getCode() != 0) {
                return FunctionBase.Result.error(result, batchResult.getMessage());
            }
            return FunctionBase.Result.success(result);
        } catch (Exception e) {
            log.error("functionSupport batchIncrementUpdate exception ", e);
            return FindByFilters.Result.error(result, "Service internal error");
        }
    }

    @ServiceMethod("batchCreateObject")
    public BatchCreateObject.Result batchCreateObject(BatchCreateObject.Arg arg, ServiceContext serviceContext) {
        BatchCreateObject.Result result = new BatchCreateObject.Result();
        String errorMsg = checkBaseArg(arg);
        if (StringUtils.isNotBlank(errorMsg)) {
            return FunctionBase.Result.error(result, errorMsg);
        }
        if (CollectionUtils.isEmpty(arg.getDataList())) {
            return FunctionBase.Result.error(result, "dataList is empty");
        }
        try {
            PaasBatchCreate.Result batchResult = paasDataProxy.batchCreate(Integer.parseInt(arg.getTenantId()), -10000, arg.getObjectApiName(), arg.getDataList());
            if (batchResult.getCode() != 0) {
                return FunctionBase.Result.error(result, batchResult.getMessage());
            }
            return FunctionBase.Result.success(result);
        } catch (Exception e) {
            log.error("functionSupport batchCreate exception ", e);
            return FindByFilters.Result.error(result, "Service internal error");
        }
    }
}
