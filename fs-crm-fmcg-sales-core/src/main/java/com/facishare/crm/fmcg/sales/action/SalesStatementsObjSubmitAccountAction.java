package com.facishare.crm.fmcg.sales.action;

import com.facishare.crm.fmcg.sales.enums.MagicEnum;
import com.facishare.crm.fmcg.sales.service.SalesStatementsService;
import com.facishare.crm.fmcg.sales.utils.I18nUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public class SalesStatementsObjSubmitAccountAction extends PreDefineAction<SalesStatementsObjSubmitAccountAction.Arg, SalesStatementsObjSubmitAccountAction.Result> {

    private IObjectData objectData;


    private final SalesStatementsService salesStatementsService = SpringUtil.getContext().getBean(SalesStatementsService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.SUBMIT_ACCOUNT.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getObjectDataId());
    }

    @Override
    protected void init() {
        super.init();
        if (CollectionUtils.notEmpty(dataList)) {
            objectData = dataList.get(0);
        }
    }

    @Override
    protected void before(SalesStatementsObjSubmitAccountAction.Arg arg) {
        super.before(arg);
        if (StringUtils.isBlank(arg.getActualPayment())) {
            return;
        }
        BigDecimal payableAmount = objectData.get("payable_amount", BigDecimal.class, BigDecimal.ZERO);
        BigDecimal actualPayment = new BigDecimal(arg.getActualPayment());
        check(payableAmount, actualPayment);
    }

    public static void check(BigDecimal payableAmount, BigDecimal actualPayment) {
        if (payableAmount.compareTo(BigDecimal.ZERO) >= 0) {
            if (actualPayment.compareTo(BigDecimal.ZERO) < 0 || actualPayment.compareTo(payableAmount) > 0) {
                throw new ValidateException(I18nUtil.get(MagicEnum.theActualPaymentShouldBeGreaterThanOrEqualToZero));
            }
        } else {
            if (actualPayment.compareTo(BigDecimal.ZERO) >= 0 || actualPayment.compareTo(payableAmount) < 0) {
                throw new ValidateException(I18nUtil.get(MagicEnum.theActualPaymentShouldBeLessThanZero));
            }
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        //修改对账单状态
        Map<String, Object> updateMap = new HashMap<>(4);
        updateMap.put("payer", Lists.newArrayList(actionContext.getUser().getUserId()));
        if (StringUtils.isNotBlank(arg.getActualPayment())) {
            updateMap.put("actual_payment", new BigDecimal(arg.getActualPayment()).add(objectData.get("actual_payment", BigDecimal.class, BigDecimal.ZERO)).stripTrailingZeros().toPlainString());
        }
        ServiceContext context = new ServiceContext(actionContext.getRequestContext(), "", "");
        IObjectData data = salesStatementsService.updateStatementsStatus(objectData, updateMap, null, objectDescribe, context);
        return Result.of(data);
    }

    @Data
    public static class Arg {
        private String objectDataId;

        private String actualPayment = "0";
    }

    @Data
    public static class Result {
        private ObjectDataDocument objectData;

        public static Result of(IObjectData objectData) {
            Result result = new Result();
            result.setObjectData(ObjectDataDocument.of(objectData));
            return result;
        }
    }
}
