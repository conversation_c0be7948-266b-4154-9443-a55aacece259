package com.facishare.crm.fmcg.sales.utils;

import com.facishare.crm.fmcg.sales.enums.ButtonAction;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.ui.layout.IButton;
import de.lab4inf.math.util.Strings;

import java.util.ArrayList;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class ButtonUtils {

    public static void deleteOtherThanTheSpecifiedButtonActions(ArrayList buttons, Set<String> specifiedButtonActions, Boolean keepTheCustomButtons) {
        buttons.removeIf(button -> {
            Map btn = (Map) (button);
            String apiName = (String) btn.get("api_name");
            if (keepTheCustomButtons && apiName.endsWith("__c")) {
                return false;
            }
            return !specifiedButtonActions.contains(btn.get("action"));
        });
    }

    private ButtonUtils() {
    }

    public static IButton buildButton(ObjectAction objectAction) {
        return buildButton(objectAction, true);
    }

    public static IButton buildButton(ObjectAction objectAction, boolean isActive) {
        IButton button = new Button();
        button.setAction(objectAction.getActionCode());
        button.setLabel(objectAction.getActionLabel());
        button.set("isActive", isActive);
        if (Strings.isNullOrEmpty(objectAction.getButtonApiName())) {
            button.setName(objectAction.getActionCode() + "_button_" + IButton.ACTION_TYPE_DEFAULT);
        } else {
            button.setName(objectAction.getButtonApiName());
        }
        button.setActionType(IButton.ACTION_TYPE_DEFAULT);
        return button;
    }

    public static IButton buildButton(ButtonAction objectAction) {
        return buildButton(objectAction, true);
    }

    public static IButton buildButton(ButtonAction objectAction, boolean isActive) {
        IButton button = new Button();
        button.setAction(objectAction.getActionCode());
        button.setLabel(objectAction.getActionLabel());
        button.set("isActive", isActive);
        if (Strings.isNullOrEmpty(objectAction.getButtonApiName())) {
            button.setName(objectAction.getActionCode() + "_button_" + IButton.ACTION_TYPE_DEFAULT);
        } else {
            button.setName(objectAction.getButtonApiName());
        }
        button.setActionType(IButton.ACTION_TYPE_DEFAULT);
        return button;
    }
}
