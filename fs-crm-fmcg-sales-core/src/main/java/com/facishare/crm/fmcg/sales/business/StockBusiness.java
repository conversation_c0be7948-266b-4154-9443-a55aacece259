package com.facishare.crm.fmcg.sales.business;

import com.alibaba.fastjson.JSONObject;
import com.fmcg.framework.http.StockProxy;
import com.fmcg.framework.http.contract.stock.StockConfig;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Component
public class StockBusiness {
    @Resource
    private StockProxy stockProxy;

    @Resource
    private MergeJedisCmd redisSupport;

    public boolean isEnableCostManagementCalculate(int tenantId) {
        String key = "sso_enableStockCostAdjustmentSwitch_" + tenantId;
        String cacheValue = redisSupport.get(key);
        if (StringUtils.isNotBlank(cacheValue)) {
            return Boolean.parseBoolean(cacheValue);
        }
        Map<String, String> stockConfig = queryStockConfig(tenantId);
        boolean isEnable = "2".equals(stockConfig.get("stock_cost_adjustment_switch"));
        if (isEnable) {
            redisSupport.setex(key, 86400L, "true");
        } else {
            redisSupport.setex(key, 3600L, "false");
        }
        return isEnable;
    }


    public boolean isEnableDeliveryNote(int tenantId) {
        String key = "sso_enableDeliveryNote_" + tenantId;
        String cacheValue = redisSupport.get(key);
        if (StringUtils.isNotBlank(cacheValue)) {
            return Boolean.parseBoolean(cacheValue);
        }
        Map<String, String> stockConfig = queryStockConfig(tenantId);
        boolean isEnable = "2".equals(stockConfig.get("delivery_note_status"));
        ;
        if (isEnable) {
            redisSupport.setex(key, 86400L, "true");
        } else {
            redisSupport.setex(key, 3600L, "false");
        }
        return isEnable;
    }

    public Map<String, String> queryStockConfig(int tenantId) {
        Map<String, String> result = Maps.newHashMap();
        StockConfig.Result stockConfig = stockProxy.queryCommonConfig(tenantId, -10000, new StockConfig.Arg());

        if (0 != stockConfig.getErrCode()) {
            return result;
        }

        for (Object value : stockConfig.getResult().getJSONArray("values")) {
            JSONObject object = (JSONObject) value;
            result.put(object.getString("key"), object.getString("value"));
        }

        return result;
    }
}
