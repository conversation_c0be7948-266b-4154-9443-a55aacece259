package com.facishare.crm.fmcg.sales.model.tool;

import com.fxiaoke.api.model.BaseResult;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @create: 2024-01-04 17:20
 **/
public interface PurgeDescribeCache {
    @Data
    @ToString
    class Arg {
        private List<String> tenantIds;
        private List<String> apiNames;
    }

    @Data
    @ToString
    class Result extends BaseResult {
        private List<String> failed;
    }
}
