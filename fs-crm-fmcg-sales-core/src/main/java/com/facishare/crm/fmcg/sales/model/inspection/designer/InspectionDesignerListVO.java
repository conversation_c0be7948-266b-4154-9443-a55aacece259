package com.facishare.crm.fmcg.sales.model.inspection.designer;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.api.model.BaseResult;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

public interface InspectionDesignerListVO {
    @Data
    @ToString
    class Arg {
        private Integer pageSize = 10;
        private Integer pageNumber = 1;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @ToString
    class Result extends BaseResult {

        List<JSONObject> dataList;

        public static Result success(List<JSONObject> dataList) {
            Result result = new Result();
            result.setErrorMsg("success");
            result.setErrorCode(0);
            result.setDataList(dataList);
            return result;
        }

    }

    /**
     * 参考
     */
    class InspectionDesignerListVo {
        /**
         * 设计器ID
         */
        private String inspectionDesignerId;
        /**
         * 稽查类型
         */
        private Integer type;
        /**
         * 类型名称
         */
        private String typeName;
        /**
         * 稽查规则ID
         */
        private String judgmentRuleId;
        /**
         * 稽查规则名称
         */
        private String judgmentRuleName;
        /**
         * 适用类型
         */
        private Integer applicableScopeType;
        /**
         * 适用类型名称
         */
        private Integer applicableScopeTypeName;
        /**
         * 描述
         */
        private String describe;
        /**
         * 创建人ID
         */
        private String createBy;
        /**
         * 创建时间
         */
        private Long createTime;
        /**
         * 修改人ID
         */
        private String modifyBy;
        /**
         * 修改时间
         */
        private Long modifyTime;
    }
}
