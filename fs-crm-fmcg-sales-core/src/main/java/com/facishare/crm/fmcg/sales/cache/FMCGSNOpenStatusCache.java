package com.facishare.crm.fmcg.sales.cache;

import com.facishare.crm.fmcg.sales.exception.ApiException;
import com.facishare.crm.fmcg.sales.model.sn.FMCGSerialNumberQueryOpenStatus;
import com.facishare.crm.fmcg.sales.service.FMCGSerialNumberService;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.fxiaoke.notifier.support.NotifierClient;
import com.google.common.base.Splitter;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Iterator;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> liu<PERSON>yu
 * @create : 2024-04-07 14:18
 **/
@Component
@Slf4j
public class FMCGSNOpenStatusCache {
    public static final String DESCRIBE_CHANGE_EVENT = "describe-change-event";
    @Resource
    private FMCGSerialNumberService fmcgSerialNumberService;

    private LoadingCache<String, FMCGSerialNumberQueryOpenStatus.Result> statusCache;

    @PostConstruct
    public void init() {
        statusCache = CacheBuilder.newBuilder()
                .maximumSize(5000)
                .expireAfterWrite(1, TimeUnit.HOURS)
                .build(new CacheLoader<String, FMCGSerialNumberQueryOpenStatus.Result>() {
                    @Override
                    public FMCGSerialNumberQueryOpenStatus.Result load(String key) {
                        return handleStatus(key);
                    }
                });

        listenDescribeChangeEventUseFastNotify();
    }

    private FMCGSerialNumberQueryOpenStatus.Result handleStatus(String key) {
        return fmcgSerialNumberService.queryFMCGSerialNumberModuleOpenStatus(null,
                new ServiceContext(RequestContext.builder().tenantId(key).build(), null, null));
    }

    private void listenDescribeChangeEventUseFastNotify() {
        NotifierClient.register(DESCRIBE_CHANGE_EVENT, (message) -> {
            dealSubMessage(message.getContent());
        });
    }

    void dealSubMessage(String message) {
        Iterator<String> it = Splitter.on('\u0004').split(message).iterator();
        String tenantId = it.next();
        String describeApiName = it.next();
        if ("FMCGSerialNumberObj".equals(describeApiName)) {
            clear(tenantId);
            log.info("statusCache clear cache {}", tenantId);
        }
    }

    public FMCGSerialNumberQueryOpenStatus.Result getNoException(String tenantId) {
        FMCGSerialNumberQueryOpenStatus.Result status;
        try {
            status = statusCache.get(tenantId);
        } catch (Exception e) {
            log.error("get statusCache is error", e);
            throw new ApiException(300001, "get statusCache is error");
        }
        return status;
    }

    public void clear(String key) {
        if (containsKey(key)) {
            statusCache.invalidate(key);
        }
    }

    public boolean containsKey(String key) {
        return statusCache.asMap().containsKey(key);
    }
}
