package com.facishare.crm.fmcg.sales.model.tool;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.api.model.BaseResult;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @create: 2024-01-22 22:11
 **/
public interface BulkDeleteDirect {
    @Data
    @ToString
    class Arg implements Serializable {
        private Map<String, DeleteData> deleteDataMap;

    }

    @Data
    @ToString
    class DeleteData implements Serializable {
        private String apiName;
        private List<String> ids;
    }

    @Data
    @ToString
    class Result extends BaseResult {
    }
}
