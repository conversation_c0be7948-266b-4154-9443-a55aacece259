package com.facishare.crm.fmcg.sales.model.inspection.designer;

import lombok.Data;
import lombok.ToString;

import java.util.Collections;
import java.util.List;

public interface CheckConfigUserSetVO {
    @Data
    @ToString
    class Arg {
        private String inspectionDesignerId;

        private List<Integer> userIds;
        private List<Integer> deptIds;
        private List<String> roleGroupIds;
        private List<String> userGroupIds;

        private List<Integer> outerTenantIds;
        private List<Integer> outerUserIds;
        private List<String> tenantGroupIds;
        private List<String> outerRoleIds;
    }


    @Data
    @ToString
    class Result {
        private List<Integer> effectiveUserIds;
        private List<Integer> unEffectiveUserIds;
        private List<HaveOtherConfig> haveHaveOtherConfigUserIds;


        public static Result emptyResult() {
            Result result = new Result();
            result.setEffectiveUserIds(Collections.emptyList());
            result.setUnEffectiveUserIds(Collections.emptyList());
            result.setHaveHaveOtherConfigUserIds(Collections.emptyList());
            return result;
        }
    }
}
