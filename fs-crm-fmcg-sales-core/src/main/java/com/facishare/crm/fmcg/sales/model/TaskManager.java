package com.facishare.crm.fmcg.sales.model;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import java.util.*;

@Slf4j
public class TaskManager {
    private final Map<String, BusinessTask> taskMap = new HashMap<>();

    public TaskManager register(String key, BusinessTask task) {
        taskMap.put(key, task);
        return this;
    }

    public Set<String> getAllTaskKeys() {
        return Collections.unmodifiableSet(taskMap.keySet());
    }

    public Map<String, TaskResult> execute(List<String> taskKeys) {
        Map<String, TaskResult> results = new LinkedHashMap<>();
        
        for (String key : taskKeys) {
            BusinessTask task = taskMap.get(key);
            if (task == null) {
                log.warn("task {} not found", key);
                continue;
            }
            
            TaskResult result = new TaskResult(key);
            try {
                task.execute();
                result.setSuccess(true);
            } catch (Exception e) {
                log.error("task {} error", key, e);
                result.setSuccess(false);
                result.setError(e);
            }
            results.put(key, result);
        }
        
        return results;
    }

    @Data
    public static class TaskResult {
        private final String taskKey;
        private boolean success;
        private Exception error;
        
        public TaskResult(String taskKey) {
            this.taskKey = taskKey;
        }

        @Override
        public String toString() {
            return "TaskResult{" +
                    "taskKey='" + taskKey + '\'' +
                    ", success=" + success +
                    ", error=" + (error != null ? error.getMessage() : "null") +
                    '}';
        }
    }
}