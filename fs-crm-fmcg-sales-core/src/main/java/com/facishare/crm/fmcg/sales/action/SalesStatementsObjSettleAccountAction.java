package com.facishare.crm.fmcg.sales.action;

import com.facishare.crm.fmcg.sales.service.SalesStatementsService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public class SalesStatementsObjSettleAccountAction extends PreDefineAction<SalesStatementsObjSettleAccountAction.Arg, SalesStatementsObjSettleAccountAction.Result> {

    private IObjectData objectData;


    private final SalesStatementsService salesStatementsService = SpringUtil.getContext().getBean(SalesStatementsService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.SETTLE_ACCOUNT.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getObjectDataId());
    }

    @Override
    protected void init() {
        super.init();
        if (CollectionUtils.notEmpty(dataList)) {
            objectData = dataList.get(0);
        }
    }

    @Override
    protected void before(SalesStatementsObjSettleAccountAction.Arg arg) {
        super.before(arg);
        if (StringUtils.isBlank(arg.getActualPayment())) {
            return;
        }
        BigDecimal payableAmount = objectData.get("payable_amount", BigDecimal.class, BigDecimal.ZERO);
        BigDecimal actualPayment = new BigDecimal(arg.getActualPayment());
        SalesStatementsObjSubmitAccountAction.check(payableAmount, actualPayment);
    }

    @Override
    protected Result doAct(Arg arg) {
        log.info("actionContext={},objectData{}", actionContext, objectData);
        //修改对账单状态
        Map<String, Object> updateMap = new HashMap<>(4);
        updateMap.put("payer", Lists.newArrayList(actionContext.getUser().getUserId()));
        String status = null;
        if (StringUtils.isNotBlank(arg.getActualPayment())) {
            updateMap.put("actual_payment", new BigDecimal(arg.getActualPayment()).add(objectData.get("actual_payment", BigDecimal.class, BigDecimal.ZERO)).stripTrailingZeros().toPlainString());
            if (new BigDecimal(arg.getActualPayment()).compareTo(objectData.get("payable_amount", BigDecimal.class, BigDecimal.ZERO)) == 0) {
                updateMap.put("statement_status", SalesStatementsObjConformanceStatementsAction.CLOSE_OFF);
                status = SalesStatementsObjConformanceStatementsAction.CLOSE_OFF;
            }
        }
        ServiceContext context = new ServiceContext(actionContext.getRequestContext(), "", "");
        IObjectData data = salesStatementsService.updateStatementsStatus(objectData, updateMap, status, objectDescribe, context);
        return Result.of(data);
    }

    @Data
    public static class Arg {
        private String objectDataId;

        private String actualPayment = "0";
    }

    @Data
    public static class Result {
        private ObjectDataDocument objectData;

        public static Result of(IObjectData objectData) {
            Result result = new Result();
            result.setObjectData(ObjectDataDocument.of(objectData));
            return result;
        }
    }
}
