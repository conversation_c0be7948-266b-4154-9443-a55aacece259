package com.facishare.crm.fmcg.sales.model.inspection;

import com.fxiaoke.api.model.BaseResult;
import lombok.Data;
import lombok.ToString;

/**
 * @author: liu<PERSON><PERSON>
 * @create: 2025-04-14 18:41
 **/
public interface Distance {
    @Data
    @ToString
    class Arg {
        private String location1;
        private String location2;
    }

    @Data
    @ToString
    class Result extends BaseResult {
        private Double distance;

        public static Distance.Result success(Double distance) {
            Distance.Result result = new Distance.Result();
            result.setDistance(distance);
            result.setErrorCode(0);
            result.setErrorMsg("success");
            return result;
        }

        public static Distance.Result error(Integer errorCode, String errorMsg) {
            Distance.Result result = new Distance.Result();
            result.setErrorCode(errorCode);
            result.setErrorMsg(errorMsg);
            return result;
        }
    }
}
