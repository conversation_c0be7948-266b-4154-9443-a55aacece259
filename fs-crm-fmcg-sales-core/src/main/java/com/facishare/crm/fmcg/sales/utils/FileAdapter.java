package com.facishare.crm.fmcg.sales.utils;

import com.facishare.crm.fmcg.sales.exception.ApiException;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.StoneFileImageProcessRequest;
import com.facishare.stone.sdk.request.StoneFileUploadRequest;
import com.facishare.stone.sdk.response.StoneFileUploadResponse;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;

@Slf4j
@Component
public class FileAdapter {
    @Resource
    private StoneProxyApi stoneProxyApi;

    public String uploadPicture(String ea, String pictureUrl) {
        String path = "";
        try {
            // 创建URL对象
            URL url = new URL(pictureUrl);
            // 打开连接
            InputStream inputStream = url.openConnection().getInputStream();
            // 读取图片数据
            ByteArrayOutputStream output = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                output.write(buffer, 0, bytesRead);
            }

            inputStream.close();
            byte[] imageBytes = output.toByteArray();
            path = uploadFile(ea, 1000, imageBytes, "png");

            // 处理图片...
        } catch (IOException e) {
            e.printStackTrace();
        }
        return path;
    }

    private String uploadFile(String ea, Integer employeeId, byte[] fileStream, String suffix) {
        try {
            StoneFileUploadRequest uploadRequest = new StoneFileUploadRequest();
            byte[] bytes = fileStream;
            InputStream inputStream = new ByteArrayInputStream(bytes);
            uploadRequest.setImageProcessRequest(new StoneFileImageProcessRequest());
            uploadRequest.setNeedThumbnail(false);
            uploadRequest.setKeepFormat(true);
            uploadRequest.setFileSize(bytes.length);
            uploadRequest.setSecurityGroup("");
            uploadRequest.setPermissions(Lists.newArrayList());
            uploadRequest.setGlobal(false);
            uploadRequest.setExtensionName(suffix);
            uploadRequest.setNamedPath("");
            uploadRequest.setEa(ea);
            uploadRequest.setEmployeeId(employeeId);
            uploadRequest.setBusiness("FMCG-SALES");
            StoneFileUploadResponse uploadResponse = stoneProxyApi.uploadByStream("n", uploadRequest, inputStream);
            return uploadResponse.getPath().endsWith(suffix.toLowerCase()) ? uploadResponse.getPath() : uploadResponse.getPath() + "." + suffix.toLowerCase();
        } catch (Exception ex) {
            log.error("upload file err.", ex);
            throw new ApiException(600514, ex.getMessage());
        }
    }


}
