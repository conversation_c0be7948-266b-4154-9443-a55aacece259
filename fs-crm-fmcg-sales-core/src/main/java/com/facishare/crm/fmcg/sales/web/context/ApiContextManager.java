package com.facishare.crm.fmcg.sales.web.context;


public class ApiContextManager {

    private static final ThreadLocal<ApiContext> THREAD_LOCAL = new ThreadLocal<>();


    private ApiContextManager() {
    }

    public static ApiContext getContext() {
        return THREAD_LOCAL.get();
    }

    public static void setContext(ApiContext requestContext) {
        if (requestContext == null) {
            return;
        }
        THREAD_LOCAL.set(requestContext);
    }

    public static void removeContext() {
        THREAD_LOCAL.remove();
    }
}
