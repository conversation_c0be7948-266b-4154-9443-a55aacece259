package com.facishare.crm.fmcg.sales.factory;

import com.facishare.crm.fmcg.sales.abs.abstraction.HandleFmcgSerialNumberAction;
import com.facishare.crm.fmcg.sales.abs.abstraction.InspectionRuleVerifyAction;
import com.facishare.crm.fmcg.sales.exception.ApiException;
import com.facishare.crm.fmcg.sales.model.inspection.InspectionVerify;
import com.facishare.crm.fmcg.sales.utils.FMCGConfigUtil;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> liuhaoyu
 * @create : 2024-07-25 11:05
 **/
@Slf4j
public class HandleFmcgSerialNumberActionFactory {
    private final static String ACTION_NAME = "HandleFmcgSerialNumberAction";

    private static final Map<String, HandleFmcgSerialNumberAction<InspectionVerify.Context>> HANDLE_FMCG_SERIAL_NUMBER_ACTION_MAP;

    static {
        HANDLE_FMCG_SERIAL_NUMBER_ACTION_MAP = Maps.newHashMap();
        Map<String, String> salesActionBeanNameMap = FMCGConfigUtil.getSalesActionBeanNameMap();
        for (Map.Entry<String, String> entry : salesActionBeanNameMap.entrySet()) {
            HandleFmcgSerialNumberAction<InspectionVerify.Context> bean = SpringContextHolder.getBean(String.format("%s%s", entry.getValue(), ACTION_NAME));
            if (Objects.nonNull(bean)) {
                HANDLE_FMCG_SERIAL_NUMBER_ACTION_MAP.put(entry.getKey(), bean);
            }
        }
    }

    public static HandleFmcgSerialNumberAction<InspectionVerify.Context> resolve(String tenantId) {
        HandleFmcgSerialNumberAction<InspectionVerify.Context> bean;
        Map<String, String> salesActionBeanNameMap = FMCGConfigUtil.getSalesActionBeanNameMap();
        for (String configKey : salesActionBeanNameMap.keySet()) {
            if (GrayRelease.isAllow("fmcg", configKey, tenantId)) {
                if (!containsBean(configKey)) {
                    throw new ApiException(100001, "current enterprise is not config HandleFmcgSerialNumberAction");
                }
                return getBean(configKey);
            }
        }
        throw new ApiException(100001, "current enterprise is not config HandleFmcgSerialNumberAction");
    }

    private static boolean containsBean(String configKey) {
        return HANDLE_FMCG_SERIAL_NUMBER_ACTION_MAP.containsKey(configKey);
    }

    private static HandleFmcgSerialNumberAction<InspectionVerify.Context> getBean(String configKey) {
        return HANDLE_FMCG_SERIAL_NUMBER_ACTION_MAP.get(configKey);
    }
}
