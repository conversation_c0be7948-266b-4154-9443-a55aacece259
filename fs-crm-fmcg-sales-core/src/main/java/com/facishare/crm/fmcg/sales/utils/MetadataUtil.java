package com.facishare.crm.fmcg.sales.utils;

import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;

import java.util.List;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @create : 2025-06-20 10:58
 **/
public class MetadataUtil {
    public static IFilter filter(String fieldName, Operator operator, List<String> fieldValues) {
        IFilter filter = new Filter();
        filter.setFieldName(fieldName);
        filter.setOperator(operator);
        filter.setFieldValues(fieldValues);
        return filter;
    }
}
