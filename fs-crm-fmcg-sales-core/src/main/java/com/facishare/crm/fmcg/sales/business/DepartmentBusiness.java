package com.facishare.crm.fmcg.sales.business;

import com.facishare.organization.adapter.api.model.biz.RunStatus;
import com.facishare.organization.adapter.api.model.biz.departmentmember.MainDepartment;
import com.facishare.organization.adapter.api.model.biz.employee.arg.BatchGetEmployeeIdsByDepartmentIdsArg;
import com.facishare.organization.adapter.api.service.EmployeeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DepartmentBusiness {

    @Resource
    private EmployeeService dubboEmployeeService;

    /**
     * 批量获取部门下面的所有员工id
     */
    public List<Integer> getEmployeeIDsByDepartmentIds(int tenantId, List<Integer> departmentIds) {
        return getEmployeeIDsByDepartmentIds(tenantId, departmentIds, RunStatus.ACTIVE);
    }

    public List<Integer> getEmployeeIDsByDepartmentIds(int tenantId, List<Integer> departmentIds, RunStatus runStatus) {
        try {
            return batchGetEmployeeIdsByDepartmentIds(tenantId, departmentIds, MainDepartment.ALL, runStatus);
        } catch (Exception e) {
            log.error("getEmployeeIDsByDepartmentIds arg:[{},{}] error:[{}]", tenantId, departmentIds.toString(), e.getMessage());
            throw e;
        }

    }

    private List<Integer> batchGetEmployeeIdsByDepartmentIds(int tenantId, List<Integer> circleIds, MainDepartment mainDepartment, com.facishare.organization.adapter.api.model.biz.RunStatus runStatus) {
        if (CollectionUtils.isEmpty(circleIds)) {
            return new ArrayList<>();
        }
        BatchGetEmployeeIdsByDepartmentIdsArg arg = new BatchGetEmployeeIdsByDepartmentIdsArg();
        arg.setDepartmentIds(circleIds);
        arg.setIncludeLowDepartment(true);
        arg.setRunStatus(runStatus);
        arg.setMainDepartment(mainDepartment);
        arg.setEnterpriseId(tenantId);
        log.info("getEmployeeIDsByCircleIDsNew ea {}", tenantId);
        return dubboEmployeeService.batchGetEmployeeIdsByDepartmentIds(arg).getEmployeeIds();
    }
}
