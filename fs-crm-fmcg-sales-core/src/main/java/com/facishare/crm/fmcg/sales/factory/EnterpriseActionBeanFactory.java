package com.facishare.crm.fmcg.sales.factory;

import com.alibaba.fastjson.TypeReference;
import com.facishare.crm.fmcg.sales.abs.abstraction.FillExtraFieldAction;
import com.facishare.crm.fmcg.sales.abs.abstraction.HandleFmcgSerialNumberAction;
import com.facishare.crm.fmcg.sales.exception.BeanFactoryException;
import com.facishare.crm.fmcg.sales.utils.FMCGConfigUtil;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> liu<PERSON>yu
 * @create : 2025-03-25 11:08
 **/
@Slf4j
@Component
public class EnterpriseActionBeanFactory {
    private static final Map<Class<?>, Map<String, Object>> ACTION_BEAN_MAP = Maps.newConcurrentMap();

    @PostConstruct
    public void init() {
        log.info("EnterpriseActionBeanFactory init start");
        initActionBeans(HandleFmcgSerialNumberAction.class);
        initActionBeans(FillExtraFieldAction.class);
        log.info("EnterpriseActionBeanFactory init end");
    }

    private static <T> void initActionBeans(Class<T> actionClass) {
        try {
            String actionName = actionClass.getSimpleName();

            String normalBeanName = String.format("normal%s", actionName);
            if (SpringContextHolder.containsBean(normalBeanName)) {
                T normalBean = SpringContextHolder.getBean(normalBeanName);
                if (Objects.nonNull(normalBean)) {
                    registerActionBean(actionClass, "normal", normalBean);
                }
            }

            Map<String, String> salesActionBeanNameMap = FMCGConfigUtil.getSalesActionBeanNameMap();
            for (Map.Entry<String, String> entry : salesActionBeanNameMap.entrySet()) {
                String beanName = String.format("%s%s", entry.getValue(), actionName);
                if (SpringContextHolder.containsBean(beanName)) {
                    T bean = SpringContextHolder.getBean(beanName);
                    if (Objects.nonNull(bean)) {
                        registerActionBean(actionClass, entry.getKey(), bean);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Failed to initialize action beans for class: {}", actionClass.getSimpleName(), e);
        }
    }

    private static <T> void registerActionBean(Class<T> actionClass, String configKey, T bean) {
        ACTION_BEAN_MAP.computeIfAbsent(actionClass, k -> Maps.newHashMap())
                .put(configKey, bean);
    }

    @SuppressWarnings("unchecked")
    public static <T> T resolve(String tenantId, TypeReference<T> typeReference) {
        Type type = typeReference.getType();
        if (type instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) type;
            Class<?> rawType = (Class<?>) parameterizedType.getRawType();
            return (T) resolve(tenantId, rawType);
        }
        return resolve(tenantId, (Class<T>) type);
    }

    public static <T> T resolve(String tenantId, Class<T> actionClass) {
        Map<String, Object> actionMap = ACTION_BEAN_MAP.get(actionClass);
        if (actionMap == null || actionMap.isEmpty()) {
            throw new BeanFactoryException(100001, "No action beans registered for type: " + actionClass.getName());
        }

        Map<String, String> salesActionBeanNameMap = FMCGConfigUtil.getSalesActionBeanNameMap();
        for (String configKey : salesActionBeanNameMap.keySet()) {
            if (GrayRelease.isAllow("fmcg", configKey, tenantId)) {
                if (containsBean(actionClass, configKey)) {
                    return getBean(actionClass, configKey);
                }
            }
        }

        if (containsBean(actionClass, "normal")) {
            return getBean(actionClass, "normal");
        }

        throw new BeanFactoryException(100002, "No suitable action bean found for type: " + actionClass.getName());
    }

    private static <T> boolean containsBean(Class<T> actionClass, String configKey) {
        Map<String, Object> actionMap = ACTION_BEAN_MAP.get(actionClass);
        return actionMap != null && actionMap.containsKey(configKey);
    }

    private static <T> T getBean(Class<T> actionClass, String configKey) {
        Map<String, Object> actionMap = ACTION_BEAN_MAP.get(actionClass);
        return actionClass.cast(actionMap.get(configKey));
    }
}
