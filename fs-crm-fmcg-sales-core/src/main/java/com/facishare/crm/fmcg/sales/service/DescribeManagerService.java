package com.facishare.crm.fmcg.sales.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.sales.enums.LayoutType;
import com.facishare.crm.fmcg.sales.utils.I18NSimpleUtils;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.service.ButtonService;
import com.facishare.paas.appframework.core.predef.service.dto.button.CreateButton;
import com.facishare.paas.appframework.metadata.FieldLayoutPojo;
import com.facishare.paas.appframework.metadata.LayoutLogicService;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.ILayoutService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.UdefButton;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.TableColumn;
import com.facishare.paas.metadata.impl.ui.layout.component.TableComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.ui.layout.ITableColumn;
import com.fxiaoke.notifier.support.NotifierClient;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DescribeManagerService {

    public static final String DETAIL_LAYOUT_TYPE = "detail";

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private ILayoutService layoutService;

    @Resource
    private ButtonService buttonService;

    @Resource
    private LayoutLogicService layoutLogicService;

    public void createLayout(User user, String objectApiName, LayoutType layoutType) throws IOException {
        log.info("create layout user={} objectApiName={}, layoutType={}", user, objectApiName, layoutType);
        //校验是否存在
        String defaultListLayoutApiName = "list_layout_default";
        ILayout listLayout = layoutLogicService.findLayoutByApiName(user, defaultListLayoutApiName, objectApiName);
        if (Objects.isNull(listLayout)) {
            log.info("no listLayout");
            String listLayoutJson = readJsonFileContent(objectApiName, layoutType);
            LayoutDocument layoutDocument = LayoutDocument.of(JSONObject.parseObject(listLayoutJson));
            if (Objects.isNull(layoutDocument)) {
                throw new MetaDataException("listLayoutJson maybe blank, listLayoutJson=" + listLayoutJson);
            }
            ILayout defaultListLayout = layoutDocument.toLayout();
            defaultListLayout.set("api_name", defaultListLayoutApiName);
            layoutLogicService.createLayout(user, defaultListLayout, false);
        }
    }

    public String readJsonFileContent(String objectApiName, LayoutType layoutType) {
        try {
            String resourceLocation = String.format("classpath:sales/layout/%s.%s.json", objectApiName, layoutType.getLayoutType());
            File file = ResourceUtils.getFile(resourceLocation);
            return new String(Files.readAllBytes(file.toPath()));
        } catch (IOException e) {
            throw new MetaDataException("read json file error: " + e.getMessage());
        }
    }

    public void addFields(String tenantId, String objectApiName, String... fieldApiNames) {
        addFields(true, tenantId, objectApiName, fieldApiNames);
    }

    public void addFields(boolean showInLayout, String tenantId, String objectApiName, String... fieldApiNames) {
        IObjectDescribe describe = serviceFacade.findObject(tenantId, objectApiName);
        for (String fieldApiName : fieldApiNames) {
            addField(showInLayout, tenantId, describe, fieldApiName);
        }
        clearDescribeCache(tenantId, describe.getApiName());
    }

    private void clearDescribeCache(String tenantId, String apiName) {
        NotifierClient.send("describe-extra-clear-room", String.format("%s_%s", tenantId, apiName));
    }

    private void addField(boolean showInLayout, String tenantId, IObjectDescribe describe, String fieldApiName) {
        if (!describe.containsField(fieldApiName)) {
            addFieldFromFileResource(showInLayout, tenantId, describe, fieldApiName);
        }
    }

    private void addFieldFromFileResource(boolean showInLayout, String tenantId, IObjectDescribe describe, String fieldApiName) {
        String fieldDescribeJson = loadFieldDescribeJsonFromResource(describe.getApiName(), fieldApiName);
        doAddField(showInLayout, tenantId, describe, fieldDescribeJson, fieldApiName);
    }

    private String loadFieldDescribeJsonFromResource(String objectApiName, String fieldApiName) {
        try {
            File file = ResourceUtils.getFile(String.format("classpath:sales/module_fields/%s.%s.json", objectApiName, fieldApiName));
            return new String(Files.readAllBytes(file.toPath()));
        } catch (IOException e) {
            throw new MetaDataException(String.format("read field[%s-%s] describe from file cause io exception.", objectApiName, fieldApiName));
        }
    }

    private void doAddField(boolean showInLayout, String tenantId, IObjectDescribe describe, String fieldDescribe, String fieldApiName) {
        User superUser = User.systemUser(tenantId);

        JSONObject field = JSON.parseObject(fieldDescribe);
        String labelI18nKey = String.format("%s.field.%s.label", describe.getApiName(), fieldApiName);
        field.put("label", I18NSimpleUtils.get(labelI18nKey, TraceContext.get().getLocale(), field.getString("label")));
        ILayout layout = null;
        try {
            layout = layoutLogicService.findDefaultLayout(superUser, DETAIL_LAYOUT_TYPE, describe.getApiName());
        } catch (Exception ignore) {
        }
        List<FieldLayoutPojo> layoutPojoList = new ArrayList<>();
        if (Objects.nonNull(layout)) {
            FieldLayoutPojo fieldLayout = new FieldLayoutPojo();
            fieldLayout.setApiName(layout.getName());
            fieldLayout.setLabel(field.getString(IFieldDescribe.LABEL));
            fieldLayout.setRenderType(field.getString(IFieldDescribe.TYPE));
            fieldLayout.setReadonly(Boolean.TRUE.equals(field.getBoolean("is_readonly")));
            fieldLayout.setRequired(false);
            fieldLayout.setShow(showInLayout);
            fieldLayout.setLayoutType(DETAIL_LAYOUT_TYPE);
            layoutPojoList.add(fieldLayout);
        }

        serviceFacade.addDescribeCustomField(superUser,
                describe.getApiName(),
                fieldDescribe,
                layoutPojoList,
                Lists.newArrayList());
    }

    public void addFieldsToListLayoutTableComponent(String tenantId, String objectApiName, String componentName, String... fieldApiNames) throws MetadataServiceException {
        List<Layout> layoutList = layoutService.findByTypes(tenantId, Lists.newArrayList(ILayout.LIST_LAYOUT_TYPE), objectApiName, null, new ActionContext());
        if (CollectionUtils.isEmpty(layoutList)) {
            return;
        }

        List<ITableColumn> tableColumnList = Lists.newArrayList();
        for (String fieldApiName : fieldApiNames) {
            String fieldDescJson = loadFieldDescribeJsonFromResource(objectApiName, fieldApiName);
            tableColumnList.add(getTableColumn(fieldDescJson));
        }

        for (Layout layout : layoutList) {
            doAddField(layout, componentName, tableColumnList);
            layoutService.replace(layout);
        }
    }

    private ITableColumn getTableColumn(String columnDescJson) {
        JSONObject columnInfo = JSON.parseObject(columnDescJson);
        ITableColumn tableColumn = new TableColumn();
        tableColumn.set(IFieldDescribe.IS_REQUIRED, columnInfo.getBooleanValue(IFieldDescribe.IS_REQUIRED));
        tableColumn.set(IFieldDescribe.API_NAME, columnInfo.getString(IFieldDescribe.API_NAME));
        tableColumn.setLabelName(columnInfo.getString(IFieldDescribe.LABEL));
        tableColumn.setRenderType(columnInfo.getString(IFieldDescribe.TYPE));
        return tableColumn;
    }

    private void doAddField(ILayout layout, String componentName, List<ITableColumn> tableColumnList) throws MetadataServiceException {
        layout.getComponents().forEach(component -> {
            if (Objects.equals(component.getName(), componentName)) {
                TableComponent tableComponent = (TableComponent) component;
                Set<String> columnApiNameSet = tableComponent.getIncludeFields().stream().map(tc -> (String) tc.get(IFieldDescribe.API_NAME)).collect(Collectors.toSet());

                List<ITableColumn> columnToRemove = Lists.newArrayList();
                for (ITableColumn tableColumn : tableColumnList) {
                    Object columnApiName = tableColumn.get(IFieldDescribe.API_NAME);
                    if (Objects.isNull(columnApiName) || columnApiNameSet.contains(columnApiName.toString())) {
                        columnToRemove.add(tableColumn);
                    }
                }
                tableColumnList.removeAll(columnToRemove);
                tableColumnList.forEach(tableComponent::addField);
            }
        });
    }

    public void createButton(String tenantId, String objectApiName, List<String> buttonApiNames, List<String> roleList) {
        List<IUdefButton> buttonList = serviceFacade.findButtonList(User.builder().tenantId(tenantId).userId("-10000").build(), objectApiName);
        Set<String> activeButtonApiNames = buttonList.stream().map(IUdefButton::getApiName).collect(Collectors.toSet());
        for (String buttonApiName : buttonApiNames) {
            if (!activeButtonApiNames.contains(buttonApiName)) {
                String buttonJsonString = loadButtonDescribeJsonFromResource(objectApiName, buttonApiName);
                IUdefButton button = new UdefButton();
                button.fromJsonString(buttonJsonString);

                button.setTenantId(tenantId);
                RequestContext.RequestContextBuilder requestContextBuilder = RequestContext.builder();
                requestContextBuilder.tenantId(tenantId);
                requestContextBuilder.user(User.systemUser(tenantId));
                requestContextBuilder.contentType(RequestContext.ContentType.FULL_JSON);
                requestContextBuilder.requestSource(RequestContext.RequestSource.CEP);
                RequestContext requestContext = requestContextBuilder.build();

                ServiceContext serviceContext = new ServiceContext(requestContext, "button", "create");
                CreateButton.Arg createButtonArg = new CreateButton.Arg();

                createButtonArg.setRoles(roleList);
                createButtonArg.setButton(button.toJsonString());
                buttonService.create(createButtonArg, serviceContext);
            }
        }
    }


    private String loadButtonDescribeJsonFromResource(String objectApiName, String fieldApiName) {
        try {
            File file = ResourceUtils.getFile(String.format("classpath:sales/button/%s.%s.json", objectApiName, fieldApiName));
            return new String(Files.readAllBytes(file.toPath()));
        } catch (IOException e) {
            throw new MetaDataException(String.format("read field[%s-%s] describe from file cause io exception.", objectApiName, fieldApiName));
        }
    }
}
