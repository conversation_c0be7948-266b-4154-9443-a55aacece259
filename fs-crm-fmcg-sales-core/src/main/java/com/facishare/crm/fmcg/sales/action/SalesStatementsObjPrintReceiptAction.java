package com.facishare.crm.fmcg.sales.action;

import com.beust.jcommander.internal.Lists;
import com.beust.jcommander.internal.Maps;
import com.facishare.crm.fmcg.sales.model.statement.SalesStatementsDetail;
import com.facishare.crm.fmcg.sales.service.SalesStatementsService;
import com.facishare.crm.fmcg.sales.utils.I18NSimpleUtils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.uc.api.model.enterprise.arg.GetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetSimpleEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fmcg.framework.http.FmcgConfigProxy;
import com.fmcg.framework.http.contract.fmcg.TenantConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
//IgnoreI18nFile
public class SalesStatementsObjPrintReceiptAction extends PreDefineAction<SalesStatementsObjPrintReceiptAction.Arg, SalesStatementsObjPrintReceiptAction.Result> {

    private IObjectData objectData;

    private final SalesStatementsService salesStatementsService = SpringUtil.getContext().getBean(SalesStatementsService.class);
    private final EnterpriseEditionService enterpriseEditionService = SpringUtil.getContext().getBean(EnterpriseEditionService.class);
    private final FmcgConfigProxy fmcgConfigProxy = SpringUtil.getContext().getBean(FmcgConfigProxy.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.Print_Receipt.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getObjectDataId());
    }

    @Override
    protected void init() {
        super.init();
        if (CollectionUtils.notEmpty(dataList)) {
            objectData = dataList.get(0);
        }
    }


    @Override
    protected Result doAct(Arg arg) {
        SalesStatementsDetail.Arg arg1 = new SalesStatementsDetail.Arg();
        arg1.setDataId(arg.getObjectDataId());
        ServiceContext context = new ServiceContext(actionContext.getRequestContext(), "", "");
        String templateConfig = getTemplateConfig(Integer.parseInt(context.getTenantId()));
        if ("1".equals(templateConfig)) {
            return Result.success("bundle://metadata/detail/BleEscPrintPage", buildTicketData(objectData));
        }
        SalesStatementsDetail.Result detail = salesStatementsService.detail(arg1, context);
        if (detail.getErrorCode() == 0) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            //头部信息
            Map<String, Object> ticketHeader = new HashMap<>(4);
            ticketHeader.put("title", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.head.title", context.getLang().getValue(), "销售日结单"));
            ticketHeader.put("topList", Lists.newArrayList("tenantName"));
            ticketHeader.put("PrintData", Maps.newHashMap("tenantName", getEnterpriseName()));
            //基础汇总信息
            Map<String, Object> baseInfoList = new HashMap<>(8);
            baseInfoList.put("title", "");
            baseInfoList.put("topList", Lists.newArrayList("salesman", "date"));
            baseInfoList.put("PrintData", Maps.newHashMap(
                    "salesman", getSalesName(),
                    "date", simpleDateFormat.format(objectData.get("statement_date", Date.class))));
            //productLable  对方定义
            baseInfoList.put("productLable", Maps.newHashMap(
                    "salesman", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.baseInfo.salesman", context.getLang().getValue(), "业务员"),
                    "date", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.baseInfo.date", context.getLang().getValue(), "账单日期")));
            //汇总信息
            Map<String, Object> collectInfoList = new HashMap<>(8);
            collectInfoList.put("title", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.collect.title", context.getLang(), "汇总信息"));
            collectInfoList.put("topList", Lists.newArrayList("sales_amount", "sales_received_amount", "sales_return_amount", "refunded_amount", "owed_amount", "a6"));
            collectInfoList.put("PrintData", Maps.newHashMap(
                    "sales_amount", objectData.get("sales_amount", String.class, "0"),
                    "sales_received_amount", objectData.get("received_amount", String.class, "0"),
                    "sales_return_amount", objectData.get("sales_return_amount", String.class, "0"),
                    "refunded_amount", objectData.get("refunded_amount", String.class, "0"),
                    "owed_amount", objectData.get("owed_amount", String.class, "0"),
                    "a6", String.valueOf(objectData.get("payable_amount", BigDecimal.class, BigDecimal.ZERO))));
            collectInfoList.put("productLable", Maps.newHashMap(
                    "sales_amount", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.collect.sales_amount", context.getLang(), "销售金额"),
                    "sales_received_amount", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.collect.sales_received_amount", context.getLang(), "销售实收"),
                    "sales_return_amount", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.collect.sales_return_amount", context.getLang(), "应退"),
                    "refunded_amount", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.collect.refunded_amount", context.getLang(), "实退"),
                    "owed_amount", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.collect.owed_amount", context.getLang(), "欠款"),
                    "a6", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.collect.a6", context.getLang(), "应上缴")));
            List<Map<String, Object>> goodsList = new ArrayList<>(detail.getDataDetailList().size());
            int i = 0;
            for (SalesStatementsDetail.Shop shop : detail.getDataDetailList()) {
                ++i;
                Map<String, Object> account = new HashMap<>(8);
                account.put("title", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.goods.title", context.getLang(), "门店明细"));
                account.put("topList", Lists.newArrayList("name", "sales_amount", "sales_received_amount", "refunded_amount", "owed_amount"));
                account.put("PrintData", Collections.singletonList(Maps.newHashMap(
                        "name", i + "、" + shop.getAccountName(),
                        "sales_amount", String.valueOf(shop.getSalesAmount()),
                        "sales_received_amount", String.valueOf(shop.getSalesReceivedAmount()),
                        "refunded_amount", String.valueOf(shop.getRefundedAmount()),
                        "owed_amount", String.valueOf(shop.getOwedAmount()))));
                account.put("productLable", Maps.newHashMap(
                        "name", " ",
                        "sales_amount", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.collect.sales_amount", context.getLang(), "销售金额"),
                        "sales_received_amount", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.collect.sales_received_amount", context.getLang(), "销售实收"),
                        "refunded_amount", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.collect.refunded_amount", context.getLang(), "实退"),
                        "owed_amount", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.collect.owed_amount", context.getLang(), "欠款")));
                goodsList.add(account);
            }
            Map<String, Object> data = new HashMap<>(8);
            data.put("ticketHeader", ticketHeader);
            data.put("baseInfoList", baseInfoList);
            data.put("collectInfoList", collectInfoList);
            data.put("goodsList", goodsList);
            Map<String, Object> map = new HashMap<>(8);
            map.put("data", data);
            map.put("apiName", "FMCG_Statements");
            map.put("dataId", UUID.randomUUID().toString());
            return Result.success("bundle://metadata/detail/BleEscPrintPage", map);
        } else {
            return Result.err();
        }
    }

    public Map<String, Object> buildTicketData(IObjectData objectData) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        //头部信息
        Map<String, Object> ticketHeader = new HashMap<>(4);
        ticketHeader.put("title", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.head.title1", getActionContext().getLang(), "业务员日账单"));
        //基础汇总信息
        Map<String, Object> baseInfoList = new HashMap<>(8);
        baseInfoList.put("title", "");
        baseInfoList.put("topList", Lists.newArrayList("date", "salesman"));
        baseInfoList.put("PrintData", Maps.newHashMap(
                "date", simpleDateFormat.format(objectData.get("statement_date", Date.class)),
                "salesman", getSalesName()
        ));
        baseInfoList.put("productLable", Maps.newHashMap(
                "date", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.baseInfo.date1", getActionContext().getLang(), "业务日期"),
                "salesman", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.baseInfo.salesman", getActionContext().getLang(), "业务员")));
        //金额汇总信息
        Map<String, Object> payInfoList = new HashMap<>(8);
        payInfoList.put("title", "");
        payInfoList.put("topList", Lists.newArrayList("order_amount", "sales_return_amount", "shipment_amount",
                "receive_cash", "online_received", "receive_debt", "receive_prepaid_deposit", "use_receive",
                "coupon_discount", "gross_receipts", "owed_amount", "payable_amount",
                "promotion_activities_discount_amount", "rebate_amount", "dynamic_amount"));
        payInfoList.put("PrintData", Maps.newHashMap(
                "order_amount", objectData.get("order_amount", BigDecimal.class),
                "sales_return_amount", objectData.get("sales_return_amount", BigDecimal.class),
                "shipment_amount", objectData.get("shipment_amount", BigDecimal.class, BigDecimal.ZERO).add(objectData.get("delivery_amount", BigDecimal.class, BigDecimal.ZERO)),
                "receive_cash", objectData.get("receive_cash", BigDecimal.class),
                "online_received", objectData.get("online_received", BigDecimal.class),
                "receive_debt", objectData.get("receive_debt", BigDecimal.class),
                "receive_prepaid_deposit", objectData.get("receive_prepaid_deposit", BigDecimal.class),
                "use_receive", objectData.get("use_receive", BigDecimal.class),
                "coupon_discount", objectData.get("coupon_discount", BigDecimal.class),
                "gross_receipts", objectData.get("gross_receipts", BigDecimal.class),
                "owed_amount", objectData.get("owed_amount", BigDecimal.class),
                "payable_amount", objectData.get("payable_amount", BigDecimal.class),
                "promotion_activities_discount_amount",  objectData.get("promotion_activities_discount_amount", BigDecimal.class),
                "rebate_amount",  objectData.get("rebate_amount", BigDecimal.class),
                "dynamic_amount",  objectData.get("dynamic_amount", BigDecimal.class)
        ));
        payInfoList.put("productLable", Maps.newHashMap(
                "order_amount", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.payInfo.order_amount1", getActionContext().getLang(), "销售订单金额"),
                "sales_return_amount", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.payInfo.sales_return_amount1", getActionContext().getLang(), "退货订单金额"),
                "shipment_amount", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.payInfo.shipment_amount1", getActionContext().getLang(), "发货金额"),
                "receive_cash", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.payInfo.receive_cash1", getActionContext().getLang(), "线下收款"),
                "online_received", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.payInfo.online_received1", getActionContext().getLang(), "线上已收"),
                "receive_debt", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.payInfo.receive_debt1", getActionContext().getLang(), "收欠款"),
                "receive_prepaid_deposit", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.payInfo.receive_prepaid_deposit1", getActionContext().getLang(), "收预收款"),
                "use_receive", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.payInfo.use_receive1", getActionContext().getLang(), "预收款抵扣"),
                "coupon_discount", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.payInfo.coupon_discount1", getActionContext().getLang(), "优惠券抵扣"),
                "gross_receipts", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.payInfo.gross_receipts1", getActionContext().getLang(), "实收款"),
                "owed_amount", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.payInfo.owed_amount1", getActionContext().getLang(), "今日欠款总额"),
                "payable_amount", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.payInfo.payable_amount1", getActionContext().getLang(), "今日应交款总额"),
                "promotion_activities_discount_amount", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.payInfo.promotion_activities_discount_amount1", getActionContext().getLang(), "促销金额"),
                "rebate_amount", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.payInfo.rebate_amount1", getActionContext().getLang(), "返利金额"),
                "dynamic_amount", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.payInfo.dynamic_amount1", getActionContext().getLang(), "抹零金额")
        ));
        Map<String, Object> otherInfo = new HashMap<>(8);
        otherInfo.put("title", "");
        otherInfo.put("topList", Lists.newArrayList("salesman", "examiner"));
        otherInfo.put("PrintData", Maps.newHashMap(
                "salesman", "\r\n\r\n",
                "examiner", "\r\n\r\n"
        ));
        otherInfo.put("productLable", Maps.newHashMap(
                "salesman", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.ptherInfo.salesman1", getActionContext().getLang(), "业务签字"),
                "examiner", I18NSimpleUtils.get("SalesStatementsObj.MobilePrintReceipt.ptherInfo.examiner1", getActionContext().getLang(), "财务签字")));
        Map<String, Object> data = new HashMap<>(8);
        data.put("ticketHeader", ticketHeader);
        data.put("baseInfoList", baseInfoList);
        data.put("payInfoList", payInfoList);
        data.put("otherInfoList", otherInfo);
        Map<String, Object> map = new HashMap<>(8);
        map.put("data", data);
        map.put("apiName", "FMCG_Statements");
        map.put("dataId", UUID.randomUUID().toString());
        return map;
    }

    public String getTemplateConfig(Integer tenantId) {
        TenantConfig.Arg arg = new TenantConfig.Arg();
        arg.setKey("SALES_STATEMENTS_TICKET_TEMPLATE");
        try {
            TenantConfig.Result result = fmcgConfigProxy.get(tenantId, -10000, arg);
            return result.getValue();
        } catch (Exception ex) {
            return "2";
        }
    }

    public String getEnterpriseName() {
        GetSimpleEnterpriseDataArg argument = new GetSimpleEnterpriseDataArg();
        argument.setEnterpriseId(Integer.parseInt(actionContext.getTenantId()));
        GetSimpleEnterpriseDataResult simpleEnterpriseData = enterpriseEditionService.getSimpleEnterpriseData(argument);
        return simpleEnterpriseData.getEnterpriseData().getEnterpriseName();
    }

    public String getSalesName() {
        List<String> owner = objectData.getOwner();
        String salesname = "";
        if (CollectionUtils.notEmpty(owner)) {
            Map<String, String> personnel = serviceFacade.findNameByIds(User.builder().tenantId(actionContext.getTenantId()).userId("-10000").build(),
                    "PersonnelObj", owner);
            salesname = personnel.getOrDefault(owner.get(0), "");
        }
        return salesname;
    }


    @Data
    public static class Arg {

        private String objectDataId;
    }

    @Data
    public static class Result {
        private String url;
        private Map<String, Object> params;
        private Integer errorCode;

        public static Result success(String url, Map<String, Object> params) {
            Result result = new Result();
            result.setUrl(url);
            result.setParams(params);
            result.setErrorCode(0);
            return result;
        }

        public static Result err() {
            Result result = new Result();
            result.setErrorCode(1);
            return result;
        }
    }
}
