package com.facishare.crm.fmcg.sales.model.function;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

public interface FindByFilters {

    @EqualsAndHashCode(callSuper = true)
    @Data
    class Arg extends FunctionBase.Arg {
        private List<FilterDTO> filters;
        private List<String> fields;
        private Boolean findAll = false;
        private Integer offset = 0;
        private Integer limit = 10;
    }

    @Data
    class FilterDTO {
        private String fieldName;
        private List<String> fieldValue;
        private String operator;
        private Boolean isMasterField;
        private Integer valueType;
    }

    @Data
    class Result extends FunctionBase.Result {

        private List<JSONObject> dataList;

        public static Result success(List<JSONObject> dataList) {
            Result result = new Result();
            result.setCode(0);
            result.setMessage("success");
            result.setDataList(dataList);
            return result;
        }
    }
}
