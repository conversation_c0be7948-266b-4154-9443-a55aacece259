package com.facishare.crm.fmcg.sales.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.beust.jcommander.internal.Lists;
import com.beust.jcommander.internal.Maps;
import com.facishare.crm.fmcg.sales.action.SalesStatementsObjConformanceStatementsAction;
import com.facishare.crm.fmcg.sales.apiname.ApiNames;
import com.facishare.crm.fmcg.sales.apiname.CommonApiNames;
import com.facishare.crm.fmcg.sales.apiname.ReturnedGoodsInvoiceApiNames;
import com.facishare.crm.fmcg.sales.business.MultipleUnitBusiness;
import com.facishare.crm.fmcg.sales.business.RelatedBizConfigBusiness;
import com.facishare.crm.fmcg.sales.business.StockBusiness;
import com.facishare.crm.fmcg.sales.constants.RoleCode;
import com.facishare.crm.fmcg.sales.dao.mongo.SalesStatementsDao;
import com.facishare.crm.fmcg.sales.dao.mongo.entity.SalesStatementsDetailEntity;
import com.facishare.crm.fmcg.sales.model.statement.*;
import com.facishare.crm.fmcg.sales.service.abstraction.ISalesStatementsService;
import com.facishare.crm.fmcg.sales.utils.DateUtils;
import com.facishare.crm.fmcg.sales.utils.I18NSimpleUtils;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.ButtonService;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fmcg.framework.http.FmcgSalesProxy;
import com.fxiaoke.common.release.GrayRelease;
import com.fxiaoke.notifier.support.NotifierClient;
import com.github.jedis.support.MergeJedisCmd;
import com.github.trace.executor.MonitorTaskWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.errors.ApiException;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@ServiceModule("sales_statements_service")
//IgnoreI18nFile
public class SalesStatementsService implements ISalesStatementsService {

    private static final String OPEN_SALES_STATEMENTS_CONFIG_KEY = "metadata_sys_is_open_SalesStatementsObj";

    private static final String FMCG_SALES_STATEMENTS_ROOM = "FMCG_SALES_STATEMENTS_ROOM";

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private ConfigService configService;

    @Resource
    private DescribeManagerService describeManagerService;

    @Resource
    private ThreadPoolTaskExecutor taskExecutor;

    @Resource
    private SalesStatementsDao salesStatementsDao;

    @Resource
    private MultipleUnitBusiness multipleUnitBusiness;

    @Resource
    private FmcgSalesProxy fmcgSalesProxy;

    @Resource
    private RelatedBizConfigBusiness relatedBizConfigBusiness;

    @Resource
    private StockBusiness stockBusiness;

    @Resource
    private MergeJedisCmd redisSupport;

    @Resource
    private ButtonService buttonService;

    /**
     * 发货单详情
     */
    @Override
    @ServiceMethod("detail")
    public SalesStatementsDetail.Result detail(SalesStatementsDetail.Arg arg, ServiceContext context) {
        log.info("SalesStatementsService detail arg={}", arg);
        String dataId = arg.getDataId();
        String tenantId = context.getTenantId();
        SalesStatementsDetailEntity salesStatementsDetailEntity = salesStatementsDao.get(SalesStatementsDetailEntity.class, arg.getDataId());
        if (Objects.nonNull(salesStatementsDetailEntity)) {
            SalesStatementsDetail.Result mongoResult = JSON.parseObject(salesStatementsDetailEntity.getData(), SalesStatementsDetail.Result.class);
            if (mongoResult.getErrorCode() != 0) {
                salesStatementsDao.delete(SalesStatementsDetailEntity.class, dataId);
            } else {
                return mongoResult.initLangData(context.getLang().getValue());
            }
        }
        SalesStatementsDetail.Result result = new SalesStatementsDetail.Result(context.getLang().getValue());
        IObjectData salesStatements = getSalesStatementsById(tenantId, dataId, context.getRequestContext());
        log.info("salesStatements={}", salesStatements);
        if (salesStatements == null) {
            return result;
        }
        Long statementDate = salesStatements.get("statement_date", Long.class);

        List<String> salesman = getList(salesStatements.get("salesman_name"));
        List<Long> dateInterval = DateUtils.getTimeStampStartTimeAndEndTime(statementDate);
        List<IObjectData> allSalesOrder = Lists.newArrayList();
        List<IObjectData> allReturnedGoodsInvoice = Lists.newArrayList();
        List<IObjectData> allPaymentList = Lists.newArrayList();
        List<IObjectData> allQuoteOrderDeliveryNote = Lists.newArrayList();
        List<IObjectData> allMatchNoteList = Lists.newArrayList();
        fillOrderData(salesman, dateInterval, allSalesOrder, allReturnedGoodsInvoice, allPaymentList, allQuoteOrderDeliveryNote, allMatchNoteList, context);
        if (CollectionUtils.isEmpty(allSalesOrder) && CollectionUtils.isEmpty(allReturnedGoodsInvoice)
                && CollectionUtils.isEmpty(allPaymentList) && CollectionUtils.isEmpty(allMatchNoteList) && CollectionUtils.isEmpty(allQuoteOrderDeliveryNote)) {
            return result;
        }
        List<SalesStatementsDetail.Shop> shopList = getShopList(tenantId, allSalesOrder, allReturnedGoodsInvoice, allPaymentList, allMatchNoteList, allQuoteOrderDeliveryNote, true, context);
        result.setDataDetailList(shopList);
        result.setErrorCode(0);
        result.setErrorMsg("success");
        return result;
    }

    @ServiceMethod("addOrUpdate")
    public SalesStatementsAdd.Result addOrUpdate(SalesStatementsAdd.Arg arg, ServiceContext context) {
        log.info("SalesStatementsAdd.Arg arg={}", JSONObject.toJSONString(arg));
        if (CollectionUtils.isEmpty(arg.getSalesman()) || CollectionUtils.isEmpty(arg.getDateInterval())) {
            return SalesStatementsAdd.Result.fail("参数错误");
        }
        recomputeSalesStatements(arg.getSalesman(), arg.getDateInterval(), 1, context);
        return SalesStatementsAdd.Result.success();
    }

    public void recomputeSalesStatements(List<String> salesman, List<Long> dateInterval, Integer depth, ServiceContext context) {
        if (depth > 2) {
            log.info("recomputeSalesStatements depth > 2, tenantId={}, dateInterval={}, salesman={} ", context.getTenantId(), dateInterval, salesman);
            return;
        }
        log.info("tenantId={}, dateInterval={}, salesman={} ", context.getTenantId(), dateInterval, salesman);
        IObjectData saleStatements = getSalesStatementsBySalesmanAndData(context.getTenantId(), salesman, dateInterval, context.getRequestContext());
        log.info("saleStatements={}", saleStatements);
        if (Objects.nonNull(saleStatements) && isStatemented(saleStatements.get("statement_status", String.class))) {
            log.info("该对账单已对账");
            return;
        }
        List<IObjectData> allSalesOrder = Lists.newArrayList(), allReturnedGoodsInvoice = Lists.newArrayList();
        List<IObjectData> allPaymentList = Lists.newArrayList();
        List<IObjectData> allQuoteOrderDeliveryNote = Lists.newArrayList();
        List<IObjectData> allMatchNoteList = Lists.newArrayList();
        fillOrderData(salesman, dateInterval, allSalesOrder, allReturnedGoodsInvoice, allPaymentList, allQuoteOrderDeliveryNote, allMatchNoteList, context);
        log.info("allSalesOrder={}", allSalesOrder);
        log.info("allReturnedGoodsInvoice={}", JSONObject.toJSONString(allReturnedGoodsInvoice));
        log.info("allPaymentList={}", JSONObject.toJSONString(allPaymentList));
        log.info("allQuoteOrderDeliveryNote={}", JSONObject.toJSONString(allQuoteOrderDeliveryNote));
        log.info("allMatchNote={}", JSONObject.toJSONString(allMatchNoteList));
        if (CollectionUtils.isEmpty(allSalesOrder) && CollectionUtils.isEmpty(allReturnedGoodsInvoice) && CollectionUtils.isEmpty(allPaymentList)
                && CollectionUtils.isEmpty(allQuoteOrderDeliveryNote) && CollectionUtils.isEmpty(allMatchNoteList)) {
            if (saleStatements != null && StringUtils.isNotBlank(saleStatements.getId())) {
                Map<String, BigDecimal> amountMap = new HashMap<>();
                fillZero(amountMap);
                updateObj(context.getTenantId(), ApiNames.SALES_STATEMENTS_OBJ, Lists.newArrayList(saleStatements), new HashMap<>(amountMap));
            }
            return;
        }
        Map<String, BigDecimal> amountMap = new HashMap<>();
        fillZero(amountMap);
        List<SalesStatementsDetail.Shop> shopList = getShopList(context.getTenantId(), allSalesOrder, allReturnedGoodsInvoice, allPaymentList, allMatchNoteList, allQuoteOrderDeliveryNote, false, context);
        computeHeadData(shopList, amountMap);
        log.info("amountMap={}", JSONObject.toJSONString(amountMap));
        //创建对象
        saleStatements = getSalesStatementsBySalesmanAndData(context.getTenantId(), salesman, dateInterval, context.getRequestContext());
        if (saleStatements != null) {
            if (isStatemented(saleStatements.get("statement_status", String.class))) {
                log.info("该对账单已对账");
                return;
            }
            log.info("修改对账单 tenantId={}, data={}", context.getTenantId(), JSON.toJSON(saleStatements));
            updateObj(context.getTenantId(), ApiNames.SALES_STATEMENTS_OBJ, Lists.newArrayList(saleStatements), new HashMap<>(amountMap));
            handleAllSourceOrders(context.getTenantId(), saleStatements.getId(), allSalesOrder, allReturnedGoodsInvoice, allPaymentList);
            return;
        }
        String insertKey = buildSalesStatementKey(context.getTenantId(), salesman.get(0), dateInterval.get(0), "insert");
        String updateKey = buildSalesStatementKey(context.getTenantId(), salesman.get(0), dateInterval.get(0), "update");
        String insertSuccessKey = buildSalesStatementKey(context.getTenantId(), salesman.get(0), dateInterval.get(0), "insert_success");
        if (redisSupport.setnx(insertKey, "1") == 1) {
            try {
                redisSupport.expire(insertKey, 60L);
                saleStatements = new ObjectData();
                amountMap.forEach(saleStatements::set);
                saleStatements.set("salesman_name", salesman);
                saleStatements.set("statement_date", dateInterval.get(0));
                saleStatements.set("statement_status", "0");
                saleStatements.set("owner", salesman);
                saleStatements.set("delivery_count", amountMap.getOrDefault("delivery_count", BigDecimal.ZERO).intValue());
                saleStatements.setDescribeApiName(ApiNames.SALES_STATEMENTS_OBJ);
                saleStatements.setTenantId(context.getTenantId());
                //create
                log.info("新建对账单 tenantId={}, data={}", context.getTenantId(), saleStatements);
                createObj(context.getTenantId(), saleStatements);
                redisSupport.setex(insertSuccessKey, 60L * 60 * 24, "1");
                handleAllSourceOrders(context.getTenantId(), saleStatements.getId(), allSalesOrder, allReturnedGoodsInvoice, allPaymentList);
            } catch (Exception ex) {
                log.error("create SalesStatementsObj error", ex);
            } finally {
                redisSupport.del(insertKey);
            }
            //创建对账单时 有新的变更  重新计算
            if ("1".equals(redisSupport.get(updateKey))) {
                recomputeSalesStatements(salesman, dateInterval, depth + 1, context);
                redisSupport.del(updateKey);
            }
            return;
        }
        if ("1".equals(redisSupport.get(insertSuccessKey))) {
            //对账单已经创建成功  重新计算
            recomputeSalesStatements(salesman, dateInterval, depth + 1, context);
        } else {
            //对账单未创建成功  等待对账单创建成功后重新计算
            redisSupport.setex(updateKey, 60L * 60 * 24, "1");
        }
    }

    private void computeHeadData(List<SalesStatementsDetail.Shop> shops, Map<String, BigDecimal> amountMap) {
        for (SalesStatementsDetail.Shop shop : shops) {
            amountMap.put("sales_amount", amountPlus(amountMap.getOrDefault("sales_amount", BigDecimal.ZERO), shop.getSalesAmount()));
            amountMap.put("shipment_amount", amountPlus(amountMap.getOrDefault("shipment_amount", BigDecimal.ZERO), shop.getShipmentAmount()));
            amountMap.put("order_wait_payment_amount", amountPlus(amountMap.getOrDefault("order_wait_payment_amount", BigDecimal.ZERO), shop.getOrderWaitPaymentAmount()));
            amountMap.put("dynamic_amount", amountPlus(amountMap.getOrDefault("dynamic_amount", BigDecimal.ZERO), shop.getDynamicAmount()));
            amountMap.put("coupon_discount", amountPlus(amountMap.getOrDefault("coupon_discount", BigDecimal.ZERO), shop.getCouponDiscount()));
            amountMap.put("promotion_activities_discount_amount", amountPlus(amountMap.getOrDefault("promotion_activities_discount_amount", BigDecimal.ZERO), shop.getPromotionActivitiesDiscountAmount()));
            amountMap.put("rebate_amount", amountPlus(amountMap.getOrDefault("rebate_amount", BigDecimal.ZERO), shop.getRebateAmount()));
            amountMap.put("order_amount", amountPlus(amountMap.getOrDefault("order_amount", BigDecimal.ZERO), shop.getOrderAmount()));
            amountMap.put("use_receive", amountPlus(amountMap.getOrDefault("use_receive", BigDecimal.ZERO), shop.getUseReceive()));
            amountMap.put("sales_return_amount", amountPlus(amountMap.getOrDefault("sales_return_amount", BigDecimal.ZERO), shop.getSalesReturnAmount()));
            amountMap.put("return_goods_deduction_amount", amountPlus(amountMap.getOrDefault("return_goods_deduction_amount", BigDecimal.ZERO), shop.getReturnGoodsDeductionAmount()));
            amountMap.put("total_settled_amount", amountPlus(amountMap.getOrDefault("total_settled_amount", BigDecimal.ZERO), shop.getTotalSettledAmount()));
            amountMap.put("refund_amount", amountPlus(amountMap.getOrDefault("refund_amount", BigDecimal.ZERO), shop.getRefundAmount()));
            amountMap.put("receive_cash", amountPlus(amountMap.getOrDefault("receive_cash", BigDecimal.ZERO), shop.getReceiveCash()));
            amountMap.put("online_payment", amountPlus(amountMap.getOrDefault("online_payment", BigDecimal.ZERO), shop.getOnlinePayment()));
            amountMap.put("receive_debt", amountPlus(amountMap.getOrDefault("receive_debt", BigDecimal.ZERO), shop.getReceiveDebt()));
            amountMap.put("receive_debt_cash", amountPlus(amountMap.getOrDefault("receive_debt_cash", BigDecimal.ZERO), shop.getReceiveDebtCash()));
            amountMap.put("receive_debt_online_payment", amountPlus(amountMap.getOrDefault("receive_debt_online_payment", BigDecimal.ZERO), shop.getReceiveDebtOnlinePayment()));
            amountMap.put("receive_prepaid_deposit", amountPlus(amountMap.getOrDefault("receive_prepaid_deposit", BigDecimal.ZERO), shop.getReceivePrepaidDeposit()));
            amountMap.put("receive_prepaid_deposit_cash", amountPlus(amountMap.getOrDefault("receive_prepaid_deposit_cash", BigDecimal.ZERO), shop.getReceivePrepaidDepositCash()));
            amountMap.put("receive_prepaid_deposit_online_payment", amountPlus(amountMap.getOrDefault("receive_prepaid_deposit_online_payment", BigDecimal.ZERO), shop.getReceivePrepaidDepositOnlinePayment()));
            amountMap.put("delivery_amount", amountPlus(amountMap.getOrDefault("delivery_amount", BigDecimal.ZERO), shop.getDeliveryAmount()));
            amountMap.put("delivery_count", amountPlus(amountMap.getOrDefault("delivery_count", BigDecimal.ZERO), new BigDecimal(shop.getDeliveryCount())));
            amountMap.put("return_goods_old_deduction_amount", amountPlus(amountMap.getOrDefault("return_goods_old_deduction_amount", BigDecimal.ZERO), shop.getReturnGoodsOldDeductionAmount()));
            amountMap.put("refunded_amount", amountPlus(amountMap.getOrDefault("refunded_amount", BigDecimal.ZERO), shop.getRefundedAmount()));
            amountMap.put("owed_amount", amountPlus(amountMap.getOrDefault("owed_amount", BigDecimal.ZERO), shop.getOwedAmount()));
            amountMap.put("total_discount", amountPlus(amountMap.getOrDefault("total_discount", BigDecimal.ZERO), shop.getTotalDiscount()));
            amountMap.put("receivable_amount", amountPlus(amountMap.getOrDefault("receivable_amount", BigDecimal.ZERO), shop.getReceivableAmount()));
            amountMap.put("received_amount", amountPlus(amountMap.getOrDefault("received_amount", BigDecimal.ZERO), shop.getReceivedAmount()));
        }
    }


    private void handleAllSourceOrders(String tenantId, String salesStatementsId, List<IObjectData> allSalesOrders, List<IObjectData> allReturnedGoodsInvoice, List<IObjectData> allPaymentList) {
        fillBackRelatedOrderData(tenantId, salesStatementsId, "SalesOrderObj", allSalesOrders);
        fillBackRelatedOrderData(tenantId, salesStatementsId, "ReturnedGoodsInvoiceObj", allReturnedGoodsInvoice);
        fillBackRelatedOrderData(tenantId, salesStatementsId, "PaymentObj", allPaymentList);
    }

    private void fillBackRelatedOrderData(String tenantId, String salesStatementsId, String relatedApiName, List<IObjectData> relatedOrderList) {
        try {
            if (!CollectionUtils.isEmpty(relatedOrderList)) {
                Map<String, Object> fieldMap = new HashMap<>();
                fieldMap.put("sales_statements_ids", Lists.newArrayList(salesStatementsId));
                updateObj(tenantId, relatedApiName, relatedOrderList, fieldMap);
            }
        } catch (Exception ex) {
            log.error("fillBackRelevantOrderData {} error", relatedApiName, ex);
        }
    }

    public String buildSalesStatementKey(String tenantId, String salesman, Long time, String flag) {
        return String.format("createSSO_%s_%s_%s_%s_key", tenantId, time, salesman, flag);
    }

    public boolean isStatemented(String statementsStatus) {
        return "1".equals(statementsStatus) || "2".equals(statementsStatus);
    }

    public void fillOrderData(List<String> salesman, List<Long> dateInterval, List<IObjectData> allSalesOrder, List<IObjectData> allReturnedGoodsInvoice, List<IObjectData> allPaymentList,
                              List<IObjectData> allQuoteOrderDeliveryNote, List<IObjectData> allMatchNoteObj, ServiceContext context) {
        String tenantId = context.getTenantId();
        //获取业务员 指定时间 的 负责的所有订单
        allSalesOrder.addAll(getCarSalesOrderByDateAndOwner(tenantId, dateInterval, salesman, context.getRequestContext()));
        allReturnedGoodsInvoice.addAll(queryReturnedGoodsInvoiceByDateAndOwner(tenantId, dateInterval, salesman, context.getRequestContext()));
        allPaymentList.addAll(queryPaymentByDateAndOwner(tenantId, dateInterval, salesman, context.getRequestContext()));
        if (stockBusiness.isEnableDeliveryNote(Integer.parseInt(tenantId))) {
            allQuoteOrderDeliveryNote.addAll(getDeliveryNoteByDateAndOwnerAndFilterQuote(tenantId, dateInterval, salesman, context.getRequestContext()));
        }
        if (relatedBizConfigBusiness.isEnableAccountsReceivable(Integer.parseInt(tenantId))) {
            allMatchNoteObj.addAll(queryMatchNoteObjByDateAndOwner(tenantId, dateInterval, salesman, context.getRequestContext()));
        }
    }

    public void fillZero(Map<String, BigDecimal> amountMap) {
        List<String> fields = new ArrayList<>();
        fields.add("sales_amount");
        fields.add("shipment_amount");
        fields.add("received_amount");
        fields.add("owed_amount");
        fields.add("dynamic_amount");
        fields.add("coupon_discount");
        fields.add("promotion_activities_discount_amount");
        fields.add("rebate_amount");
        fields.add("sales_return_amount");
        fields.add("order_amount");
        fields.add("use_receive");
        fields.add("receive_cash");
        fields.add("online_payment");
        fields.add("receive_debt");
        fields.add("receive_prepaid_deposit");
        fields.add("order_wait_payment_amount");
        fields.add("receive_debt_cash");
        fields.add("receive_debt_online_payment");
        fields.add("receive_prepaid_deposit_cash");
        fields.add("receive_prepaid_deposit_online_payment");
        fields.add("return_goods_deduction_amount");
        fields.add("refund_amount");
        fields.add("return_goods_old_deduction_amount");
        fields.add("total_settled_amount");
        fields.add("delivery_amount");
        fields.add("delivery_count");
        fields.add("receivable_amount");
        fields.add("refunded_amount");
        fields.add("total_discount");
        fields.forEach(field -> amountMap.put(field, BigDecimal.ZERO));
    }

    @ServiceMethod("statementDetail")
    public WebDetail.Result statementDetail(WebDetail.Arg arg, ServiceContext context) {
        List<IObjectData> dataList = queryStatementsByDateAndOwner(arg.getSalesmanName(), arg.getStatementDate(), context.getRequestContext());
        IObjectData objectData = null;
        if (!CollectionUtils.isEmpty(dataList)) {
            objectData = dataList.get(0);
        }
        if (Objects.isNull(objectData)) {
            objectData = new ObjectData();
        }
        WebDetail.Result result = new WebDetail.Result();
        result.setStatementStatus(objectData.get("statement_status", String.class, "0"));
        handleWebDetailTop(result, objectData, context.getLang().getValue());
        handleWebDetailCenter(Integer.parseInt(context.getTenantId()), result, objectData, context.getLang().getValue());
        handleWebDetailBottom(result, objectData, context.getLang().getValue());
        return result;
    }

    @ServiceMethod("statementAccountDetail")
    public SalesStatementsDetail.Result statementAccountDetail(WebDetail.Arg arg, ServiceContext context) {
        List<IObjectData> dataList = queryStatementsByDateAndOwner(arg.getSalesmanName(), arg.getStatementDate(), context.getRequestContext());
        String dataId = "";
        if (!CollectionUtils.isEmpty(dataList)) {
            dataId = dataList.get(0).getId();
        }
        SalesStatementsDetail.Arg arg1 = new SalesStatementsDetail.Arg();
        arg1.setDataId(dataId);
        return detail(arg1, context);
    }

    private void handleWebDetailBottom(WebDetail.Result result, IObjectData objectData, String lang) {
        WebDetail.Bottom bottom = new WebDetail.Bottom();
        WebDetail.BottomIem bottomIem1 = new WebDetail.BottomIem();
        bottomIem1.setTitle(I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.salesReceipt", lang, "销售收款"));
        bottomIem1.setDataList(Lists.newArrayList(
                new WebDetail.LabelAndValue(I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.onlinePayment", lang, "在线支付"), "¥" + objectData.get("online_payment", BigDecimal.class, BigDecimal.ZERO).toPlainString(), I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.onlinePayment.tips", lang, "回款用途为销售收款，且在线支付的回款金额")),
                new WebDetail.LabelAndValue(I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.receiveCash", lang, "线下收款"), "¥" + objectData.get("receive_cash", BigDecimal.class, BigDecimal.ZERO).toPlainString(), I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.receiveCash.tips", lang, "回款用途为销售收款，且非在线支付的回款金额")),
                new WebDetail.LabelAndValue(I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.useReceive", lang, "使用预收"), "¥" + objectData.get("use_receive", BigDecimal.class, BigDecimal.ZERO).toPlainString()),
                new WebDetail.LabelAndValue(I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.owedAmount", lang, "今日新增欠款"), "¥" + objectData.get("owed_amount", BigDecimal.class, BigDecimal.ZERO).toPlainString()),
                new WebDetail.LabelAndValue(I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.refundedAmount", lang, "实退金额"), "¥" + objectData.get("refunded_amount", BigDecimal.class, BigDecimal.ZERO).toPlainString())
        ));
        bottom.setDataList(Lists.newArrayList(bottomIem1));
        result.setBottom(bottom);
    }

    private void handleWebDetailCenter(int tenantId, WebDetail.Result result, IObjectData objectData, String lang) {
        WebDetail.Center center = new WebDetail.Center();
        WebDetail.CenterItem centerItem1 = new WebDetail.CenterItem();
        centerItem1.setData(new WebDetail.LabelAndValue(I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.salesAmount", lang, "订单金额"), "¥" + objectData.get("sales_amount", BigDecimal.class, BigDecimal.ZERO).toPlainString(), I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.salesAmount.tips", lang, "订单的销售金额合计")));

        WebDetail.CenterItem centerItem2 = new WebDetail.CenterItem();
        BigDecimal shipmentAmount = objectData.get("shipment_amount", BigDecimal.class, BigDecimal.ZERO);
        BigDecimal salesReturnAmount = objectData.get("sales_return_amount", BigDecimal.class, BigDecimal.ZERO);
        BigDecimal deliveryAmount = objectData.get("delivery_amount", BigDecimal.class, BigDecimal.ZERO);
        BigDecimal receivableAmount = objectData.get("receivable_amount", BigDecimal.class, BigDecimal.ZERO); //销售净额
        if (GrayRelease.isAllow("fmcg", "SALES_STATEMENTS_CHANGE_GRAY", tenantId)) {
            centerItem2.setData(new WebDetail.LabelAndValue(I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.netSales", lang, "销售净额"), "¥" + receivableAmount.toPlainString(), I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.shipmentAmount", lang, "发货金额") + "-" + I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.salesReturnAmount", lang, "退货金额")));
            centerItem2.setSubDataList(Lists.newArrayList(
                    new WebDetail.LabelAndValue("= " + I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.shipmentAmount", lang, "发货金额"), "¥" + shipmentAmount.toPlainString()),
                    new WebDetail.LabelAndValue("+ " + I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.deliveryAmount", lang, "配送金额"), "¥" + deliveryAmount.toPlainString()),
                    new WebDetail.LabelAndValue("- " + I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.salesReturnAmount", lang, "退货金额"), "¥" + (shipmentAmount.add(deliveryAmount).subtract(receivableAmount)).toPlainString(), "", "owed_red")));
        } else {
            centerItem2.setData(new WebDetail.LabelAndValue(I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.netSales", lang, "销售净额"), "¥" + shipmentAmount.add(deliveryAmount).subtract(salesReturnAmount).toPlainString(), I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.shipmentAmount", lang, "发货金额") + "-" + I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.salesReturnAmount", lang, "退货金额")));
            centerItem2.setSubDataList(Lists.newArrayList(
                    new WebDetail.LabelAndValue("= " + I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.shipmentAmount", lang, "发货金额"), "¥" + shipmentAmount.toPlainString()),
                    new WebDetail.LabelAndValue("+ " + I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.deliveryAmount", lang, "配送金额"), "¥" + deliveryAmount.toPlainString()),
                    new WebDetail.LabelAndValue("- " + I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.salesReturnAmount", lang, "退货金额"), "¥" + salesReturnAmount.toPlainString(), "", "owed_red")));
        }
        WebDetail.CenterItem centerItem3 = new WebDetail.CenterItem();
        Integer deliveryCount = objectData.get("delivery_count", Integer.class, 0);
        centerItem3.setData(new WebDetail.LabelAndValue(I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.deliveryInfo", lang, "配送信息"), "¥" + deliveryAmount.toPlainString()));
        centerItem3.setSubDataList(Lists.newArrayList(
                new WebDetail.LabelAndValue(I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.deliveryAmount", lang, "配送金额"), "¥" + deliveryAmount.toPlainString()),
                new WebDetail.LabelAndValue(I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.deliveryCount", lang, "配送单数"), deliveryCount.toString())));
        center.setDataList(Lists.newArrayList(centerItem1, centerItem2, centerItem3));
        result.setCenter(center);
    }

    private void handleWebDetailTop(WebDetail.Result result, IObjectData objectData, String lang) {
        //总收款
        BigDecimal grossReceipts = objectData.get("gross_receipts", BigDecimal.class, BigDecimal.ZERO);
        //线上已收
        BigDecimal onlineReceived = objectData.get("online_received", BigDecimal.class, BigDecimal.ZERO);
        //应交
        BigDecimal payableAmount = objectData.get("payable_amount", BigDecimal.class, BigDecimal.ZERO);
        //实交
        BigDecimal actualPayment = objectData.get("actual_payment", BigDecimal.class, BigDecimal.ZERO);
        BigDecimal receivedAmount = objectData.get("received_amount", BigDecimal.class, BigDecimal.ZERO);
        BigDecimal receiveDebt = objectData.get("receive_debt", BigDecimal.class, BigDecimal.ZERO);
        BigDecimal receivePrepaidDeposit = objectData.get("receive_prepaid_deposit", BigDecimal.class, BigDecimal.ZERO);
        WebDetail.Top top = new WebDetail.Top();
        top.setTitle(new WebDetail.LabelAndValue(I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.grossReceipts", lang, "日实收款"), "¥" + grossReceipts.toPlainString()));
        top.setSubTitle(Lists.newArrayList(
                new WebDetail.LabelAndValue(I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.onlineReceived", lang, "线上已收"), "¥" + onlineReceived.toPlainString(), I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.onlineReceived.tips", lang, "回款方式为在线支付的回款金额")),
                new WebDetail.LabelAndValue(I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.cashPayable", lang, "现金应交"), "¥" + actualPayment.add(payableAmount), I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.cashPayable.tips", lang, "回款方式非在线支付的回款金额")),
                new WebDetail.LabelAndValue(I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.actualPayment", lang, "已交"), "¥" + actualPayment.toPlainString()),
                new WebDetail.LabelAndValue(I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.payableAmount", lang, "待交"), "¥" + payableAmount.toPlainString(), "", "brandColor")
        ));
        top.setNormalList(Lists.newArrayList(
                new WebDetail.LabelAndValue(I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.receivedAmount", lang, "收货款"), "¥" + receivedAmount.toPlainString()),
                new WebDetail.LabelAndValue(I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.receiveDebt", lang, "收欠款"), "¥" + receiveDebt.toPlainString()),
                new WebDetail.LabelAndValue(I18NSimpleUtils.get("SalesStatementsObj.MobileDetail.receivePrepaidDeposit", lang, "收预收款"), "¥" + receivePrepaidDeposit.toPlainString())
        ));
        result.setTop(top);
    }

    @ServiceMethod("recompute")
    public String recompute(Recompute.Pre arg, ServiceContext context) {
        if (CollectionUtils.isEmpty(arg.getDataIds())) {
            for (String userId : arg.getUserIds()) {
                Recompute recompute = new Recompute();
                recompute.setFlag("1");
                recompute.setSalesman(Collections.singletonList(userId));
                recompute.setStatementDate(arg.getStatementDate());
                recompute.setTenantId(Integer.valueOf(context.getTenantId()));
                NotifierClient.send(FMCG_SALES_STATEMENTS_ROOM, JSONObject.toJSONString(recompute));
            }
            return "已通知重新计算";
        }
        List<IObjectData> salesStatementsList = getSalesStatementsByIds(context.getTenantId(), arg.getDataIds(), context.getRequestContext());
        if (CollectionUtils.isEmpty(salesStatementsList)) {
            return "对账单不存在";
        }
        for (IObjectData salesStatements : salesStatementsList) {
            Long statementDate = salesStatements.get("statement_date", Long.class);
            List<String> salesman = getList(salesStatements.get("salesman_name"));
            Recompute recompute = new Recompute();
            recompute.setFlag("1");
            recompute.setSalesman(salesman);
            recompute.setStatementDate(statementDate);
            recompute.setTenantId(Integer.valueOf(context.getTenantId()));
            NotifierClient.send(FMCG_SALES_STATEMENTS_ROOM, JSONObject.toJSONString(recompute));
        }
        return "已通知重新计算";
    }

    public void checkAndFilterHasDeliveryNote(String tenantId, List<IObjectData> allSalesOrder, RequestContext context) {
        if (CollectionUtils.isEmpty(allSalesOrder)) {
            return;
        }
        Iterator<IObjectData> iterator = allSalesOrder.iterator();
        while (iterator.hasNext()) {
            IObjectData order = iterator.next();
            IFilter filter = new Filter();
            filter.setFieldName("sales_order_id");
            filter.setOperator(Operator.IN);
            filter.setFieldValues(Lists.newArrayList(order.getId()));
            List<IObjectData> dataList = getObjByFilter(tenantId, "DeliveryNoteObj", Lists.newArrayList("_id"), Lists.newArrayList(filter), context);
            if (CollectionUtils.isEmpty(dataList)) {
                iterator.remove();
            }
        }
    }

    public List<SalesStatementsDetail.Shop> getShopList(String tenantId, List<IObjectData> allSalesOrder, List<IObjectData> allReturnedGoodsInvoice, List<IObjectData> allPaymentList,
                                                        List<IObjectData> allMatchNoteObj, List<IObjectData> allQuoteOrderDeliveryNote, boolean isFillTable, ServiceContext context) {
        List<SalesStatementsDetail.Shop> shops = Lists.newArrayList();
        Map<String, SalesStatementsDetail.Shop> accountIdToShopMap = new HashMap<>();
        handleSalesOrderV1(shops, accountIdToShopMap, allSalesOrder, context);
        handleReturnedGoodsInvoiceV1(shops, accountIdToShopMap, allReturnedGoodsInvoice, context);
        handlePaymentV1(shops, accountIdToShopMap, allPaymentList, context);
        handleQuoteOrderDeliveryNoteV1(shops, accountIdToShopMap, allQuoteOrderDeliveryNote, context);
        //退货冲抵
        handleMatchNoteObjData(shops, accountIdToShopMap, allMatchNoteObj, context);

        // 换货换出金额大于换入金额，欠款需要加上换货的欠款
        Map<String, BigDecimal> accountIdToExchangeGoodsDebtAmount = handleExchangeGoodsDebtAmount(context.getRequestContext(), allReturnedGoodsInvoice);

        //计算汇总数据
        for (SalesStatementsDetail.Shop shop : shops) {
            //实退金额 退货冲抵金额+退款金额
            shop.setRefundedAmount(shop.getRefundAmount().add(shop.getReturnGoodsOldDeductionAmount()));
            //应收金额 = 销 - 退  - 优惠 - 抹零
            shop.setCashPayable(amountSubtract(shop.getCashPayable(), shop.getRefundAmount()));
            //待回款金额-max((应退金额-max(|退回单.累计结算金额|，退款金额）,0) + (换出大于换入的换货的应收 - 相应核销单的核销金额)
            shop.setOwedAmount(shop.getOrderWaitPaymentAmount().subtract(shop.getSalesReturnAmountV2().subtract(shop.getTotalSettledAmountV2().max(shop.getRefundAmount())).max(BigDecimal.ZERO))
                    .add(accountIdToExchangeGoodsDebtAmount.getOrDefault(shop.getAccountId(), BigDecimal.ZERO)));
            //实收金额 收货款 = 收现金+在线支付
            shop.setReceivedAmount(shop.getReceiveCash().add(shop.getOnlinePayment()));
            if (GrayRelease.isAllow("fmcg", "SALES_STATEMENTS_CHANGE_GRAY", tenantId)) {
                //销售净额 发货金额 + 配送金额 -退货入库金额
                shop.setReceivableAmount(shop.getShipmentAmount().add(shop.getDeliveryAmount()).subtract(shop.getReturnReceivedStorageAmount()));
            } else {
                //销售净额 发货金额 + 配送金额 -退货金额
                shop.setReceivableAmount(shop.getShipmentAmount().add(shop.getDeliveryAmount()).subtract(shop.getSalesReturnAmount()));
            }
        }
        if (isFillTable) {
            boolean multipleEnable = multipleUnitBusiness.enableMultipleUnit(Integer.parseInt(tenantId));
            Map<String, String> unitIdToName = Maps.newHashMap();
            Map<String, String> productIdToName = Maps.newHashMap();
            for (SalesStatementsDetail.Shop shop : shops) {
                handleOrderTable(shop, unitIdToName, productIdToName, multipleEnable, context);
            }
        }
        return shops;
    }

    private Map<String, BigDecimal> handleExchangeGoodsDebtAmount(RequestContext context, List<IObjectData> returnedGoodsInvoiceObjList) {
        if (CollectionUtils.isEmpty(returnedGoodsInvoiceObjList)) {
            return Collections.emptyMap();
        }
        Map<String, BigDecimal> accountIdToExchangeGoodsDebtAmount = Maps.newHashMap();

        // 1.门店维度的应收
        List<String> exchangeGoodsIdList = Lists.newArrayList();
        for (IObjectData returnedGoodsInvoiceObj : returnedGoodsInvoiceObjList) {
            String recordType = returnedGoodsInvoiceObj.get(CommonApiNames.RECORD_TYPE, String.class);
            BigDecimal returnedGoodsInvAmount = returnedGoodsInvoiceObj.get(ReturnedGoodsInvoiceApiNames.RETURNED_GOODS_INV_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
            if (!Objects.equals(recordType, ReturnedGoodsInvoiceApiNames.EXCHANGE_TYPE) || returnedGoodsInvAmount.doubleValue() >= 0) {
                continue;
            }
            exchangeGoodsIdList.add(returnedGoodsInvoiceObj.getId());

            String accountId = returnedGoodsInvoiceObj.get(ReturnedGoodsInvoiceApiNames.ACCOUNT_ID, String.class);
            BigDecimal oldValue = accountIdToExchangeGoodsDebtAmount.getOrDefault(accountId, BigDecimal.ZERO);
            accountIdToExchangeGoodsDebtAmount.put(accountId, oldValue.add(returnedGoodsInvAmount.abs()));
        }
        if (exchangeGoodsIdList.isEmpty()) {
            return accountIdToExchangeGoodsDebtAmount;
        }

        // 2.门店维度的实收，核销金额
        List<IObjectData> accountsReceivableNoteObjList = queryAccountsReceivableNoteObjByReturnedGoodsInvoiceIds(context, exchangeGoodsIdList);
        if (CollectionUtils.isEmpty(accountsReceivableNoteObjList)) {
            return accountIdToExchangeGoodsDebtAmount;
        }
        List<String> accountsReceivableNoteIds = accountsReceivableNoteObjList.stream().map(DBRecord::getId).collect(Collectors.toList());
        List<IObjectData> matchNoteObjList = queryMatchNoteObjByAccountsReceivableNoteIds(context, accountsReceivableNoteIds);
        if (CollectionUtils.isEmpty(matchNoteObjList)) {
            return accountIdToExchangeGoodsDebtAmount;
        }

        for (IObjectData matchNoteObj : matchNoteObjList) {
            String accountId = matchNoteObj.get("account_id", String.class);
            BigDecimal matchAmount = matchNoteObj.get("this_match_amount", BigDecimal.class, BigDecimal.ZERO).abs();
            if (accountIdToExchangeGoodsDebtAmount.containsKey(accountId)) {
                accountIdToExchangeGoodsDebtAmount.put(accountId, accountIdToExchangeGoodsDebtAmount.get(accountId).subtract(matchAmount));
            }
        }
        return accountIdToExchangeGoodsDebtAmount;
    }

    private List<IObjectData> queryAccountsReceivableNoteObjByReturnedGoodsInvoiceIds(RequestContext context, List<String> returnedGoodsInvoiceIds) {
        if (CollectionUtils.isEmpty(returnedGoodsInvoiceIds)) {
            return Collections.emptyList();
        }
        List<String> field = Lists.newArrayList("_id", "name");
        IFilter filter1 = new Filter();
        filter1.setFieldName("object_receivable_api_name");
        filter1.setOperator(Operator.EQ);
        filter1.setFieldValues(Lists.newArrayList(ApiNames.RETURNED_GOODS_INVOICE_OBJ));

        IFilter filter2 = new Filter();
        filter2.setFieldName("object_receivable_data_id");
        filter2.setOperator(Operator.IN);
        filter2.setFieldValues(returnedGoodsInvoiceIds);

        List<IFilter> filters = Lists.newArrayList(filter1, filter2);
        return getObjByFilter(context.getTenantId(), ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ, field, filters, context);
    }

    private List<IObjectData> queryMatchNoteObjByAccountsReceivableNoteIds(RequestContext context, List<String> accountsReceivableNoteIds) {
        List<String> field = Lists.newArrayList("account_id", "this_match_amount");
        IFilter filter1 = new Filter();
        filter1.setFieldName("credit_api_name");
        filter1.setOperator(Operator.EQ);
        filter1.setFieldValues(Lists.newArrayList(ApiNames.PAYMENT_OBJ));

        IFilter filter2 = new Filter();
        filter2.setFieldName("debit_api_name");
        filter2.setOperator(Operator.EQ);
        filter2.setFieldValues(Lists.newArrayList(ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ));

        IFilter filter3 = new Filter();
        filter3.setFieldName("debit_data_id");
        filter3.setOperator(Operator.IN);
        filter3.setFieldValues(accountsReceivableNoteIds);

        List<IFilter> filters = Lists.newArrayList(filter1, filter2, filter3);
        return getObjByFilter(context.getTenantId(), ApiNames.MATCH_NOTE_OBJ, field, filters, context);
    }

    public void handleOrderTable(SalesStatementsDetail.Shop shop, Map<String, String> unitIdToName, Map<String, String> productIdToName, boolean multipleEnable, ServiceContext context) {
        handleSalesOrderTable(shop, unitIdToName, productIdToName, multipleEnable, context);
        handleReturnedGoodsInvoiceTable(shop, unitIdToName, productIdToName, multipleEnable, context);
    }

    public void handleSalesOrderTable(SalesStatementsDetail.Shop shop, Map<String, String> unitIdToName, Map<String, String> productIdToName, boolean multipleEnable, ServiceContext context) {
        String tenantId = context.getTenantId();
        //销售订单
        for (SalesStatementsDetail.SalesOrder salesOrder : shop.getSalesOrderList()) {
            //订单产品数据
            List<IObjectData> salesOrderProduct = querySalesOrderProductByOrderId(tenantId, salesOrder.getOrderId(), context.getRequestContext());
            List<String> productIds = salesOrderProduct.stream().map(i -> i.get("product_id", String.class)).distinct().collect(Collectors.toList());
            if (multipleEnable) {
                List<String> unitIds = salesOrderProduct.stream().map(i -> i.get("actual_unit", String.class)).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
                queryUnitInfoByIds(tenantId, unitIds, context.getRequestContext()).forEach(i -> unitIdToName.put(i.getId(), i.getName()));
            }
            queryProductByIds(tenantId, productIds, context.getRequestContext()).forEach(i -> productIdToName.put(i.getId(), i.getName()));
            salesOrder.setOrderProductTable(new SalesStatementsDetail.OrderProductTable(context.getLang().getValue()));
            for (IObjectData orderProductData : salesOrderProduct) {
                SalesStatementsDetail.OrderProduct orderProduct = new SalesStatementsDetail.OrderProduct();
                orderProduct.setProductId(orderProductData.get("product_id", String.class));
                orderProduct.setProductName(productIdToName.getOrDefault(orderProduct.getProductId(), ""));
                orderProduct.setIsGiveaway(String.valueOf(orderProductData.get("is_giveaway", String.class, "0")));
                orderProduct.setQuantity(orderProductData.get("quantity", BigDecimal.class, BigDecimal.ZERO));
                orderProduct.setUnit(unitIdToName.getOrDefault(orderProductData.get("actual_unit", String.class), orderProductData.get("unit", String.class)));
                orderProduct.setUnitPrice(orderProductData.get("sales_price", String.class));
                orderProduct.setSubtotal(orderProductData.get("subtotal", BigDecimal.class, BigDecimal.ZERO));
                salesOrder.getOrderProductTable().getOrderProductList().add(orderProduct);
            }
        }
    }

    public void handleReturnedGoodsInvoiceTable(SalesStatementsDetail.Shop shop, Map<String, String> unitIdToName, Map<String, String> productIdToName, boolean multipleEnable, ServiceContext context) {
        String tenantId = context.getTenantId();
        //销售订单
        for (SalesStatementsDetail.ReturnedOrder returnedOrder : shop.getReturnedOrderList()) {
            //退货单产品
            List<IObjectData> returnedGoodsInvoiceProduct = queryReturnedGoodsInvoiceProductObjByOrderId(tenantId, returnedOrder.getOrderId(), context.getRequestContext());
            List<String> returnProductIds = returnedGoodsInvoiceProduct.stream().map(i -> i.get("product_id", String.class)).distinct().filter(i -> !productIdToName.containsKey(i)).collect(Collectors.toList());
            String quantityFieldApiName = "quantity", priceFieldApiName = "returned_product_price";
            if (multipleEnable) {
                quantityFieldApiName = "auxiliary_quantity";
                priceFieldApiName = "auxiliary_returned_product_price";
                List<String> returnUnitIds = returnedGoodsInvoiceProduct.stream().map(i -> i.get("actual_unit", String.class)).filter(StringUtils::isNotBlank).distinct().filter(i -> !unitIdToName.containsKey(i)).collect(Collectors.toList());
                queryUnitInfoByIds(tenantId, returnUnitIds, context.getRequestContext()).forEach(i -> unitIdToName.put(i.getId(), i.getName()));
            }
            queryProductByIds(tenantId, returnProductIds, context.getRequestContext()).forEach(i -> productIdToName.put(i.getId(), i.getName()));
            returnedOrder.setOrderProductTable(new SalesStatementsDetail.OrderProductTable(context.getLang().getValue()));
            for (IObjectData rgiProduct : returnedGoodsInvoiceProduct) {
                SalesStatementsDetail.OrderProduct orderProduct = new SalesStatementsDetail.OrderProduct();
                orderProduct.setProductId(rgiProduct.get("product_id", String.class));
                orderProduct.setProductName(productIdToName.getOrDefault(orderProduct.getProductId(), ""));
                orderProduct.setIsGiveaway("0");
                orderProduct.setQuantity(rgiProduct.get(quantityFieldApiName, BigDecimal.class, BigDecimal.ZERO));
                orderProduct.setUnit(unitIdToName.getOrDefault(rgiProduct.get("actual_unit", String.class), rgiProduct.get("unit", String.class)));
                orderProduct.setUnitPrice(rgiProduct.get(priceFieldApiName, String.class));
                orderProduct.setSubtotal(rgiProduct.get("subtotal", BigDecimal.class, BigDecimal.ZERO));
                returnedOrder.getOrderProductTable().getOrderProductList().add(orderProduct);
            }
        }
    }

    public void fillAccountInfoByAccountId(ServiceContext context, String accountId, SalesStatementsDetail.Shop shop) {
        try {
            shop.setAccountId(accountId);
            IObjectData account = getAccountById(context.getTenantId(), accountId, context.getRequestContext());
            shop.setAccountName(account.get("name", String.class, "-"));
            List<Map<String, Object>> doorPhotoList = getList(account.get("door_photo"));
            if (doorPhotoList != null && !doorPhotoList.isEmpty()) {
                Map<String, Object> doorPhotoMap = doorPhotoList.get(0);
                if (doorPhotoMap != null) {
                    shop.setDoorPhoto(doorPhotoMap);
                }
            }
        } catch (Exception ignore) {
        }
    }

    public void handlePaymentV1(List<SalesStatementsDetail.Shop> shops, Map<String, SalesStatementsDetail.Shop> accountIdToShopMap, List<IObjectData> allPaymentList, ServiceContext context) {
        for (IObjectData payment : allPaymentList) {
            String accountId = payment.get("account_id", String.class);
            BigDecimal paymentAmount = payment.get("amount", BigDecimal.class, BigDecimal.ZERO);
            String collectionType = payment.get("collection_type", String.class);//Blue  Red
            SalesStatementsDetail.Shop shop = getAccountShopOrDefault(shops, accountIdToShopMap, accountId, context);
            if ("Red".equals(collectionType)) {
                //退款金额
                shop.setRefundAmount(amountPlus(paymentAmount.abs(), shop.getRefundAmount()));
                continue;
            }
            String purpose = payment.get("purpose", String.class); //1.销售收款 2.预存款 3.定金 4.欠款
            String payType = payment.get("pay_type", String.class);//1.线下支付 2.在线支付 3.二维码收款
            boolean isReceiveCash = StringUtils.isBlank(payType) || "1".equals(payType);
            boolean isOnlinePayment = "2".equals(payType) || "3".equals(payType);
            if (("1".equals(purpose) || StringUtils.isBlank(purpose)) && isReceiveCash) {
                shop.setCashPayable(amountPlus(paymentAmount, shop.getCashPayable()));
                shop.setSalesReceivedAmount(amountPlus(paymentAmount, shop.getSalesReceivedAmount()));
                shop.setReceiveCash(amountPlus(paymentAmount, shop.getReceiveCash()));
            } else if (("1".equals(purpose) || StringUtils.isBlank(purpose)) && isOnlinePayment) {
                shop.setSalesReceivedAmount(amountPlus(paymentAmount, shop.getSalesReceivedAmount()));
                shop.setOnlinePayment(amountPlus(paymentAmount, shop.getOnlinePayment()));
            } else if ("4".equals(purpose)) {
                if (isReceiveCash) {
                    shop.setCashPayable(amountPlus(paymentAmount, shop.getCashPayable()));
                    shop.setReceiveDebt(amountPlus(paymentAmount, shop.getReceiveDebt()));
                    shop.setReceiveDebtCash(amountPlus(paymentAmount, shop.getReceiveDebtCash()));
                } else if (isOnlinePayment) {
                    shop.setReceiveDebt(amountPlus(paymentAmount, shop.getReceiveDebt()));
                    shop.setReceiveDebtOnlinePayment(amountPlus(paymentAmount, shop.getReceiveDebtOnlinePayment()));
                }
            } else if ("2".equals(purpose)) {
                if (isReceiveCash) {
                    shop.setCashPayable(amountPlus(paymentAmount, shop.getCashPayable()));
                    shop.setReceivePrepaidDeposit(amountPlus(paymentAmount, shop.getReceivePrepaidDeposit()));
                    shop.setReceivePrepaidDepositCash(amountPlus(paymentAmount, shop.getReceivePrepaidDepositCash()));
                } else if (isOnlinePayment) {
                    shop.setReceivePrepaidDeposit(amountPlus(paymentAmount, shop.getReceivePrepaidDeposit()));
                    shop.setReceivePrepaidDepositOnlinePayment(amountPlus(paymentAmount, shop.getReceivePrepaidDepositOnlinePayment()));
                }
            }
        }
    }

    public void handleQuoteOrderDeliveryNoteV1(List<SalesStatementsDetail.Shop> shops, Map<String, SalesStatementsDetail.Shop> accountIdToShopMap, List<IObjectData> allQuoteOrderDeliveryNote, ServiceContext context) {
        for (IObjectData deliveryNote : allQuoteOrderDeliveryNote) {
            String accountId = deliveryNote.get("account_id", String.class);
            BigDecimal totalDeliveryMoney = deliveryNote.get("total_delivery_money", BigDecimal.class, BigDecimal.ZERO);
            SalesStatementsDetail.Shop shop = getAccountShopOrDefault(shops, accountIdToShopMap, accountId, context);
            shop.setDeliveryAmount(amountPlus(totalDeliveryMoney, shop.getDeliveryAmount()));
            shop.setDeliveryCount(shop.getDeliveryCount() + 1);
        }
    }

    public SalesStatementsDetail.Shop getAccountShopOrDefault(List<SalesStatementsDetail.Shop> shops, Map<String, SalesStatementsDetail.Shop> accountIdToShopMap, String accountId, ServiceContext context) {
        if (!accountIdToShopMap.containsKey(accountId)) {
            SalesStatementsDetail.Shop shop = new SalesStatementsDetail.Shop();
            fillAccountInfoByAccountId(context, accountId, shop);
            accountIdToShopMap.put(accountId, shop);
            shops.add(shop);
            return shop;
        }
        return accountIdToShopMap.get(accountId);
    }

    public void handleSalesOrderV1(List<SalesStatementsDetail.Shop> shops, Map<String, SalesStatementsDetail.Shop> accountIdToShopMap, List<IObjectData> allSalesOrder, ServiceContext context) {
        Map<String, List<IObjectData>> accountIdToSalesOrder = allSalesOrder.stream()
                .collect(Collectors.groupingBy(i -> i.get("account_id", String.class)));
        accountIdToSalesOrder.forEach((accountId, salesOrderList) -> {
            SalesStatementsDetail.Shop shop = new SalesStatementsDetail.Shop();
            fillAccountInfoByAccountId(context, accountId, shop);
            for (IObjectData salesOrderData : salesOrderList) {
                SalesStatementsDetail.SalesOrder salesOrder = new SalesStatementsDetail.SalesOrder();
                salesOrder.setOrderId(salesOrderData.getId());
                salesOrder.setOrderNum(salesOrderData.getName());
                salesOrder.setSalesAmount(salesOrderData.get("order_amount", BigDecimal.class, BigDecimal.ZERO));
                salesOrder.setReceivedAmount(salesOrderData.get("payment_amount", BigDecimal.class, BigDecimal.ZERO));
                salesOrder.setOrderWaitPaymentAmount(salesOrderData.get("receivable_amount", BigDecimal.class, BigDecimal.ZERO));
                salesOrder.setTotalDiscount(Stream.of(
                                salesOrderData.get("coupon_amount", BigDecimal.class, BigDecimal.ZERO).abs(),
                                salesOrderData.get("rebate_amount", BigDecimal.class, BigDecimal.ZERO).abs(),
                                salesOrderData.get("policy_dynamic_amount", BigDecimal.class, BigDecimal.ZERO).abs())
                        .reduce(BigDecimal.ZERO, BigDecimal::add, BigDecimal::add)
                );
                salesOrder.setDynamicAmount(salesOrderData.get("dynamic_amount", BigDecimal.class, BigDecimal.ZERO));
                salesOrder.setUseReceive(salesOrderData.get("no_rebate_faccount_amount", BigDecimal.class, BigDecimal.ZERO));

                shop.setSalesAmount(amountPlus(salesOrder.getSalesAmount(), shop.getSalesAmount()));
                if (!"quote_order".equals(salesOrderData.get("sales_mode", String.class))) {
                    shop.setShipmentAmount(amountPlus(salesOrderData.get("delivered_amount_sum", BigDecimal.class, BigDecimal.ZERO), shop.getShipmentAmount()));
                }
                shop.setOrderWaitPaymentAmount(amountPlus(salesOrder.getOrderWaitPaymentAmount(), shop.getOrderWaitPaymentAmount()));
                shop.setDynamicAmount(amountPlus(salesOrder.getDynamicAmount(), shop.getDynamicAmount()));
                shop.setCouponDiscount(amountPlus(salesOrderData.get("coupon_amount", BigDecimal.class, BigDecimal.ZERO), shop.getCouponDiscount()));
                shop.setPromotionActivitiesDiscountAmount(amountPlus(salesOrderData.get("policy_dynamic_amount", BigDecimal.class, BigDecimal.ZERO), shop.getPromotionActivitiesDiscountAmount()));
                shop.setRebateAmount(amountPlus(salesOrderData.get("rebate_amount", BigDecimal.class, BigDecimal.ZERO), shop.getRebateAmount()));
                shop.setOrderAmount(amountPlus(salesOrderData.get("product_amount", BigDecimal.class, BigDecimal.ZERO), shop.getOrderAmount()));
                shop.setUseReceive(amountPlus(salesOrder.getUseReceive(), shop.getUseReceive()));
                shop.setTotalDiscount(amountPlus(salesOrder.getTotalDiscount(), shop.getTotalDiscount()));
                shop.getSalesOrderList().add(salesOrder);
                shop.setSalesOrderListTag(I18NSimpleUtils.get("SalesOrderObj.attribute.self.display_name", context.getLang().getValue(), "销售订单"));
            }
            shops.add(shop);
            accountIdToShopMap.put(accountId, shop);
        });
    }

    private void handleMatchNoteObjData(List<SalesStatementsDetail.Shop> shops, Map<String, SalesStatementsDetail.Shop> accountIdToShopMap, List<IObjectData> allMatchNoteObj, ServiceContext context) {
        for (IObjectData matchNote : allMatchNoteObj) {
            String accountId = matchNote.get("account_id", String.class);
            SalesStatementsDetail.Shop shop = getAccountShopOrDefault(shops, accountIdToShopMap, accountId, context);
            shop.setReturnGoodsOldDeductionAmount(amountPlus(matchNote.get("this_match_amount", BigDecimal.class, BigDecimal.ZERO), shop.getReturnGoodsOldDeductionAmount()));
        }
    }

    public void handleReturnedGoodsInvoiceV1(List<SalesStatementsDetail.Shop> shops, Map<String, SalesStatementsDetail.Shop> accountIdToShopMap, List<IObjectData> returnedGoodsInvoiceList, ServiceContext context) {
        if (CollectionUtils.isEmpty(returnedGoodsInvoiceList)) {
            return;
        }
        List<String> returnNoteIds = returnedGoodsInvoiceList.stream().map(DBRecord::getId).collect(Collectors.toList());
        Map<String, BigDecimal> returnNoteIdToTotalAmountMap = new HashMap<>();
        boolean enableCostManagementCalculate = stockBusiness.isEnableCostManagementCalculate(Integer.parseInt(context.getTenantId()));
        if (enableCostManagementCalculate) {
            //开启成本管理
            List<IObjectData> goodsReceivedNoteByReturnNoteIds = getGoodsReceivedNoteByReturnNoteIds(context.getTenantId(), returnNoteIds, context.getRequestContext());
            log.info("goodsReceivedNoteByReturnNoteIds={}", JSONObject.toJSONString(goodsReceivedNoteByReturnNoteIds));
            for (IObjectData goodsReceivedNoteByReturnNoteId : goodsReceivedNoteByReturnNoteIds) {
                returnNoteIdToTotalAmountMap.put(goodsReceivedNoteByReturnNoteId.get("return_note_id", String.class), goodsReceivedNoteByReturnNoteId.get("total_amount", BigDecimal.class, BigDecimal.ZERO));
            }
        }

        for (IObjectData rgi : returnedGoodsInvoiceList) {
            String accountId = rgi.get("account_id", String.class);
            SalesStatementsDetail.ReturnedOrder returnedOrder = new SalesStatementsDetail.ReturnedOrder();
            returnedOrder.setOrderId(rgi.getId());
            returnedOrder.setOrderNum(rgi.getName());
            returnedOrder.setIsRefunded("0".equals(rgi.get("pending_refund_amount", String.class, "1")));
            returnedOrder.setSalesReturnAmount(rgi.get("returned_goods_inv_amount", BigDecimal.class, BigDecimal.ZERO));
            returnedOrder.setReturnGoodsDeductionAmount(rgi.get("car_sales_deduction_amount", BigDecimal.class, BigDecimal.ZERO));
            returnedOrder.setRefundAmount(rgi.get("car_sales_returned_amount", BigDecimal.class, BigDecimal.ZERO));
            returnedOrder.setTotalSettledAmount(rgi.get("total_settled_amount", BigDecimal.class, BigDecimal.ZERO).abs());
            SalesStatementsDetail.Shop shop = getAccountShopOrDefault(shops, accountIdToShopMap, accountId, context);
            //退
            shop.setSalesReturnAmount(amountPlus(returnedOrder.getSalesReturnAmount(), shop.getSalesReturnAmount()));
            shop.setReturnGoodsDeductionAmount(amountPlus(returnedOrder.getReturnGoodsDeductionAmount(), shop.getReturnGoodsDeductionAmount()));
            shop.setTotalSettledAmount(amountPlus(returnedOrder.getTotalSettledAmount(), shop.getTotalSettledAmount()));
            shop.getReturnedOrderList().add(returnedOrder);
            shop.setReturnedOrderListTag(I18NSimpleUtils.get("ReturnedGoodsInvoiceObj.attribute.self.display_name", context.getLang().getValue(), "退货单"));
            if (enableCostManagementCalculate) {
                shop.setReturnReceivedStorageAmount(amountPlus(returnNoteIdToTotalAmountMap.get(rgi.getId()), shop.getReturnReceivedStorageAmount()));
            } else {
                shop.setReturnReceivedStorageAmount(shop.getSalesReturnAmount());
            }

            // 兼容换出大于换入的换货场景
            String recordType = rgi.get(CommonApiNames.RECORD_TYPE, String.class);
            BigDecimal returnedGoodsInvAmount = rgi.get(ReturnedGoodsInvoiceApiNames.RETURNED_GOODS_INV_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
            if (!Objects.equals(recordType, ReturnedGoodsInvoiceApiNames.EXCHANGE_TYPE) || returnedGoodsInvAmount.doubleValue() >= 0) {
                shop.setSalesReturnAmountV2(amountPlus(returnedOrder.getSalesReturnAmount(), shop.getSalesReturnAmountV2()));
                shop.setTotalSettledAmountV2(amountPlus(returnedOrder.getTotalSettledAmount(), shop.getTotalSettledAmountV2()));
            }
        }
    }

    @Override
    public void initSalesStatementsModule(String tenantId) {
        try {
            User superUser = User.systemUser(tenantId);
            describeManagerService.addFields(tenantId, ApiNames.SALES_ORDER_OBJ, "statement_status", "sales_statements_ids");
            describeManagerService.addFields(tenantId, ApiNames.RETURNED_GOODS_INVOICE_OBJ, "statement_status", "sales_statements_ids");
            describeManagerService.addFields(tenantId, ApiNames.PAYMENT_OBJ, "sales_statements_ids");
            configService.upsertTenantConfig(superUser, OPEN_SALES_STATEMENTS_CONFIG_KEY, "true", ConfigValueType.STRING);
            log.info("ei {}, configService.upsertTenantConfig {}", tenantId, OPEN_SALES_STATEMENTS_CONFIG_KEY);
            //预设按钮
            describeManagerService.createButton(tenantId, ApiNames.SALES_STATEMENTS_OBJ,
                    Lists.newArrayList("SubmitAccount_button_default", "CloseAccount_button_default", "SettleAccount_button_default"),
                    Lists.newArrayList(RoleCode.SYSTEM_ADMINISTRATOR));
        } catch (Exception e) {
            log.error("对账单初始化失败 reason={}, tenantId={}", e.getMessage(), tenantId);
            throw new MetaDataException(e.getMessage());
        }
    }

    @ServiceMethod("addFieldDescribe")
    public AddFieldDescribe.Result addFieldDescribe(AddFieldDescribe.Arg arg, ServiceContext context) {
        if (CollectionUtils.isEmpty(arg.getObjectApiNames()) || CollectionUtils.isEmpty(arg.getTenantIds())) {
            return AddFieldDescribe.Result.error("objectApiNames or tenantIds is empty.");
        }
        List<String> objectApiNames = Lists.newArrayList(ApiNames.SALES_ORDER_OBJ, ApiNames.RETURNED_GOODS_INVOICE_OBJ);
        if (arg.getObjectApiNames().stream().anyMatch(i -> !objectApiNames.contains(i))) {
            return AddFieldDescribe.Result.error("objectApiName support : " + objectApiNames);
        }
        try {
            for (String tenantId : arg.getTenantIds()) {
                for (String describeApiName : arg.getObjectApiNames()) {
                    log.info("ei {}, {} addFields statement_status", tenantId, describeApiName);
                    describeManagerService.addFields(tenantId, describeApiName, "statement_status");
                }
            }
            return AddFieldDescribe.Result.success();
        } catch (Exception e) {
            log.error("addFieldDescribe error", e);
            return AddFieldDescribe.Result.error("unknown " + e.getMessage());
        }
    }

    @Override
    @ServiceMethod("openStatus")
    public int salesStatementsModuleOpenStatus(ServiceContext context) {
        User superUser = User.systemUser(context.getTenantId());
        String isOpen = configService.findTenantConfig(superUser, OPEN_SALES_STATEMENTS_CONFIG_KEY);
        if (StringUtils.isNotBlank(isOpen) && Boolean.TRUE.toString().equals(isOpen)) {
            return 1;
        }
        return 0;
    }


    @ServiceMethod("updateStatementsStatus")
    public UpdateStatements.Result updateStatementsStatus(UpdateStatements.Arg arg, ServiceContext context) {
        log.info("updateStatementsStatus arg={}", arg);
        if (Objects.isNull(arg)) {
            return UpdateStatements.Result.error("arg is empty");
        }
        if (StringUtils.isBlank(arg.getDataId())) {
            return UpdateStatements.Result.error("dataId is empty");
        }
        List<IObjectData> objectDataByIds = serviceFacade.findObjectDataByIds(context.getTenantId(), Lists.newArrayList(arg.getDataId()), ApiNames.SALES_STATEMENTS_OBJ);
        log.info("updateStatementsStatus serviceFacade.findObjectDataByIds={}", JSONObject.toJSONString(objectDataByIds));
        if (CollectionUtils.isEmpty(objectDataByIds)) {
            return UpdateStatements.Result.error("未找到对账单数据");
        }
        IObjectData objectData = objectDataByIds.get(0);
        String statementStatus = objectData.get("statement_status", String.class);
        if (SalesStatementsObjConformanceStatementsAction.CLOSE.equals(arg.getStatus())) {
            if (!Boolean.TRUE.equals(arg.getIsCancel())) {
                arg.setStatus(statementStatus);
            }
        }
        //修改对账单状态
        Map<String, Object> updateMap = new HashMap<>(4);
        updateMap.put("statement_status", arg.getStatus());
        updateMap.put("payer", Lists.newArrayList(context.getUser().getUserId()));
        if (Objects.nonNull(arg.getFromArgs())) {
            if (arg.getFromArgs().containsKey("from_actual_payment")) {
                BigDecimal formActualPayment = Objects.isNull(arg.getFromArgs().getBigDecimal("from_actual_payment")) ? BigDecimal.ZERO : arg.getFromArgs().getBigDecimal("from_actual_payment");
                updateMap.put("actual_payment", formActualPayment);
                BigDecimal payableAmount = objectData.get("payable_amount", BigDecimal.class, BigDecimal.ZERO);
                BigDecimal actualPayment = objectData.get("actual_payment", BigDecimal.class, BigDecimal.ZERO);
                BigDecimal cashTotal = payableAmount.add(actualPayment);
                log.info("cashTotal={}", cashTotal);
                if (SalesStatementsObjConformanceStatementsAction.CLOSE_OFF.equals(arg.getStatus()) && cashTotal.compareTo(formActualPayment) > 0) {
                    arg.setStatus(statementStatus);
                    updateMap.put("statement_status", statementStatus);
                    log.info("钱未交够, 无法进入结清状态");
                }
            }
        }
        IObjectDescribe describe = serviceFacade.findObject(context.getTenantId(), ApiNames.SALES_STATEMENTS_OBJ);
        updateStatementsStatus(objectData, updateMap, arg.getStatus(), describe, context);
        return UpdateStatements.Result.success();
    }

    public IObjectData updateStatementsStatus(IObjectData objectData, Map<String, Object> updateMap, String status, IObjectDescribe describe, ServiceContext context) {
        IObjectData oldData = ObjectDataExt.of(objectData).copy();
        IObjectData newData = ObjectDataExt.of(objectData).copy();
        IObjectData result = serviceFacade.updateWithMap(context.getUser(), objectData, updateMap);
        updateMap.forEach(newData::set);
        Map<String, Object> diffMap = ObjectDataExt.of(newData).diff(oldData, describe);
        taskExecutor.execute(MonitorTaskWrapper.wrap(() -> {
            serviceFacade.log(context.getUser(), EventType.MODIFY, ActionType.Modify, describe, newData, diffMap, oldData);
        }));
        if (StringUtils.isBlank(status)) {
            return result;
        }
        //修改销售订单状态
        SalesStatementsConformance.Arg conformanceArg = new SalesStatementsConformance.Arg();
        conformanceArg.setId(objectData.getId());
        conformanceArg.setSalesman(objectData.getOwner());
        conformanceArg.setDate(objectData.get("statement_date", Long.class));
        conformanceArg.setStatementStatus(status);
        conformanceStatements(conformanceArg, context);
        return result;
    }

    /**
     * 确认对账
     */
    @Override
    public void conformanceStatements(SalesStatementsConformance.Arg arg, ServiceContext context) {
        List<Long> dateInterval = DateUtils.getTimeStampStartTimeAndEndTime(arg.getDate());
        String tenantId = context.getTenantId();
        List<IObjectData> allSalesOrder = Lists.newArrayList();
        List<IObjectData> allReturnedGoodsInvoice = Lists.newArrayList();
        List<IObjectData> allPaymentList = Lists.newArrayList();
        List<IObjectData> allQuoteOrderDeliveryNote = Lists.newArrayList();
        List<IObjectData> allMatchNoteList = Lists.newArrayList();
        fillOrderData(arg.getSalesman(), dateInterval, allSalesOrder, allReturnedGoodsInvoice, allPaymentList, allQuoteOrderDeliveryNote, allMatchNoteList, context);
        if (CollectionUtils.isEmpty(allSalesOrder) && CollectionUtils.isEmpty(allReturnedGoodsInvoice)
                && CollectionUtils.isEmpty(allPaymentList) && CollectionUtils.isEmpty(allMatchNoteList) && CollectionUtils.isEmpty(allQuoteOrderDeliveryNote)) {
            return;
        }
        log.info("allOrder={}", JSONObject.toJSONString(allSalesOrder));
        log.info("allReturnedGoodsInvoice={}", JSONObject.toJSONString(allReturnedGoodsInvoice));
        Map<String, Object> fieldMap = Maps.newHashMap();
        fieldMap.put("statement_status", arg.getStatementStatus());
        updateObj(tenantId, ApiNames.SALES_ORDER_OBJ, allSalesOrder, fieldMap);
        // updateObj(tenantId, ApiNames.RETURNED_GOODS_INVOICE_OBJ, allReturnedGoodsInvoice, fieldMap);
        if (SalesStatementsObjConformanceStatementsAction.CLOSE.equals(arg.getStatementStatus())) {
            salesStatementsDao.delete(SalesStatementsDetailEntity.class, arg.getId());
            log.info("deleteSalesStatementsDetailEntity success");
            return;
        }
        if (SalesStatementsObjConformanceStatementsAction.CLOSE_OFF.equals(arg.getStatementStatus())) {
            SalesStatementsDetailEntity salesStatementsDetailEntity = salesStatementsDao.get(SalesStatementsDetailEntity.class, arg.getId());
            if (Objects.nonNull(salesStatementsDetailEntity)) {
                SalesStatementsDetail.Result mongoResult = JSON.parseObject(salesStatementsDetailEntity.getData(), SalesStatementsDetail.Result.class);
                if (mongoResult.getErrorCode() != 0) {
                    salesStatementsDao.delete(SalesStatementsDetailEntity.class, arg.getId());
                } else {
                    return;
                }
            }
        }
        List<IObjectData> finalAllMatchNoteList = allMatchNoteList;
        List<IObjectData> finalAllQuoteOrderDeliveryNote = allQuoteOrderDeliveryNote;
        taskExecutor.execute(MonitorTaskWrapper.wrap(() -> {
            //存储快照
            try {
                SalesStatementsDetail.Result result = new SalesStatementsDetail.Result(context.getLang().getValue());
                List<SalesStatementsDetail.Shop> shopList = getShopList(tenantId, allSalesOrder, allReturnedGoodsInvoice, allPaymentList, finalAllMatchNoteList, finalAllQuoteOrderDeliveryNote, true, context);
                result.setDataDetailList(shopList);
                result.setErrorCode(0);
                result.setErrorMsg("success");
                salesStatementsDao.saveSalesStatementsDetailEntity(arg.getId(), String.valueOf(tenantId), JSON.toJSONString(result));
                log.info("saveSalesStatementsDetailEntity success");
            } catch (Exception e) {
                log.error("对账单快照存储失败 message={}, _id={},salesman={},dateInterval={}, tenantId={}, allSalesOrder={}", e.getMessage(), arg.getId(), arg.getSalesman(), dateInterval, tenantId, allSalesOrder);
            }
        }));
    }


    private BigDecimal amountPlus(BigDecimal price1, BigDecimal price2) {
        if (price1 == null) {
            price1 = BigDecimal.ZERO;
        }
        if (price2 == null) {
            price2 = BigDecimal.ZERO;
        }
        return price1.add(price2);
    }

    private BigDecimal amountSubtract(BigDecimal price1, BigDecimal... price2) {
        if (price1 == null) {
            price1 = BigDecimal.ZERO;
        }
        for (BigDecimal bigDecimal : price2) {
            if (bigDecimal == null) {
                continue;
            }
            price1 = price1.subtract(bigDecimal);
        }
        return price1;
    }

    private <E> List<E> getList(Object value) {
        if (null == value) {
            return Lists.newArrayList();
        } else {
            String str;
            if (value instanceof String) {
                str = (String) value;
            } else {
                str = JSON.toJSONString(value);
            }
            return JSON.parseObject(str, new TypeReference<List<E>>() {
            });
        }
    }


    private List<IObjectData> queryStatementsByDateAndOwner(List<String> salesmanName, Long salesStatementDate, RequestContext context) {
        List<String> fields = Lists.newArrayList("statement_status", "actual_payment", "receive_prepaid_deposit", "receive_debt", "receive_cash", "online_payment",
                "sales_return_amount", "shipment_amount", "sales_amount", "received_amount", "online_received", "gross_receipts", "payable_amount", "use_receive", "owed_amount",
                "refunded_amount", "delivery_amount", "delivery_count", "receivable_amount");
        IFilter filter1 = new Filter();
        filter1.setFieldName("salesman_name");
        filter1.setOperator(Operator.IN);
        filter1.setFieldValues(salesmanName);
        IFilter filter2 = new Filter();
        filter2.setFieldName("statement_date");
        filter2.setOperator(Operator.EQ);
        filter2.setFieldValues(Lists.newArrayList(salesStatementDate.toString()));
        List<IFilter> filterList = Lists.newArrayList(filter1, filter2);
        return getObjByFilter(context.getTenantId(), ApiNames.SALES_STATEMENTS_OBJ, fields, filterList, context);
    }

    private List<IObjectData> getCarSalesOrderByDateAndOwner(String tenantId, List<Long> dateInterval, List<String> owner, RequestContext context) {
        List<String> dateIntervalList = dateInterval.stream().map(String::valueOf).collect(Collectors.toList());
        IFilter filter1 = new Filter();
        filter1.setFieldName("create_time");
        filter1.setOperator(Operator.BETWEEN);
        filter1.setFieldValues(dateIntervalList);

        IFilter filter2 = new Filter();
        filter2.setFieldName("owner");
        filter2.setOperator(Operator.EQ);
        filter2.setFieldValues(owner);

        String pattern = "1 and 2";
        List<IFilter> filters = Lists.newArrayList(filter1, filter2);
        return querySalesOrderByDateAndOwner(tenantId, pattern, filters, context);
    }

    private List<IObjectData> getDeliveryNoteByDateAndOwnerAndFilterQuote(String tenantId, List<Long> dateInterval, List<String> owner, RequestContext context) {
        List<IObjectData> deliveryNoteByDateAndOwner = getDeliveryNoteByDateAndOwner(tenantId, dateInterval, owner, context);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(deliveryNoteByDateAndOwner)) {
            return Collections.emptyList();
        }
        Set<String> salesOrderId = deliveryNoteByDateAndOwner.stream().map(i -> i.get("sales_order_id", String.class)).collect(Collectors.toSet());
        IFilter filter1 = new Filter();
        filter1.setFieldName("_id");
        filter1.setOperator(Operator.IN);
        filter1.setFieldValues(new ArrayList<>(salesOrderId));
        IFilter filter2 = new Filter();
        filter2.setFieldName("sales_mode");
        filter2.setOperator(Operator.EQ);
        filter2.setFieldValues(Lists.newArrayList("quote_order"));
        String pattern = "1 and 2";
        List<IObjectData> salesOrderByDateAndOwner = querySalesOrderByDateAndOwner(tenantId, pattern, Lists.newArrayList(filter1, filter2), context);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(salesOrderByDateAndOwner)) {
            return Collections.emptyList();
        }
        Set<String> idSet = salesOrderByDateAndOwner.stream().map(DBRecord::getId).collect(Collectors.toSet());
        return deliveryNoteByDateAndOwner.stream().filter(i -> idSet.contains(i.get("sales_order_id", String.class))).collect(Collectors.toList());
    }

    private List<IObjectData> getDeliveryNoteByDateAndOwner(String tenantId, List<Long> dateInterval, List<String> owner, RequestContext context) {
        List<String> dateIntervalList = dateInterval.stream().map(String::valueOf).collect(Collectors.toList());
        List<String> field = Lists.newArrayList("account_id", "delivery_date", "owner", "total_delivery_money", "sales_order_id");
        IFilter filter1 = new Filter();
        filter1.setFieldName("delivery_date");
        filter1.setOperator(Operator.BETWEEN);
        filter1.setFieldValues(dateIntervalList);

        IFilter filter2 = new Filter();
        filter2.setFieldName("owner");
        filter2.setOperator(Operator.EQ);
        filter2.setFieldValues(owner);

        List<IFilter> filters = Lists.newArrayList(filter1, filter2);
        return getObjByFilter(tenantId, ApiNames.DELIVERY_NOTE_OBJ, field, filters, context);
    }

    private List<IObjectData> getGoodsReceivedNoteByReturnNoteIds(String tenantId, List<String> returnNoteIds, RequestContext context) {
        List<String> field = Lists.newArrayList("_id", "return_note_id", "total_amount");
        IFilter filter1 = new Filter();
        filter1.setFieldName("return_note_id");
        filter1.setOperator(Operator.IN);
        filter1.setFieldValues(returnNoteIds);

        List<IFilter> filters = Lists.newArrayList(filter1);
        return getObjByFilter(tenantId, ApiNames.GOODS_RECEIVED_NOTE_OBJ, field, filters, context);
    }

    private List<IObjectData> querySalesOrderByDateAndOwner(String tenantId, String pattern, List<IFilter> filters, RequestContext context) {
        // List<String> dateIntervalList = dateInterval.stream().map(String::valueOf).collect(Collectors.toList());
        List<String> field = Lists.newArrayList(
                "_id", "name", "account_id", "owner", "create_time", "order_amount", "delivered_amount_sum", "payment_amount", "receivable_amount", "returned_goods_amount", "refund_amount",
                "dynamic_amount", "coupon_amount", "policy_dynamic_amount", "rebate_amount", "car_sales_deduction_amount", "price_book_amount", "no_rebate_faccount_amount", "paid_amount",
                "hist_offset_amount", "settled_amount", "sales_statements_ids", "sales_mode", "product_amount", "statement_status");
        return getObjByFilter(tenantId, ApiNames.SALES_ORDER_OBJ, field, pattern, filters, context);
    }

    private List<IObjectData> queryReturnedGoodsInvoiceByDateAndOwner(String tenantId, List<Long> dateInterval, List<String> owner, RequestContext context) {
        List<String> dateIntervalList = dateInterval.stream().map(String::valueOf).collect(Collectors.toList());
        List<String> field = Lists.newArrayList(CommonApiNames.RECORD_TYPE, "account_id", "name", "owner", "create_time", "returned_goods_inv_amount", "order_id", "return_mode", "refund_method",
                "car_sales_deduction_amount", "car_sales_returned_amount", "total_settled_amount", "pending_refund_amount", "sales_statements_ids");
        IFilter filter1 = new Filter();
        filter1.setFieldName("create_time");
        filter1.setOperator(Operator.BETWEEN);
        filter1.setFieldValues(dateIntervalList);

        IFilter filter2 = new Filter();
        filter2.setFieldName("owner");
        filter2.setOperator(Operator.EQ);
        filter2.setFieldValues(owner);

        List<IFilter> filters = Lists.newArrayList(filter1, filter2);
        return getObjByFilter(tenantId, ApiNames.RETURNED_GOODS_INVOICE_OBJ, field, filters, context);
    }

    private List<IObjectData> queryPaymentByDateAndOwner(String tenantId, List<Long> dateInterval, List<String> owner, RequestContext context) {
        List<String> dateIntervalList = dateInterval.stream().map(String::valueOf).collect(Collectors.toList());
        List<String> field = Lists.newArrayList("purpose", "payment_term", "owner", "payment_time", "amount", "account_id", "collection_type", "pay_type", "sales_statements_ids");
        IFilter filter1 = new Filter();
        filter1.setFieldName("payment_time");
        filter1.setOperator(Operator.BETWEEN);
        filter1.setFieldValues(dateIntervalList);

        IFilter filter2 = new Filter();
        filter2.setFieldName("owner");
        filter2.setOperator(Operator.EQ);
        filter2.setFieldValues(owner);

        IFilter filter3 = new Filter();
        filter3.setFieldName("life_status");
        filter3.setOperator(Operator.EQ);
        filter3.setFieldValues(Lists.newArrayList("normal"));

        List<IFilter> filters = Lists.newArrayList(filter1, filter2, filter3);
        return getObjByFilter(tenantId, ApiNames.PAYMENT_OBJ, field, filters, context);
    }

    private List<IObjectData> queryMatchNoteObjByDateAndOwner(String tenantId, List<Long> dateInterval, List<String> owner, RequestContext context) {
        List<String> dateIntervalList = dateInterval.stream().map(String::valueOf).collect(Collectors.toList());
        List<String> field = Lists.newArrayList("account_id", "name", "this_match_amount", "verification_method", "debit_data_id", "ar_date");
        IFilter filter1 = new Filter();
        filter1.setFieldName("ar_date");
        filter1.setOperator(Operator.BETWEEN);
        filter1.setFieldValues(dateIntervalList);

        IFilter filter2 = new Filter();
        filter2.setFieldName("owner");
        filter2.setOperator(Operator.EQ);
        filter2.setFieldValues(owner);

        IFilter filter3 = new Filter();
        filter3.setFieldName("verification_method");
        filter3.setOperator(Operator.EQ);
        filter3.setFieldValues(Lists.newArrayList("ArOffsetAr"));

        IFilter filter4 = new Filter();
        filter4.setFieldName("this_match_amount");
        filter4.setOperator(Operator.GT);
        filter4.setFieldValues(Lists.newArrayList("0"));

        List<IFilter> filters = Lists.newArrayList(filter1, filter2, filter3, filter4);
        return getObjByFilter(tenantId, ApiNames.MATCH_NOTE_OBJ, field, filters, context);
    }

    private List<IObjectData> querySalesOrderProductByOrderId(String tenantId, String orderId, RequestContext
            context) {
        List<String> field = Lists.newArrayList("_id", "product_id", "is_giveaway", "quantity", "unit", "sales_price", "subtotal", "actual_unit");
        IFilter filter = new Filter();
        filter.setFieldName("order_id");
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(orderId));
        return getObjByFilter(tenantId, ApiNames.SALES_ORDER_PRODUCT_OBJ, field, Lists.newArrayList(filter), context);
    }

    private List<IObjectData> queryReturnedGoodsInvoiceObjByOrderId(String tenantId, String orderId, RequestContext
            context) {
        List<String> field = Lists.newArrayList("_id", "name", "returned_goods_inv_amount");
        IFilter filter = new Filter();
        filter.setFieldName("order_id");
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(orderId));
        return getObjByFilter(tenantId, ApiNames.RETURNED_GOODS_INVOICE_OBJ, field, Lists.newArrayList(filter), context);
    }

    private List<IObjectData> queryReturnedGoodsInvoiceObjByOrderIds(String
                                                                             tenantId, List<String> orderIds, RequestContext context) {
        List<String> field = Lists.newArrayList("_id", "returned_goods_inv_amount");
        IFilter filter = new Filter();
        filter.setFieldName("order_id");
        filter.setOperator(Operator.IN);
        filter.setFieldValues(orderIds);
        return getObjByFilter(tenantId, ApiNames.RETURNED_GOODS_INVOICE_OBJ, field, Lists.newArrayList(filter), context);
    }

    private List<IObjectData> queryReturnedGoodsInvoiceProductObjByOrderId(String tenantId, String
            rgiId, RequestContext context) {
        List<String> field = Lists.newArrayList("_id", "product_id", "quantity", "unit", "actual_unit", "returned_product_price", "subtotal", "auxiliary_quantity", "auxiliary_returned_product_price");
        IFilter filter = new Filter();
        filter.setFieldName("returned_goods_inv_id");
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(rgiId));
        return getObjByFilter(tenantId, ApiNames.RETURNED_GOODS_INVOICE_PRODUCT_OBJ, field, Lists.newArrayList(filter), context);
    }

    private List<IObjectData> queryUnitInfoByIds(String tenantId, List<String> dataIds, RequestContext context) {
        if (CollectionUtils.isEmpty(dataIds)) {
            return Lists.newArrayList();
        }
        List<String> field = Lists.newArrayList("_id", "name");
        IFilter filter = new Filter();
        filter.setFieldName("_id");
        filter.setOperator(Operator.IN);
        filter.setFieldValues(dataIds);
        return getObjByFilter(tenantId, ApiNames.UNIT_INFO_OBJ, field, Lists.newArrayList(filter), context);
    }

    private List<IObjectData> queryProductByIds(String tenantId, List<String> dataIds, RequestContext context) {
        if (CollectionUtils.isEmpty(dataIds)) {
            return Lists.newArrayList();
        }
        List<String> field = Lists.newArrayList("_id", "name");
        IFilter filter = new Filter();
        filter.setFieldName("_id");
        filter.setOperator(Operator.IN);
        filter.setFieldValues(dataIds);
        return getObjByFilter(tenantId, ApiNames.PRODUCT_OBJ, field, Lists.newArrayList(filter), context);
    }

    private List<IObjectData> getObjByFilter(String tenantId, String
            describeApiName, List<String> field, List<IFilter> filters, RequestContext context) {
        SearchTemplateQuery queryTemplate = new SearchTemplateQuery();
        queryTemplate.setLimit(200);
        queryTemplate.setFilters(filters);
        QueryResult<IObjectData> result = serviceFacade.findBySearchTemplateQueryWithFields(
                ActionContextExt.of(User.systemUser(tenantId), context).getContext(),
                describeApiName,
                queryTemplate,
                field);
        if (result == null) {
            log.info("serviceFacade.findBySearchTemplateQueryWithFields {} error tenantId={}, arg={}", describeApiName, tenantId, queryTemplate);
            throw new ApiException(String.format("paasDataProxy.findByQuery %s error tenantId=%s, arg=%s", describeApiName, tenantId, queryTemplate));
        }
        if (!CollectionUtils.isEmpty(result.getData())) {
            return result.getData();
        }
        return Collections.emptyList();
    }

    private List<IObjectData> getObjByFilter(String tenantId, String describeApiName, List<String> field, String
            pattern, List<IFilter> filters, RequestContext context) {
        SearchTemplateQuery queryTemplate = new SearchTemplateQuery();
        queryTemplate.setLimit(200);
        queryTemplate.setFilters(filters);
        queryTemplate.setPattern(pattern);
        QueryResult<IObjectData> result = serviceFacade.findBySearchTemplateQueryWithFields(
                ActionContextExt.of(User.systemUser(tenantId), context).getContext(),
                describeApiName,
                queryTemplate,
                field);
        if (result == null) {
            log.info("serviceFacade.findBySearchTemplateQueryWithFields {} error tenantId={}, arg={}", describeApiName, tenantId, queryTemplate);
            throw new ApiException(String.format("paasDataProxy.findByQuery %s error tenantId=%s, arg=%s", describeApiName, tenantId, queryTemplate));
        }
        if (!CollectionUtils.isEmpty(result.getData())) {
            return result.getData();
        }
        return Collections.emptyList();
    }

    private void createObj(String tenantId, IObjectData iObjectData) {
        serviceFacade.saveObjectData(User.systemUser(tenantId), iObjectData);
    }

    public IObjectData getSalesStatementsById(String tenantId, String dataId, RequestContext context) {
        List<String> field = Lists.newArrayList("salesman_name", "statement_date");
        IFilter filter = new Filter();
        filter.setFieldName("_id");
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(dataId));
        List<IObjectData> dataList = getObjByFilter(tenantId, ApiNames.SALES_STATEMENTS_OBJ, field, Lists.newArrayList(filter), context);
        return CollectionUtils.isEmpty(dataList) ? null : dataList.get(0);

    }

    public IObjectData getSalesStatementsBySalesmanAndData(String tenantId, List<String> salesman, List<Long> dateInterval, RequestContext context) {
        List<String> field = Lists.newArrayList(
                "_id", "name", "salesman_name", "total_discount", "rebate_amount", "received_amount",
                "shipment_amount", "sales_amount", "receivable_amount", "promotion_activities_discount_amount", "payer", "refunded_amount",
                "statement_date", "checkout_time", "statement_status", "dynamic_amount", "owed_amount", "coupon_discount", "sales_return_amount");
        IFilter filter = new Filter();
        filter.setFieldName("owner");
        filter.setOperator(Operator.IN);
        filter.setFieldValues(salesman);
        IFilter filter1 = new Filter();
        filter1.setFieldName("statement_date");
        filter1.setOperator(Operator.BETWEEN);
        filter1.setFieldValues(dateInterval.stream().map(Object::toString).collect(Collectors.toList()));
        List<IObjectData> dataList = getObjByFilter(tenantId, ApiNames.SALES_STATEMENTS_OBJ, field, Lists.newArrayList(filter, filter1), context);
        return CollectionUtils.isEmpty(dataList) ? null : dataList.get(0);

    }

    public List<IObjectData> getSalesStatementsByIds(String tenantId, List<String> dataIds, RequestContext context) {
        List<String> field = Lists.newArrayList("salesman_name", "statement_date");
        IFilter filter = new Filter();
        filter.setFieldName("_id");
        filter.setOperator(Operator.IN);
        filter.setFieldValues(Lists.newArrayList(dataIds));
        List<IObjectData> dataList = getObjByFilter(tenantId, ApiNames.SALES_STATEMENTS_OBJ, field, Lists.newArrayList(filter), context);
        return CollectionUtils.isEmpty(dataList) ? Collections.emptyList() : dataList;

    }

    private IObjectData getAccountById(String tenantId, String dataId, RequestContext context) {
        List<String> field = Lists.newArrayList("_id", "name", "door_photo");
        IFilter filter = new Filter();
        filter.setFieldName("_id");
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(dataId));
        List<IObjectData> dataList = getObjByFilter(tenantId, ApiNames.ACCOUNT_OBJ, field, Lists.newArrayList(filter), context);
        return CollectionUtils.isEmpty(dataList) ? new ObjectData() : dataList.get(0);
    }

    private void updateObj(String tenantId, String describeApiName, List<IObjectData> objectDataList, Map<String, Object> fieldMap) {
        if (CollectionUtils.isEmpty(objectDataList)) {
            return;
        }
        for (IObjectData iObjectData : objectDataList) {
            iObjectData.setDescribeApiName(describeApiName);
            iObjectData.setTenantId(tenantId);
        }
        List<IObjectData> iObjectData = serviceFacade.batchUpdateWithMap(User.systemUser(tenantId), objectDataList, fieldMap);
        if (iObjectData == null) {
            log.info("serviceFacade.batchUpdateWithMap {} error tenantId={}, objectDataList={}, fieldMap={}", describeApiName, tenantId, objectDataList, fieldMap);
            throw new ApiException(String.format("paasDataProxy.batchIncrementUpdate %s error tenantId=%s", describeApiName, tenantId));
        }
    }


}

