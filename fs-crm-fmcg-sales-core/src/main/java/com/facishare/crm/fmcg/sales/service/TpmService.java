package com.facishare.crm.fmcg.sales.service;

import com.facishare.crm.fmcg.sales.model.tpm.RedPacketDistribution;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.fmcg.framework.http.TPMProxy;
import com.fmcg.framework.http.contract.tpm.CustomerPublish;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR> liuhaoyu
 * @create : 2025-02-25 10:59
 **/
@Service
@Slf4j
@ServiceModule("tpm")
public class TpmService {
    @Resource
    private TPMProxy tpmProxy;

    @ServiceMethod("red_packet_distribution")
    public RedPacketDistribution.Result redPacketDistribution(RedPacketDistribution.Arg arg, ServiceContext serviceContext) {
        RedPacketDistribution.ArgBody data = arg.getArg();
        if (Objects.isNull(data.getDataId()) || Objects.isNull(data.getApiName()) || Objects.isNull(data.getTenantId())) {
            return RedPacketDistribution.Result.error(100001, "Parameter exception");
        }
        CustomerPublish.Arg cpArg = new CustomerPublish.Arg();
        cpArg.setDataId(data.getDataId());
        cpArg.setApiName(data.getApiName());
        cpArg.setTenantId(data.getTenantId());
        log.info("cpArg: {}", cpArg);
        CustomerPublish.Result cpResult = tpmProxy.customerPublishRedPacket(Integer.parseInt(serviceContext.getTenantId()), serviceContext.getUser().getUserIdInt(), cpArg);
        log.info("cpResult: {}", cpResult);
        if (cpResult.getCode() != 0) {
            return RedPacketDistribution.Result.error(cpResult.getCode(), cpResult.getMessage());
        }
        if (Objects.isNull(cpResult.getData())) {
            return RedPacketDistribution.Result.error(100002, "CustomerPublishRedPacket Return data is null");
        }
        if (!cpResult.getData().getSuccess()) {
            return RedPacketDistribution.Result.error(100003, cpResult.getData().getMessage());
        }
        if (StringUtils.isEmpty(cpResult.getData().getRedPackId())) {
            return RedPacketDistribution.Result.error(100004, "CustomerPublishRedPacket redPackId is null");
        }
        return RedPacketDistribution.Result.success(cpResult.getData().getRedPackId(), cpResult.getMessage());
    }
}