package com.facishare.crm.fmcg.sales.cache;

import com.facishare.crm.fmcg.sales.apiname.AccountObjApiNames;
import com.facishare.crm.fmcg.sales.apiname.CommonApiNames;
import com.facishare.crm.fmcg.sales.exception.InspectionVerifyErrorInfo;
import com.facishare.crm.fmcg.sales.exception.InspectionVerifyFailureInterruptException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> liuhaoyu
 * @create : 2025-05-12 19:29
 **/
@Component
@Slf4j
public class AccountCache {
    public static final String ACCOUNT_NO_API_NAME = "account_no__c";
    public static final String ACCOUNT_ID_API_NAME = "account_id__c";
    @Resource
    private ServiceFacade serviceFacade;

    private LoadingCache<String, String> cache;

    @PostConstruct
    public void init() {
        cache = CacheBuilder.newBuilder()
                .maximumSize(5000)
                .expireAfterWrite(4, TimeUnit.HOURS)
                .build(new CacheLoader<String, String>() {
                    @Override
                    public String load(String key) {
                        return queryAccount(key);
                    }
                });
    }

    public String queryAccount(String key) {
        String[] strings = splitKey(key);
        String tenantId = strings[0];
        String objApiName = strings[1];
        String fieldVal1 = strings[2];
        if ("VirtualWarehouse__c".equals(objApiName)) {
            IObjectData virtualWarehouse = findOne(tenantId, "VirtualWarehouse__c", Lists.newArrayList(ACCOUNT_ID_API_NAME),
                    Lists.newArrayList(getFilter(CommonApiNames.NAME, Operator.EQ, Lists.newArrayList(fieldVal1))));
            if (Objects.isNull(virtualWarehouse)) {
                return null;
            }
            return virtualWarehouse.get(ACCOUNT_ID_API_NAME, String.class);
        } else if ("shipping_location__c".equals(objApiName)) {
            IObjectData shippingLocation = findOne(tenantId, "shipping_location__c", Lists.newArrayList(ACCOUNT_ID_API_NAME),
                    Lists.newArrayList(getFilter(CommonApiNames.NAME, Operator.EQ, Lists.newArrayList(fieldVal1))));
            if (Objects.isNull(shippingLocation)) {
                return null;
            }
            return shippingLocation.get(ACCOUNT_ID_API_NAME, String.class);
        } else if (AccountObjApiNames.OBJECT_API_NAME.equals(objApiName)) {
            IObjectData account = findOne(tenantId, AccountObjApiNames.OBJECT_API_NAME, Lists.newArrayList(CommonApiNames.ID),
                    Lists.newArrayList(getFilter(ACCOUNT_NO_API_NAME, Operator.EQ, Lists.newArrayList(fieldVal1))));
            if (Objects.isNull(account)) {
                return null;
            }
            return account.getId();
        } else if ("seller_delivery_relation__c".equals(objApiName)) {
            IObjectData sellerDeliveryRelation = findOne(tenantId, "seller_delivery_relation__c", Lists.newArrayList("seller_reach__c"),
                    Lists.newArrayList(getFilter("delivery_party_code__c", Operator.EQ, Lists.newArrayList(fieldVal1)),
                            getFilter("seller_reach_code__c", Operator.EQ, Lists.newArrayList(strings[3]))));
            if (Objects.isNull(sellerDeliveryRelation)) {
                return null;
            }
            return sellerDeliveryRelation.get("seller_reach__c", String.class);
        }
        return null;
    }

    public String get(Integer tenantId, String objApiName, String fieldVal1, String fieldVal2) {
        try {
            String key = buildKey(tenantId, objApiName, fieldVal1, fieldVal2);
            return cache.get(key);
        } catch (CacheLoader.InvalidCacheLoadException e) {
            log.info("get account cache invalid :{}", e.getMessage());
            return null;
        } catch (Exception e) {
            log.error("get account cache error", e);
            throw new InspectionVerifyFailureInterruptException(100001, new InspectionVerifyErrorInfo("get account cache error", null));
        }
    }

    public void clear(Integer tenantId, String objApiName, String fieldVal1, String fieldVal2) {
        log.info("Get ready clear cache for {}, {}, {}, {}", tenantId, objApiName, fieldVal1, fieldVal2);
        String key = buildKey(tenantId, objApiName, fieldVal1, fieldVal2);
        if (containsKey(key)) {
            log.info("Clearing cache for {}, {}, {}, {}", tenantId, objApiName, fieldVal1, fieldVal2);
            cache.invalidate(key);
        }
    }

    public boolean containsKey(String key) {
        return cache.asMap().containsKey(key);
    }

    public String buildKey(Integer tenantId, String objApiName, String fieldVal1, String fieldVal2) {
        if (Objects.nonNull(fieldVal2)) {
            return String.format("%s#$%s#$%s#$%s", tenantId, objApiName, fieldVal1, fieldVal2);
        }
        return String.format("%s#$%s#$%s", tenantId, objApiName, fieldVal1);
    }

    public String[] splitKey(String key) {
        return key.split("#\\$");
    }

    private IObjectData findOne(String tenantId, String apiName, List<String> fields, List<IFilter> filters) {
        SearchTemplateQuery queryTemplate = new SearchTemplateQuery();
        queryTemplate.setLimit(1);
        queryTemplate.setFilters(filters);

        List<IObjectData> result = serviceFacade.findBySearchTemplateQueryWithFields(
                ActionContextExt.of(User.systemUser(tenantId)).getContext(),
                apiName,
                queryTemplate,
                fields).getData();
        if (CollectionUtils.isEmpty(result)) {
            return null;
        }
        return result.get(0);
    }

    private IFilter getFilter(String fieldName, Operator operator, List<String> fieldValues) {
        IFilter filter = new Filter();
        filter.setFieldName(fieldName);
        filter.setOperator(operator);
        filter.setFieldValues(fieldValues);
        return filter;
    }
}
