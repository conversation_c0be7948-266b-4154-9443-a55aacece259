package com.facishare.crm.fmcg.sales.utils;

import com.facishare.crm.fmcg.sales.apiname.FmcgSalesOrderGrayConfig;
import com.fxiaoke.common.release.GrayRelease;
import lombok.experimental.UtilityClass;

/**
 * @description:
 * @author: yangyx
 * @date: 2023-02-13 20:01
 **/
@UtilityClass
public class CrmFmcgSalesGrayUtil {

    private final String configFileName = "fmcg";

    public boolean isYuanQi(String tenantId) {
        return GrayRelease.isAllow(configFileName, FmcgSalesOrderGrayConfig.YUANQI, tenantId);
    }

    public boolean isYinLu(String tenantId) {
        return GrayRelease.isAllow(configFileName, FmcgSalesOrderGrayConfig.YINLU_ORDER, tenantId);
    }

    public boolean isHaoLiYou(String tenantId) {
        return GrayRelease.isAllow(configFileName, FmcgSalesOrderGrayConfig.HAOLIYOU, tenantId);
    }

    public boolean isHaoXiangNi(String tenantId) {
        return GrayRelease.isAllow(configFileName, FmcgSalesOrderGrayConfig.HAOXIANGNI, tenantId);
    }

    public boolean isChuanHua(String tenantId) {
        return GrayRelease.isAllow(configFileName, FmcgSalesOrderGrayConfig.CHUANHUA, tenantId);
    }

    public boolean isZhongKen(String tenantId) {
        return GrayRelease.isAllow(configFileName, FmcgSalesOrderGrayConfig.ZHONGKEN, tenantId);
    }

    public boolean isMengNiu(String tenantId) {
        return GrayRelease.isAllow(configFileName, FmcgSalesOrderGrayConfig.MENGNIU, tenantId);
    }

    public boolean isMengNiuV1(String tenantId) {
        return "mengniu-public-prod".equals(System.getProperty("process.profile", System.getProperty("spring.profiles.active", "")))
                || GrayRelease.isAllow(configFileName, FmcgSalesOrderGrayConfig.MENGNIU, tenantId);
    }

    public boolean isXianChun(String tenantId) {
        return GrayRelease.isAllow(configFileName, FmcgSalesOrderGrayConfig.XIANCHUN, tenantId);
    }

    public boolean isRIO(String tenantId) {
        return GrayRelease.isAllow(configFileName, FmcgSalesOrderGrayConfig.IS_RIO_TENANT, tenantId);
    }

    public boolean isFuErJia(String tenantId) {
        return GrayRelease.isAllow(configFileName, FmcgSalesOrderGrayConfig.FUERJIA, tenantId);
    }

    public boolean isXingHeng(String tenantId) {
        return GrayRelease.isAllow(configFileName, FmcgSalesOrderGrayConfig.XINGHENG, tenantId);
    }

    public boolean showProductLayout(String tenantId) {
        return GrayRelease.isAllow(configFileName, FmcgSalesOrderGrayConfig.SALES_ORDER_LAYOUT, tenantId);
    }

    public boolean supportProductCustomInfoComponent(String tenantId) {
        return GrayRelease.isAllow(configFileName, FmcgSalesOrderGrayConfig.SUPPORT_PRODUCT_CUSTOM_INFO_COMPONENT, tenantId);
    }
}
