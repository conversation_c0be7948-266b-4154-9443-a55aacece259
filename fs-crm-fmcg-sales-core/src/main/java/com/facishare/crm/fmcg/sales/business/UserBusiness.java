package com.facishare.crm.fmcg.sales.business;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserBusiness {
    @Resource
    private DepartmentBusiness departmentBusiness;

    @Resource
    private RoleBusiness roleBusiness;


    public List<Integer> mergeUserIds(Integer tenantId, List<Integer> userIds, List<Integer> departmentIds, List<String> roleGroupIds, List<String> userGroups) {
        Set<Integer> allUserIdList = new HashSet<>();
        if (CollectionUtils.isNotEmpty(userIds)) {
            allUserIdList.addAll(userIds);
        }

        if (CollectionUtils.isNotEmpty(departmentIds)) {
            List<Integer> usersOfDep = departmentBusiness.getEmployeeIDsByDepartmentIds(tenantId, departmentIds);
            if (CollectionUtils.isNotEmpty(usersOfDep)) {
                allUserIdList.addAll(usersOfDep);
            }
        }

        if (CollectionUtils.isNotEmpty(roleGroupIds)) {
            List<Integer> usersOfRole = roleBusiness.queryEmployeeIdsByRoleIds(tenantId, roleGroupIds, false);
            if (CollectionUtils.isNotEmpty(usersOfRole)) {
                allUserIdList.addAll(usersOfRole);
            }
        }
        return new ArrayList<>(allUserIdList);
    }

}
