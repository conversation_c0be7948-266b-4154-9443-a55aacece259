package com.facishare.crm.fmcg.sales.dao.mongo.entity;

import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

/**
 * <AUTHOR>
 */
@Entity(value = "fmcg_sales_statements_detail", noClassnameStored = true)
@Data
@ToString
public class SalesStatementsDetailEntity {

    public static final String FIELD_CREATE_TIME = "CT";
    public static final String FIELD_DATA = "D";
    public static final String FIELD_TENANT_ID = "EI";

    @Id
    private String id;

    @Property(FIELD_TENANT_ID)
    private String tenantId;

    /**
     * 数据
     */
    @Property(FIELD_DATA)
    private String data;

    /**
     * 创建时间
     */
    @Property(FIELD_CREATE_TIME)
    private long createTime;
}