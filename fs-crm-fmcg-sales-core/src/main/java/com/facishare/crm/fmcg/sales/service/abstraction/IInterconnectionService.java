package com.facishare.crm.fmcg.sales.service.abstraction;

import com.facishare.crm.fmcg.sales.model.interconnection.BatchOpenDownstream;
import com.facishare.crm.fmcg.sales.model.interconnection.InitPersonnel;
import com.facishare.crm.fmcg.sales.model.interconnection.UpdatePersonnelPhone;
import com.facishare.paas.appframework.core.model.ServiceContext;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @create: 2023-10-26 16:05
 **/
public interface IInterconnectionService {

    BatchOpenDownstream.Result batchOpenDownstream(BatchOpenDownstream.Arg arg, ServiceContext serviceContext);

    InitPersonnel.Result initPersonnel(InitPersonnel.Arg arg, ServiceContext serviceContext);

    UpdatePersonnelPhone.Result updatePersonnelPhone(UpdatePersonnelPhone.Arg arg, ServiceContext serviceContext);
}
