package com.facishare.crm.fmcg.sales.service.abstraction;

import com.facishare.crm.fmcg.sales.model.statement.SalesStatementsConformance;
import com.facishare.crm.fmcg.sales.model.statement.SalesStatementsDetail;
import com.facishare.paas.appframework.core.model.ServiceContext;

/**
 * <AUTHOR>
 */
public interface ISalesStatementsService {

    /**
     * 发货单详情
     */
    SalesStatementsDetail.Result detail(SalesStatementsDetail.Arg arg, ServiceContext context);


    void initSalesStatementsModule(String tenantId);


    int salesStatementsModuleOpenStatus(ServiceContext context);

    /**
     * 确认对账
     */
    void conformanceStatements(SalesStatementsConformance.Arg arg, ServiceContext context);
}
