package com.facishare.crm.fmcg.sales.service;

import com.facishare.crm.fmcg.sales.constants.RoleCode;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.ButtonService;
import com.facishare.paas.appframework.core.predef.service.dto.button.CreateButton;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.List;

@Service
@Slf4j
public class ButtonManagerService {

    @Resource
    private ButtonService buttonService;

    public void createButtonByRole(ServiceContext serviceContext, String buttonStr, List<String> roles) {
        CreateButton.Arg createButtonArg = new CreateButton.Arg();
        createButtonArg.setButton(buttonStr);
        createButtonArg.setRoles(roles);
        createButtonArg.setPost_actions(Lists.newArrayList());
        createButtonArg.setIgnoreDefaultRole(false);
        try {
            buttonService.create(createButtonArg, serviceContext);
        } catch (Exception e) {
            log.error("createButtonByRole error", e);
        }
    }

    public void addCustomerButton(String tenantId, String ea, String objectApiName, String buttonApiName) {
        RequestContext requestContext = RequestContext.builder().tenantId(tenantId).ea(ea).user(User.systemUser(tenantId)).build();
        ServiceContext serviceContext = new ServiceContext(requestContext, "", "");
        addCustomerButton(serviceContext, objectApiName, buttonApiName);
    }

    public void addCustomerButton(ServiceContext serviceContext, String objectApiName, String buttonApiName) {
        String customerButtonStr = loadButtonDescribeJsonFromResource(objectApiName, buttonApiName);
        createButtonByRole(serviceContext, customerButtonStr, Lists.newArrayList(RoleCode.SYSTEM_ADMINISTRATOR));
    }

    private String loadButtonDescribeJsonFromResource(String objectApiName, String buttonApiName) {
        try {
            File file = ResourceUtils.getFile(String.format("classpath:sales/button/%s.%s.json", objectApiName, buttonApiName));
            return new String(Files.readAllBytes(file.toPath()));
        } catch (IOException e) {
            throw new MetaDataException(String.format("read button[%s-%s] describe from file cause io exception.", objectApiName, buttonApiName));
        }
    }
}
