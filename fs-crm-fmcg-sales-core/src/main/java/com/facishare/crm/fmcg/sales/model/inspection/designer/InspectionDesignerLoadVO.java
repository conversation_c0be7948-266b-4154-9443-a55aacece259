package com.facishare.crm.fmcg.sales.model.inspection.designer;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.api.model.BaseResult;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Map;

public interface InspectionDesignerLoadVO {
    @Data
    @ToString
    class Arg {

    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @ToString
    class Result extends BaseResult {

        private String inspectionDesignerId;
        private Map<String, JSONObject> data;

        public static Result success(String inspectionDesignerId,Map<String, JSONObject> data) {
            Result result = new Result();
            result.setErrorMsg("success");
            result.setErrorCode(0);
            result.setInspectionDesignerId(inspectionDesignerId);
            result.setData(data);
            return result;
        }

        public static Result error(String msg) {
            Result result = new Result();
            result.setErrorMsg(msg);
            result.setErrorCode(400);
            return result;
        }

    }
}
