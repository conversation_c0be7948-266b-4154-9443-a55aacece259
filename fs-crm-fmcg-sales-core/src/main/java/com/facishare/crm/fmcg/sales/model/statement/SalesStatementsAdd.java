package com.facishare.crm.fmcg.sales.model.statement;

import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SalesStatementsAdd {

    @Data
    @ToString
    class Arg {
        private List<String> salesman;
        private List<Long> dateInterval;
    }

    @Data
    @ToString
    class Result {
        private int code;
        private String message;

        public static Result success() {
            Result result = new Result();
            result.setCode(0);
            result.setMessage("success");
            return result;
        }

        public static Result fail(String msg) {
            Result result = new Result();
            result.setCode(400);
            result.setMessage(msg);
            return result;
        }


    }

}
