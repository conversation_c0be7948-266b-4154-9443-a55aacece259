package com.facishare.crm.fmcg.sales.model.sn;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.api.model.BaseResult;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @create: 2023-10-25 22:18
 **/
public interface UpdateMongoAction {
    @Data
    @ToString
    class Arg {
        private List<JSONObject> data;
        private String tenantId;
    }

    @Data
    @ToString
    class Result extends BaseResult {
    }
}
