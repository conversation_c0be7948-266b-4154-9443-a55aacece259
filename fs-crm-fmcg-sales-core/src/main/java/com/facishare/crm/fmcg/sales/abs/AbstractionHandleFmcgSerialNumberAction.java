package com.facishare.crm.fmcg.sales.abs;

import com.facishare.crm.fmcg.sales.abs.abstraction.HandleFmcgSerialNumberAction;
import com.facishare.crm.fmcg.sales.apiname.InspectionRecordObjApiNames;
import com.facishare.crm.fmcg.sales.model.inspection.InspectionVerify;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @create : 2024-07-25 11:32
 **/
@Slf4j
public abstract class AbstractionHandleFmcgSerialNumberAction implements HandleFmcgSerialNumberAction<InspectionVerify.Context> {

    @Override
    public void action(InspectionVerify.Context context) {
        this.fillData(context);
    }

    /**
     * 目前需要在 context.getObjectData() 中填充的字段 : belong_account_id
     * 需要填充未知原因，或抛出异常
     */
    protected abstract void fillData(InspectionVerify.Context context);

    protected void fillCausesUnknown(InspectionVerify.Context context, String causesUnknown) {
        context.getObjectData().set(InspectionRecordObjApiNames.INSPECTION_RESULT, "2");
        context.getObjectData().set(InspectionRecordObjApiNames.CAUSES_UNKNOWN, causesUnknown);
    }
}
