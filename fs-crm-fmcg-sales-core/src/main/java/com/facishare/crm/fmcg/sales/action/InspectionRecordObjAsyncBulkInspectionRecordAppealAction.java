package com.facishare.crm.fmcg.sales.action;

import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAsyncBulkAction;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> liu<PERSON><PERSON>
 * @create : 2025-03-24 15:47
 **/
public class InspectionRecordObjAsyncBulkInspectionRecordAppealAction  extends AbstractStandardAsyncBulkAction<InspectionRecordObjAsyncBulkInspectionRecordAppealAction.Arg, InspectionRecordObjInspectionRecordAppealAction.Arg> {
    @Override
    protected String getDataIdByParam(InspectionRecordObjInspectionRecordAppealAction.Arg arg) {
        return arg.getObjectDataId();
    }

    @Override
    protected List<InspectionRecordObjInspectionRecordAppealAction.Arg> getButtonParams() {
        InspectionRecordObjAsyncBulkInspectionRecordAppealAction.Arg asyncBulkArg = this.arg;
        return asyncBulkArg.getDataIds().stream()
                .map(id -> {
                    InspectionRecordObjInspectionRecordAppealAction.Arg arg = new InspectionRecordObjInspectionRecordAppealAction.Arg();
                    arg.setObjectDataId(id);
                    arg.setArgs(asyncBulkArg.getArgs());
                    return arg;
                })
                .collect(Collectors.toList());
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.INSPECTION_RECORD_APPEAL.getButtonApiName();
    }

    @Override
    protected String getActionCode() {
        return ObjectAction.INSPECTION_RECORD_APPEAL.getActionCode();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.INSPECTION_RECORD_APPEAL.getActionCode());
    }

    @Data
    public static class Arg {
        private List<String> dataIds;
        private JSONObject args;
    }
}
