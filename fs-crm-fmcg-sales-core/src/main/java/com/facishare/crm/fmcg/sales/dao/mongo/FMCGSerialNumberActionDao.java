package com.facishare.crm.fmcg.sales.dao.mongo;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.sales.dao.mongo.entity.FMCGSerialNumberActionEntity;
import com.google.common.base.Strings;
import org.bson.types.ObjectId;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.mongodb.morphia.query.UpdateResults;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> l<PERSON><PERSON>yu
 * @create : 2023-09-19 10:17
 **/
@Service
public class FMCGSerialNumberActionDao extends BaseDao {
    public List<FMCGSerialNumberActionEntity> find(String key, String val) {
        return find(FMCGSerialNumberActionEntity.class, key, val).asList();
    }

    public List<FMCGSerialNumberActionEntity> findByUniqueId(String tenantId, String uniqueId) {
        Query<FMCGSerialNumberActionEntity> query = mongoContext.createQuery(FMCGSerialNumberActionEntity.class);
        if (!Strings.isNullOrEmpty(uniqueId)) {
            query.field(FMCGSerialNumberActionEntity.F_UNIQUE_ID).equal(uniqueId);
        }
        query.field(FMCGSerialNumberActionEntity.F_TENANT_ID).equal(tenantId);
        return query.asList();
    }

    public UpdateResults updateAction(JSONObject entity) {
        Query<FMCGSerialNumberActionEntity> query = mongoContext.createQuery(FMCGSerialNumberActionEntity.class);
        String id = entity.getString("id");
        query.field("_id").equal(new ObjectId(id));
        UpdateOperations<FMCGSerialNumberActionEntity> update = mongoContext.createUpdateOperations(FMCGSerialNumberActionEntity.class);
        entity.remove("id");
        for (Map.Entry<String, Object> o : entity.entrySet()) {
            String key = o.getKey();
            Object value = o.getValue();
            update.set(key, value);
        }
        return mongoContext.update(query, update);
    }
}
