package com.facishare.crm.fmcg.sales.factory;

import com.facishare.crm.fmcg.sales.abs.abstraction.InspectionRuleVerifyAction;
import com.facishare.crm.fmcg.sales.exception.ApiException;
import com.facishare.crm.fmcg.sales.exception.BeanFactoryException;
import com.facishare.crm.fmcg.sales.model.inspection.InspectionVerify;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> liuhaoyu
 * @create : 2024-07-13 16:47
 **/
@Slf4j
public class InspectionRuleVerifyActionFactory {
    public static InspectionRuleVerifyAction<InspectionVerify.Result, InspectionVerify.Context> getBeanByRuleId(String ruleId) {
        if (!SpringContextHolder.containsBean(ruleId)) {
            log.error("No bean for current rule found : {}", ruleId);
            throw new BeanFactoryException(100001, "No bean for current rule found, ruleId is " + ruleId);
        }
        return SpringContextHolder.getBean(ruleId);
    }
}
