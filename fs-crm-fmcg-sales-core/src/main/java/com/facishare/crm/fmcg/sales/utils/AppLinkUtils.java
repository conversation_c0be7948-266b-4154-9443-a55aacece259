package com.facishare.crm.fmcg.sales.utils;

import com.facishare.crm.fmcg.sales.exception.AdapterException;
import com.facishare.crm.fmcg.sales.model.SaveAppLink;
import com.github.autoconf.ConfigFactory;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class AppLinkUtils {

    public static SaveAppLink.Result saveAppLink(String ea, String bizId, String appHomeLinkType) {
        Map<String, Object> header = new HashMap<>();
        header.put("Content-Type", "application/json");
        Map<String, String> body = new HashMap<>();
        body.put("bizId", bizId);
        body.put("appHomeLinkType", appHomeLinkType);
        body.put("ea", ea);
        String url = ConfigFactory.getConfig("variables_endpoint").get("svc_wq_task") + "/custom/saveAppLink";
        try {
            return HttpUtil.post(
                    url,
                    header,
                    body,
                    SaveAppLink.Result.class);
        } catch (IOException e) {
            throw new AdapterException("metadata", "http remote call error.", 100500);
        }
    }
}
