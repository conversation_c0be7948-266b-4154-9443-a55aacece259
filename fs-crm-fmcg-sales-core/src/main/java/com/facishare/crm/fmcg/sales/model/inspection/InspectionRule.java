package com.facishare.crm.fmcg.sales.model.inspection;

import com.facishare.crm.fmcg.sales.enums.MagicEnum;
import com.facishare.crm.fmcg.sales.utils.I18nUtil;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> l<PERSON><PERSON><PERSON>
 * @create : 2024-07-25 14:43
 **/
@Data
public class InspectionRule {
    public static final List<InspectionRule> INSPECTION_RULE_LIST;

    static {
        INSPECTION_RULE_LIST = Lists.newArrayList();
        INSPECTION_RULE_LIST.add(new InspectionRule("locationInspectionRuleVerifyAction", I18nUtil.get(MagicEnum.locationCodeInspection)));
        INSPECTION_RULE_LIST.add(new InspectionRule("loginAccountInspectionRuleVerifyAction", I18nUtil.get(MagicEnum.loginAccountCodeInspection)));
        INSPECTION_RULE_LIST.add(new InspectionRule("inspectionAccountInspectionRuleVerifyAction", I18nUtil.get(MagicEnum.inspectionAccountCodeInspection)));
    }

    private String ruleId;
    private String ruleName;

    public InspectionRule(String ruleId, String ruleName) {
        this.ruleId = ruleId;
        this.ruleName = ruleName;
    }
}
