package com.facishare.crm.fmcg.sales.utils;

import com.facishare.crm.fmcg.sales.enums.MagicEnum;
import org.apache.commons.lang3.StringUtils;


/**
 * @description: 多语言国际码转化工具
 * @author: lijingsong
 * @date: 2022/4/6
 **/
public class I18nUtil {
    public static String get(MagicEnum magicEnum) {
        return I18NConvertBySpecificKey(magicEnum.getI18nKey(), magicEnum.getDefaultValue());
    }


    public static String I18NConvertBySpecificKey(String key, String defaultValue, Object... params) {
        String i18NStr = I18N.getBySpecificKey(key, params);
        return StringUtils.isEmpty(i18NStr) ? defaultValue : i18NStr;
    }
}
