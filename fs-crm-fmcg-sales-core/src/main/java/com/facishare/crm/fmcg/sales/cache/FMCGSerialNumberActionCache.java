package com.facishare.crm.fmcg.sales.cache;

import com.facishare.crm.fmcg.sales.dao.mongo.FMCGSerialNumberActionDao;
import com.facishare.crm.fmcg.sales.dao.mongo.entity.FMCGSerialNumberActionEntity;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> liu<PERSON><PERSON>
 * @create : 2023-09-22 16:05
 **/
@Component
@Slf4j
public class FMCGSerialNumberActionCache {
    private LoadingCache<String, List<FMCGSerialNumberActionEntity>> actionCache;

    @Resource
    private FMCGSerialNumberActionDao fmcgSerialNumberActionDao;

    @PostConstruct
    public void init() {
        actionCache = CacheBuilder.newBuilder()
                .maximumSize(2000)
                .expireAfterWrite(2, TimeUnit.HOURS)
                .build(new CacheLoader<String, List<FMCGSerialNumberActionEntity>>() {
                    @Override
                    public List<FMCGSerialNumberActionEntity> load(String key) {
                        return handleActions(key);
                    }
                });
    }

    private List<FMCGSerialNumberActionEntity> handleActions(String key) {
        return fmcgSerialNumberActionDao.find(FMCGSerialNumberActionEntity.F_TENANT_ID, key);
    }

    public List<FMCGSerialNumberActionEntity> getNoException(String tenantId) {
        try {
            return actionCache.get(tenantId);
        } catch (Exception e) {
            log.error("get actionCache is error", e);
        }
        return null;
    }

    public void clear(String key) {
        if (containsKey(key)) {
            actionCache.invalidate(key);
        }
    }

    public boolean containsKey(String key) {
        return actionCache.asMap().containsKey(key);
    }
}
