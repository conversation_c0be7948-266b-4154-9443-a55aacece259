package com.facishare.crm.fmcg.sales.action;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.sales.apiname.InspectionDataAppealRecordObjApiNames;
import com.facishare.crm.fmcg.sales.apiname.InspectionRecordObjApiNames;
import com.facishare.crm.fmcg.sales.apiname.PersonnelObjApiNames;
import com.facishare.crm.fmcg.sales.business.InspectionBusiness;
import com.facishare.crm.fmcg.sales.enums.ButtonAction;
import com.facishare.crm.fmcg.sales.enums.MagicEnum;
import com.facishare.crm.fmcg.sales.pool.InspectionBusinessPool;
import com.facishare.crm.fmcg.sales.task.gnomon.GnomonNomonService;
import com.facishare.crm.fmcg.sales.utils.I18nUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> liuhaoyu
 * @create : 2024-07-18 18:13
 **/
public class InspectionRecordObjInspectionRecordAppealAction extends AbstractStandardAction<InspectionRecordObjInspectionRecordAppealAction.Arg, InspectionRecordObjInspectionRecordAppealAction.Result> {
    private IObjectData objectData;

    private final InspectionBusiness inspectionBusiness = SpringUtil.getContext().getBean(InspectionBusiness.class);
    private final GnomonNomonService gnomonNomonService = SpringUtil.getContext().getBean(GnomonNomonService.class);

    public static final String FROM_APPEAL_REASON = "form_appeal_reason";

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        Boolean illegallyGoodsAccountOwner = inspectionBusiness.isIllegallyGoodsAccountOwner(actionContext.getUser(),
                objectData.get(InspectionRecordObjApiNames.ILLEGALLY_GOODS_ACCOUNT_ID, String.class));
        if (!illegallyGoodsAccountOwner) {
            throw new ValidateException(I18nUtil.get(MagicEnum.noOperationPermission));
        }
        if (!"0".equals(objectData.get(InspectionRecordObjApiNames.PROCESSING_STATE, String.class))) {
            throw new ValidateException(I18nUtil.get(MagicEnum.currentDataHasBeenProcessed));
        }
        if (!"1".equals(objectData.get(InspectionRecordObjApiNames.INSPECTION_RESULT, String.class))) {
            throw new ValidateException(I18nUtil.get(MagicEnum.currentDataIsNotAbnormal));
        }
        if (Objects.isNull(arg.args) || Objects.isNull(arg.args.get(FROM_APPEAL_REASON))) {
            throw new ValidateException(I18nUtil.get(MagicEnum.appealReasonRequired));
        }
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.INSPECTION_RECORD_APPEAL.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getObjectDataId());
    }

    @Override
    protected void init() {
        super.init();
        if (CollectionUtils.notEmpty(dataList)) {
            this.objectData = dataList.get(0);
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        Map<String, Object> updateMap = Maps.newHashMap();
        updateMap.put(InspectionRecordObjApiNames.APPEAL_REASON, arg.getArgs().get(FROM_APPEAL_REASON));
        updateMap.put(InspectionRecordObjApiNames.WHETHER_APPEAL, true);
        updateMap.put(InspectionRecordObjApiNames.PROCESSING_STATE, "3");
        updateMap.put(InspectionRecordObjApiNames.APPEAL_TIME, System.currentTimeMillis());
        updateMap.put(InspectionRecordObjApiNames.APPEAL_PERSONNEL_ID, actionContext.getUser().getUserId());
        updateMap.put(InspectionRecordObjApiNames.APPEAL_PERSONNEL_NAME, actionContext.getUser().getUserName());

        IObjectData data = serviceFacade.updateWithMap(actionContext.getUser(), this.objectData, updateMap);
        createInspectionDataAppealRecord(this.objectData);
        //删除窜货自动确认任务
        InspectionBusinessPool.execute(() -> {
            try {
                gnomonNomonService.sendDeleteIllegallyGoodsAutomaticConfirmationTask(actionContext.getTenantId(), objectData.getId());
            } catch (Exception e) {
                log.error("appealAction sendDeleteIllegallyGoodsAutomaticConfirmationTask Error", e);
            }
        });
        return Result.of(data);
    }

    @Override
    protected Map<String, Object> getArgs() {
        Map<String, Object> args = super.getArgs();
        args.put(InspectionRecordObjApiNames.APPEAL_REASON, arg.getArgs().get(FROM_APPEAL_REASON));
        return args;
    }

    private void createInspectionDataAppealRecord(IObjectData inspectionRecordData) {
        IObjectData inspectionDataAppealRecord = new ObjectData();
        inspectionDataAppealRecord.setTenantId(inspectionRecordData.getTenantId());
        inspectionDataAppealRecord.setRecordType("default__c");
        inspectionDataAppealRecord.setDescribeApiName(InspectionDataAppealRecordObjApiNames.OBJECT_API_NAME);
        inspectionDataAppealRecord.setIsPublic(true);

        inspectionDataAppealRecord.set(InspectionDataAppealRecordObjApiNames.INSPECTION_RECORD_ID, inspectionRecordData.getId());
        inspectionDataAppealRecord.set(InspectionDataAppealRecordObjApiNames.PRODUCT_ID, inspectionRecordData.get(InspectionRecordObjApiNames.PRODUCT_ID));
        inspectionDataAppealRecord.set(InspectionDataAppealRecordObjApiNames.PRODUCT_NAME, inspectionRecordData.get(InspectionRecordObjApiNames.PRODUCT_NAME));
        inspectionDataAppealRecord.set(InspectionDataAppealRecordObjApiNames.FMCG_SERIAL_NUMBER_ID, inspectionRecordData.get(InspectionRecordObjApiNames.FMCG_SERIAL_NUMBER_ID));
        inspectionDataAppealRecord.set(InspectionDataAppealRecordObjApiNames.ILLEGALLY_GOODS_ACCOUNT_ID, inspectionRecordData.get(InspectionRecordObjApiNames.ILLEGALLY_GOODS_ACCOUNT_ID));
        inspectionDataAppealRecord.set(InspectionDataAppealRecordObjApiNames.ILLEGALLY_GOODS_ACCOUNT_NAME, inspectionRecordData.get(InspectionRecordObjApiNames.ILLEGALLY_GOODS_ACCOUNT_NAME));
        inspectionDataAppealRecord.set(InspectionDataAppealRecordObjApiNames.COVERING_SALES_AREAS_ID, inspectionRecordData.get(InspectionRecordObjApiNames.COVERING_SALES_AREAS_ID));
        inspectionDataAppealRecord.set(InspectionDataAppealRecordObjApiNames.APPEAL_PERSONNEL_ID, inspectionRecordData.get(InspectionRecordObjApiNames.APPEAL_PERSONNEL_ID));
        inspectionDataAppealRecord.set(InspectionDataAppealRecordObjApiNames.APPEAL_PERSONNEL_NAME, inspectionRecordData.get(InspectionRecordObjApiNames.APPEAL_PERSONNEL_NAME));
        inspectionDataAppealRecord.set(InspectionDataAppealRecordObjApiNames.ILLEGALLY_GOODS_TENANT_ID, inspectionRecordData.get(InspectionRecordObjApiNames.ILLEGALLY_GOODS_TENANT_ID));
        inspectionDataAppealRecord.set(InspectionDataAppealRecordObjApiNames.APPEAL_TIME, inspectionRecordData.get(InspectionRecordObjApiNames.APPEAL_TIME));
        inspectionDataAppealRecord.set(InspectionDataAppealRecordObjApiNames.APPEAL_REASON, inspectionRecordData.get(InspectionRecordObjApiNames.APPEAL_REASON));

        IObjectData personnelData = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), actionContext.getUser().getUserId(), PersonnelObjApiNames.OBJECT_API_NAME);
        inspectionDataAppealRecord.setOwner(Lists.newArrayList(actionContext.getUser().getUserId()));
        inspectionDataAppealRecord.setDataOwnDepartment(personnelData.getDataOwnDepartment());

        serviceFacade.saveObjectData(actionContext.getUser(), inspectionDataAppealRecord);
    }

    @Data
    public static class Arg {
        private String objectDataId;
        private JSONObject args;
    }

    @Data
    public static class Result {
        private ObjectDataDocument objectData;

        public static Result of(IObjectData objectData) {
            Result result = new Result();
            result.setObjectData(ObjectDataDocument.of(objectData));
            return result;
        }
    }
}
