package com.facishare.crm.fmcg.sales.controller;

import com.facishare.crm.fmcg.sales.model.statement.SalesStatementsOpenStatus;
import com.facishare.crm.fmcg.sales.service.SalesStatementsService;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class SalesStatementsObjOpenStatusController extends PreDefineController<SalesStatementsObjOpenStatusController.Arg, SalesStatementsOpenStatus.Result> {

    private static final SalesStatementsService salesStatementsService = SpringUtil.getContext().getBean(SalesStatementsService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }

    @Override
    protected SalesStatementsOpenStatus.Result doService(Arg arg) {
        ServiceContext serviceContext = new ServiceContext(controllerContext.getRequestContext(), "sales_statements_service", "detail");
        int openStatus = salesStatementsService.salesStatementsModuleOpenStatus(serviceContext);
        SalesStatementsOpenStatus.Result result = new SalesStatementsOpenStatus.Result();
        result.setValue(openStatus);
        return result;
    }

    @Data
    @ToString
    static class Arg implements Serializable {
    }
}
