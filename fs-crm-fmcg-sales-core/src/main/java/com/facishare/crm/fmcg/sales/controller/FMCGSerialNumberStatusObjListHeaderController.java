package com.facishare.crm.fmcg.sales.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.google.common.collect.Lists;

/**
 * <AUTHOR> l<PERSON><PERSON><PERSON>
 * @create : 2023-08-11 16:42
 **/
public class FMCGSerialNumberStatusObjListHeaderController extends StandardListHeaderController {
    @Override
    protected Result after(Arg arg, Result result) {
        buttonFilter(arg, result);
        return super.after(arg, result);
    }

    public void buttonFilter(Arg arg, Result result) {
        result.setButtons(Lists.newArrayList());
        result.getLayout().put("buttons", Lists.newArrayList());
    }
}
