package com.facishare.crm.fmcg.sales.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.sales.cache.AccountCache;
import com.facishare.crm.fmcg.sales.cache.DataLevelI18NTranslateCache;
import com.facishare.crm.fmcg.sales.cache.DataLevelI18nFieldSupportLanguagesCache;
import com.facishare.crm.fmcg.sales.cache.ObjectStatusCache;
import com.facishare.crm.fmcg.sales.model.tool.BulkDeleteDirect;
import com.facishare.crm.fmcg.sales.model.tool.PurgeDescribeCache;
import com.facishare.crm.fmcg.sales.model.tool.ResetEmployeePassword;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.service.impl.ObjectDescribeServiceImpl;
import com.facishare.uc.api.model.employee.arg.ResetEmployeePasswordArg;
import com.facishare.uc.api.model.employee.result.ResetEmployeePasswordResult;
import com.facishare.uc.api.service.EmployeeEditionService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> liuhaoyu
 * @create : 2024-01-04 17:13
 **/
@Service
@Slf4j
@ServiceModule("tool_service")
public class ToolService {
    @Autowired
    private ObjectDescribeServiceImpl objectDescribeService;
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private EmployeeEditionService employeeEditionService;
    @Resource
    private DataLevelI18NTranslateCache dataLevelI18NTranslateCache;
    @Resource
    private DataLevelI18nFieldSupportLanguagesCache dataLevelI18nFieldSupportLanguagesCache;
    @Resource
    private ObjectStatusCache objectStatusCache;
    @Resource
    private AccountCache accountCache;

    @ServiceMethod("clear_cache")
    public JSONObject clearCache(JSONObject arg) {
        try {
            String cacheName = arg.getString("cacheName");
            switch (cacheName) {
                case "dataLevelI18NTranslateCache":
                    dataLevelI18NTranslateCache.clear(arg.getString("tenantId"), arg.getString("apiName"), arg.getString("fieldApiName"), arg.getString("backlog"));
                    break;
                case "dataLevelI18nFieldSupportLanguagesCache":
                    dataLevelI18nFieldSupportLanguagesCache.clear(dataLevelI18nFieldSupportLanguagesCache.buildKey(arg.getString("tenantId"), arg.getString("describeApiName")));
                    break;
                case "objectStatusCache":
                    objectStatusCache.clear(objectStatusCache.buildKey(arg.getString("tenantId"), arg.getString("describeApiName")));
                    break;
                case "accountCache":
                    accountCache.clear(arg.getInteger("tenantId"), arg.getString("objApiName"), arg.getString("fieldVal1"), arg.getString("fieldVal2"));
                    break;
            }
        } catch (Exception e) {
            log.error("clearCache is fail", e);
            return new JSONObject() {{
                put("msg", "fail");
            }};
        }
        return new JSONObject() {{
            put("msg", "success");
        }};
    }

    @ServiceMethod("purge_describe_cache")
    public PurgeDescribeCache.Result purgeDescribeCache(PurgeDescribeCache.Arg arg, ServiceContext serviceContext) {
        PurgeDescribeCache.Result result = new PurgeDescribeCache.Result();
        List<String> tenantIds = arg.getTenantIds();
        List<String> apiNames = arg.getApiNames();
        if (CollectionUtils.isEmpty(tenantIds) || CollectionUtils.isEmpty(apiNames)) {
            result.setErrorCode(10000);
            result.setErrorMsg("Parameter null");
            return result;
        }

        List<String> failed = Lists.newArrayList();

        for (String tenantId : tenantIds) {
            for (String apiName : apiNames) {
                try {
                    log.info("purgeDescribeCache tenantId {}, apiName {}", tenantId, apiName);
                    objectDescribeService.purgeDescribeCache(tenantId, apiName, false, true);
                } catch (Exception e) {
                    failed.add(String.format("%s_%s_%s", tenantId, apiName, e.getMessage()));
                    log.error("purgeDescribeCache is error", e);
                }
            }
        }
        result.setErrorCode(0);
        result.setErrorMsg("success");
        result.setFailed(failed);
        return result;
    }

    @ServiceMethod("bulk_delete_direct")
    public BulkDeleteDirect.Result bulkDeleteDirect(BulkDeleteDirect.Arg arg, ServiceContext serviceContext) {
        BulkDeleteDirect.Result result = new BulkDeleteDirect.Result();
        Map<String, BulkDeleteDirect.DeleteData> deleteDataMap = arg.getDeleteDataMap();
        for (Map.Entry<String, BulkDeleteDirect.DeleteData> entry : deleteDataMap.entrySet()) {
            String tenantId = entry.getKey();
            String apiName = entry.getValue().getApiName();
            for (List<String> ids : Lists.partition(entry.getValue().getIds(), 100)) {
                List<IObjectData> objectDataList = Lists.newArrayList();
                for (String id : ids) {
                    IObjectData objectData = new ObjectData();
                    objectData.setId(id);
                    objectData.setDescribeApiName(apiName);
                    objectData.setTenantId(tenantId);
                    objectDataList.add(objectData);
                }
                log.info("tenantId {} bulkDeleteDirect arg : {}", tenantId, JSON.toJSONString(objectDataList));
                List<IObjectData> bulkDeleteDirectResult = serviceFacade.bulkDeleteDirect(objectDataList, User.systemUser(tenantId));
                log.info("tenantId {} bulkDeleteDirect result : {}", tenantId, JSON.toJSONString(bulkDeleteDirectResult));
            }
        }
        result.setErrorCode(0);
        result.setErrorMsg("success");
        return result;
    }

    @ServiceMethod("reset_employee_password")
    public ResetEmployeePassword.Result resetEmployeePassword(ResetEmployeePassword.Arg arg, ServiceContext serviceContext) {
        List<ResetEmployeePassword.InitData> data = arg.getData();
        for (ResetEmployeePassword.InitData initData : data) {
            Integer tenantId = initData.getTenantId();
            String initPassword = initData.getInitPassword();
            List<String> personnelIds = initData.getPersonnelIds();
            for (String personnelId : personnelIds) {
                try {
                    ResetEmployeePasswordArg resetEmployeePasswordArg = new ResetEmployeePasswordArg();
                    resetEmployeePasswordArg.setEnterpriseId(tenantId);
                    resetEmployeePasswordArg.setEmployeeId(Integer.parseInt(personnelId));
                    resetEmployeePasswordArg.setNewPassword(initPassword);
                    resetEmployeePasswordArg.setPasswordEncodeType(0);
                    resetEmployeePasswordArg.setInitialPassword(false);
                    resetEmployeePasswordArg.setCheckPasswordHistory(false);
                    ResetEmployeePasswordResult resetEmployeePasswordResult = employeeEditionService.resetEmployeePassword(resetEmployeePasswordArg);
                    System.out.println(JSON.toJSONString(resetEmployeePasswordResult));
                } catch (Exception e) {
                    log.error("resetEmployeePassword is error", e);
                    log.info("resetEmployeePassword is error : {}, {}, {}", tenantId, personnelId, initPassword);
                }
            }
        }
        return ResetEmployeePassword.Result.success();
    }

    @ServiceMethod("clear_object_status_cache")
    public String clearObjectStatusCache(@RequestBody JSONObject arg) {
        String tenantId = arg.getString("tenantId");
        String describeApiName = arg.getString("describeApiName");
        String key = objectStatusCache.buildKey(tenantId, describeApiName);
        try {
            log.info("before clear {}", JSON.toJSONString(objectStatusCache.getNoException(tenantId, describeApiName)));
            objectStatusCache.clear(key);
            log.info("after clear {}", JSON.toJSONString(objectStatusCache.getNoException(tenantId, describeApiName)));
        } catch (Exception e) {
            log.error("clear is error : ", e);
            return "failed";
        }
        return "success";
    }
}
