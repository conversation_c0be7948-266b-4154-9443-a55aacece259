package com.facishare.crm.fmcg.sales.cache;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.sales.apiname.AccountObjApiNames;
import com.facishare.crm.fmcg.sales.apiname.CommonApiNames;
import com.facishare.crm.fmcg.sales.apiname.EnterpriseRelationObjApiNames;
import com.facishare.crm.fmcg.sales.exception.ApiException;
import com.facishare.crm.fmcg.sales.model.interconnection.Relation;
import com.facishare.crm.fmcg.sales.model.sn.FMCGSerialNumberQueryOpenStatus;
import com.facishare.uc.api.model.enterprise.arg.GetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetSimpleEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.contract.paas.data.PaasDataFindById;
import com.fmcg.framework.http.contract.paas.data.PaasDataFindOne;
import com.google.common.base.Strings;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> liuhaoyu
 * @create : 2024-04-16 20:02
 **/
@Slf4j
@Component
public class InterconnectionRelationCache {
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private PaasDataProxy paasDataProxy;
    @Resource
    private EnterpriseEditionService enterpriseEditionService;
    @Resource
    private FMCGSNOpenStatusCache fmcgsnOpenStatusCache;

    private LoadingCache<String, Relation.TenantInfo> relationCache;

    @PostConstruct
    public void init() {
        relationCache = CacheBuilder.newBuilder()
                .maximumSize(4000)
                .expireAfterWrite(4, TimeUnit.HOURS)
                .build(new CacheLoader<String, Relation.TenantInfo>() {
                    @Override
                    public Relation.TenantInfo load(String key) {
                        return handleRelation(key);
                    }
                });
    }

    public Relation.TenantInfo getNoException(String tenantId) {
        try {
            return relationCache.get(tenantId);
        } catch (Exception e) {
            log.error("get relationCache is error", e);
        }
        return null;
    }

    public Relation.TenantInfo get(String tenantId) throws ExecutionException {
        return relationCache.get(tenantId);
    }


    public Boolean containsKey(String key) {
        return relationCache.asMap().containsKey(key);
    }

    public void clear(String key) {
        if (containsKey(key)) {
            relationCache.invalidate(key);
        }
    }

    private Relation.TenantInfo handleRelation(String tenantId) {
        Integer ei = Integer.parseInt(tenantId);
        String ea = eieaConverter.enterpriseIdToAccount(ei);

        Integer numberOneTenantId = findNumberOneTenantId(tenantId);

        if (ei.equals(numberOneTenantId)) {
            Relation.TenantInfo tenantInfo = new Relation.TenantInfo();
            tenantInfo.setTenantId(ei);
            tenantInfo.setName(getEnterpriseData(ei).getEnterpriseName());
            tenantInfo.setType("1");
            tenantInfo.setUpstreamTenantId(null);
            tenantInfo.setAccountRecordType(null);
            return tenantInfo;
        }

        JSONObject enterpriseRelationObj = findEnterpriseRelationObj(numberOneTenantId,
                Lists.newArrayList(new PaasDataFindOne.FilterDTO(EnterpriseRelationObjApiNames.ENTERPRISE_ACCOUNT, "EQ", ea)),
                Lists.newArrayList(EnterpriseRelationObjApiNames.MAPPER_ACCOUNT_ID));
        String mapperAccountId = enterpriseRelationObj.getString(EnterpriseRelationObjApiNames.MAPPER_ACCOUNT_ID);
        if (Strings.isNullOrEmpty(mapperAccountId)) {
            throw new ApiException(10002, "EnterpriseRelationObj mapper_account_id field is null");
        }
        JSONObject accountObj = findAccountObj(numberOneTenantId, mapperAccountId);
        String recordType = accountObj.getString(CommonApiNames.RECORD_TYPE);
        Relation.TenantInfo tenantInfo = new Relation.TenantInfo();
        tenantInfo.setTenantId(ei);
        tenantInfo.setName(getEnterpriseData(ei).getEnterpriseName());
        tenantInfo.setAccountRecordType(recordType);
        if ("dealer__c".equals(recordType) || "mollercular_company__c".equals(recordType)) {
            tenantInfo.setType("N");
            tenantInfo.setUpstreamTenantId(numberOneTenantId);
        } else if ("secondary_dealer__c".equals(recordType)) {
            tenantInfo.setType("M");
            if (Objects.isNull(accountObj.get("sellers__c")) || CollectionUtils.isEmpty(accountObj.getJSONArray("sellers__c"))) {
                throw new ApiException(10003, "AccountObj sellers__c field is null");
            }
            List<String> accountIds = accountObj.getJSONArray("sellers__c").toJavaList(String.class);
            JSONObject upEnterpriseRelationObj = findEnterpriseRelationObj(numberOneTenantId,
                    Lists.newArrayList(new PaasDataFindOne.FilterDTO(EnterpriseRelationObjApiNames.MAPPER_ACCOUNT_ID, "EQ", accountIds.get(0))),
                    Lists.newArrayList(EnterpriseRelationObjApiNames.ENTERPRISE_ACCOUNT));
            String upstreamEa = upEnterpriseRelationObj.getString(EnterpriseRelationObjApiNames.ENTERPRISE_ACCOUNT);
            if (Strings.isNullOrEmpty(upstreamEa)) {
                throw new ApiException(10004, "EnterpriseRelationObj enterprise_account field is null");
            }
            tenantInfo.setUpstreamTenantId(eieaConverter.enterpriseAccountToId(upstreamEa));
        }
        return tenantInfo;
    }

    private Integer findNumberOneTenantId(String tenantId) {
        FMCGSerialNumberQueryOpenStatus.Result result = fmcgsnOpenStatusCache.getNoException(tenantId);
        if (!Objects.equals(1, result.getValue()) || !Objects.equals(1, result.getIsShare())) {
            throw new ApiException(10007, "FMCGSerialNumberObj is not enabled or shared");
        }
        if (Strings.isNullOrEmpty(result.getUpstreamTenantId())) {
            throw new ApiException(10008, "FMCGSerialNumberObj describe field upstreamTenantId is null");
        }
        return Integer.parseInt(result.getUpstreamTenantId());
    }

    private JSONObject findAccountObj(Integer oneTenantId, String accountId) {
        PaasDataFindById.Arg arg = new PaasDataFindById.Arg();
        arg.setDescribeApiName(AccountObjApiNames.OBJECT_API_NAME);
        arg.setDataId(accountId);
        arg.setSelectFields(Lists.newArrayList(CommonApiNames.RECORD_TYPE, "sellers__c"));
        log.info("findAccountObj arg : {}", arg);
        PaasDataFindById.Result result = paasDataProxy.findById(oneTenantId, -10000, arg);
        log.info("findAccountObj result : {}", result);
        if (result.getCode() != 0) {
            throw new ApiException(result.getCode(), result.getMessage());
        }
        if (Objects.isNull(result.getData()) || Objects.isNull(result.getData().getObjectData())) {
            throw new ApiException(10005, "current AccountObj is not found");
        }
        return result.getData().getObjectData();
    }

    private JSONObject findEnterpriseRelationObj(Integer oneTenantId, List<PaasDataFindOne.FilterDTO> filters, List<String> fields) {
        PaasDataFindOne.Arg arg = new PaasDataFindOne.Arg();
        arg.setDescribeApiName(EnterpriseRelationObjApiNames.OBJECT_API_NAME);
        arg.setSelectFields(fields);
        PaasDataFindOne.QueryDTO queryDTO = new PaasDataFindOne.QueryDTO();
        queryDTO.setFilters(filters);
        arg.setSearchQueryInfo(JSONObject.toJSONString(queryDTO));
        log.info("findEnterpriseRelationObj arg : {}", arg);
        PaasDataFindOne.Result result = paasDataProxy.findOne(oneTenantId, -10000, arg);
        log.info("findEnterpriseRelationObj result : {}", result);
        if (result.getCode() != 0) {
            throw new ApiException(result.getCode(), result.getMessage());
        }
        if (Objects.isNull(result.getData()) || Objects.isNull(result.getData().getObjectData())) {
            throw new ApiException(10006, "current EnterpriseRelationObj is not found");
        }
        return result.getData().getObjectData();
    }

    private SimpleEnterpriseData getEnterpriseData(Integer tenantId) {
        GetSimpleEnterpriseDataArg argument = new GetSimpleEnterpriseDataArg();
        argument.setEnterpriseId(tenantId);
        GetSimpleEnterpriseDataResult simpleEnterpriseData = enterpriseEditionService.getSimpleEnterpriseData(argument);
        return simpleEnterpriseData.getEnterpriseData();
    }
}