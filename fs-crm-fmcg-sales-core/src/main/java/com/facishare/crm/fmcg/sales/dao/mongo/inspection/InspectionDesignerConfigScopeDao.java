package com.facishare.crm.fmcg.sales.dao.mongo.inspection;

import com.facishare.appserver.utils.AccountUtils;
import com.facishare.crm.fmcg.sales.dao.mongo.BaseDao;
import com.facishare.crm.fmcg.sales.dao.mongo.entity.inspection.InspectionDesignerConfigEntity;
import com.facishare.crm.fmcg.sales.dao.mongo.entity.inspection.InspectionDesignerConfigScopeEntity;
import com.facishare.crm.fmcg.sales.enums.InspectionDesignerConfigEnum;
import com.github.mongo.support.DatastoreExt;
import org.apache.commons.collections.CollectionUtils;
import org.bson.types.ObjectId;
import org.mongodb.morphia.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class InspectionDesignerConfigScopeDao extends BaseDao {


    private DatastoreExt getDBContext(String tenantId) {
        return mongoContext;
    }

    public void add(InspectionDesignerConfigScopeEntity item) {
        getDBContext(item.getTenantId()).save(item);
    }

    public InspectionDesignerConfigScopeEntity getById(String tenantId, String userAccount) {
        return buildQueryByTenantId(tenantId)
                .field("_id").equal(userAccount)
                .get();
    }

    public void batchAdd(List<InspectionDesignerConfigScopeEntity> items) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }

        getDBContext(items.get(0).getTenantId()).save(items);
    }

    public void batchDelete(String tenantId, List<String> userAccounts) {
        if (CollectionUtils.isEmpty(userAccounts)) {
            return;
        }
        Query<InspectionDesignerConfigScopeEntity> query = buildQueryByTenantId(tenantId);
        query.field("_id").in(userAccounts);
        getDBContext(tenantId).delete(query);
    }

    public List<InspectionDesignerConfigScopeEntity> getHaveOtherConfigUsers(String tenantId, List<String> ids, String inspectionDesignerId) {
        Query<InspectionDesignerConfigScopeEntity> query = buildQueryByTenantId(tenantId);
        query.field(InspectionDesignerConfigScopeEntity.F_INSPECTION_DESIGNER_ID).notEqual(inspectionDesignerId);
        query.field("_id").in(ids);
        return query.asList();
    }

    public int deleteByInspectionDesignerId(String tenantId, String inspectionDesignerId) {
        Query<InspectionDesignerConfigScopeEntity> query = buildQueryByTenantId(tenantId);
        query.field(InspectionDesignerConfigEntity.F_INSPECTION_DESIGNER_ID).equal(inspectionDesignerId);
        return getDBContext(tenantId).delete(query).getN();
    }

    public List<Integer> queryUseUserIdsByInspectionDesignerId(String tenantId, String inspectionDesignerId) {
        Query<InspectionDesignerConfigScopeEntity> query = buildQueryByTenantId(tenantId);
        query.field(InspectionDesignerConfigScopeEntity.F_INSPECTION_DESIGNER_ID).equal(inspectionDesignerId);
        List<InspectionDesignerConfigScopeEntity> list = query.asList();
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream()
                    .map(o -> AccountUtils.getUserIdByAccount(o.getUserAccount()))
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    private Query<InspectionDesignerConfigScopeEntity> buildQueryByTenantId(String tenantId) {
        return getDBContext(tenantId).createQuery(InspectionDesignerConfigScopeEntity.class)
                .field(InspectionDesignerConfigScopeEntity.F_TENANT_ID).equal(tenantId);
    }
}
