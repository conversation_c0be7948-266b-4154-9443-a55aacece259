package com.facishare.crm.fmcg.sales.dao.mongo.entity;

import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Property;

/**
 * <AUTHOR> liu<PERSON>yu
 * @create : 2023-09-18 16:32
 **/
@Data
@ToString
public class ConditionEntity {
    public static final String F_FIELD_NAME = "FN";
    public static final String F_FIELD_TYPE = "FT";
    public static final String F_OPPORTUNITY = "OY";
    public static final String F_OPERATOR = "O";
    public static final String F_FIELD_VALUE = "FV";
    public static final String F_ROW_NO = "RO";
    @Property(F_FIELD_NAME)
    private String fieldName;
    @Property(F_FIELD_TYPE)
    private String fieldType;
    @Property(F_OPPORTUNITY)
    private String opportunity;
    @Property(F_OPERATOR)
    private String operator;
    @Property(F_FIELD_VALUE)
    private Object fieldValue;
    @Property(F_ROW_NO)
    private Integer rowNo;
}
