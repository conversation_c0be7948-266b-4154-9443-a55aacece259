package com.facishare.crm.fmcg.sales.dao.mongo.inspection;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.sales.dao.mongo.BaseDao;
import com.facishare.crm.fmcg.sales.dao.mongo.entity.inspection.InspectionDesignerConfigEntity;
import com.facishare.crm.fmcg.sales.enums.InspectionDesignerConfigEnum;
import com.github.mongo.support.DatastoreExt;
import io.swagger.models.auth.In;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.mongodb.morphia.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
public class InspectionDesignerConfigDao extends BaseDao {


    public DatastoreExt getDBContext(String tenantId) {
        return mongoContext;
    }


    public void addInspectionDesignerConfig(InspectionDesignerConfigEntity item) {
        if (item.getId() == null) {
            item.setId(new ObjectId());
        }
        getDBContext(item.getTenantId()).save(item);
    }

    public void batchAddInspectionDesignerConfig(List<InspectionDesignerConfigEntity> items) {
        batchSave(items);
    }

    public List<InspectionDesignerConfigEntity> queryByPage(String tenantId, int limit, int offset) {
        return buildQueryByTenantId(tenantId)
                .field(InspectionDesignerConfigEntity.F_GROUP_KEY).equal(InspectionDesignerConfigEnum.globalName.getGroupKey())
                .field(InspectionDesignerConfigEntity.F_KEY).equal(InspectionDesignerConfigEnum.globalName.getKey())
                .limit(limit)
                .offset(offset)
                .order("-" + InspectionDesignerConfigEntity.F_CREATE_TIME)
                .asList();
    }

    public List<InspectionDesignerConfigEntity> queryByPage(String tenantId, InspectionDesignerConfigEnum configEnum, String value, int limit, int offset) {
        Query<InspectionDesignerConfigEntity> query = buildQueryByTenantId(tenantId)
                .field(InspectionDesignerConfigEntity.F_GROUP_KEY).equal(configEnum.getGroupKey())
                .field(InspectionDesignerConfigEntity.F_KEY).equal(configEnum.getKey());
        if (StringUtils.isNotBlank(value)) {
            query.field(InspectionDesignerConfigEntity.F_VALUE).equal(value);
        }
        return query
                .limit(limit)
                .offset(offset)
                .asList();
    }

    public List<InspectionDesignerConfigEntity> queryByFilter(String tenantId, Collection<String> inspectionDesignerIds) {
        return queryByInspectionDesignerIdsAndGroupKey(tenantId, inspectionDesignerIds, Collections.emptyList(), Collections.emptyList(), true);
    }

    public List<InspectionDesignerConfigEntity> queryByFilter(String tenantId, Collection<String> inspectionDesignerIds, Collection<String> groupKeys, Collection<String> keys) {
        return queryByInspectionDesignerIdsAndGroupKey(tenantId, inspectionDesignerIds, groupKeys, keys, false);
    }

    public int deleteByInspectionDesignerId(String tenantId, String inspectionDesignerId) {
        Query<InspectionDesignerConfigEntity> query = buildQueryByTenantId(tenantId);
        query.field(InspectionDesignerConfigEntity.F_INSPECTION_DESIGNER_ID).equal(inspectionDesignerId);
        return getDBContext(tenantId).delete(query).getN();
    }

    public boolean checkNameRepeat(String tenantId, String name) {
        return checkNameRepeat(tenantId, name, null);
    }

    public boolean checkNameRepeat(String tenantId, String name, String inspectionDesignerId) {
        return checkRepeatByConfigEnum(tenantId, InspectionDesignerConfigEnum.globalName, JSONObject.toJSONString(name), inspectionDesignerId);
    }

    public boolean checkRepeatByConfigEnum(String tenantId, InspectionDesignerConfigEnum configEnum, String value, String inspectionDesignerId) {
        Query<InspectionDesignerConfigEntity> query = buildQueryByTenantId(tenantId);
        query.field(InspectionDesignerConfigEntity.F_GROUP_KEY).equal(configEnum.getGroupKey());
        query.field(InspectionDesignerConfigEntity.F_KEY).equal(configEnum.getKey());
        query.field(InspectionDesignerConfigEntity.F_VALUE).equal(value);
        if (StringUtils.isNotBlank(inspectionDesignerId)) {
            query.field(InspectionDesignerConfigEntity.F_INSPECTION_DESIGNER_ID).notEqual(inspectionDesignerId);
        }
        return query.countAll() > 0;
    }

    public Map<String, String> queryInspectionDesignerNameMapOfId(String tenantId, Collection<String> inspectionDesignerIds) {
        Map<String, String> result = new HashMap<>();
        Query<InspectionDesignerConfigEntity> query = buildQueryByTenantId(tenantId);
        query.field(InspectionDesignerConfigEntity.F_INSPECTION_DESIGNER_ID).in(inspectionDesignerIds);
        query.field(InspectionDesignerConfigEntity.F_GROUP_KEY).equal(InspectionDesignerConfigEnum.globalName.getGroupKey());
        query.field(InspectionDesignerConfigEntity.F_KEY).equal(InspectionDesignerConfigEnum.globalName.getKey());
        for (InspectionDesignerConfigEntity inspectionDesignerConfigEntity : query.asList()) {
            result.put(inspectionDesignerConfigEntity.getInspectionDesignerId(), JSONObject.parseObject(inspectionDesignerConfigEntity.getValue(), String.class));
        }
        return result;
    }

    public List<Integer> getUnEffectiveUserIds(String tenantId, String inspectionDesignerId) {
        Query<InspectionDesignerConfigEntity> query = buildQueryByTenantId(tenantId);
        query.field(InspectionDesignerConfigEntity.F_INSPECTION_DESIGNER_ID).equal(inspectionDesignerId);
        query.field(InspectionDesignerConfigEntity.F_GROUP_KEY).equal(InspectionDesignerConfigEnum.businessUnEffectiveUserIds.getGroupKey());
        query.field(InspectionDesignerConfigEntity.F_KEY).equal(InspectionDesignerConfigEnum.businessUnEffectiveUserIds.getKey());
        InspectionDesignerConfigEntity inspectionDesignerConfigEntity = query.get();
        if (Objects.nonNull(inspectionDesignerConfigEntity)) {
            return JSONObject.parseObject(inspectionDesignerConfigEntity.getValue(), List.class);
        }
        return Collections.emptyList();
    }

    private List<InspectionDesignerConfigEntity> queryByInspectionDesignerIdsAndGroupKey(String tenantId, Collection<String> inspectionDesignerIds, Collection<String> groupKeys, Collection<String> keys, boolean acceptKeysEmpty) {
        if (CollectionUtils.isEmpty(inspectionDesignerIds)) {
            return Collections.emptyList();
        }
        if (!acceptKeysEmpty && (CollectionUtils.isEmpty(groupKeys) || CollectionUtils.isEmpty(keys))) {
            return Collections.emptyList();
        }
        Query<InspectionDesignerConfigEntity> query = buildQueryByTenantId(tenantId);
        query.field(InspectionDesignerConfigEntity.F_INSPECTION_DESIGNER_ID).in(inspectionDesignerIds);
        if (CollectionUtils.isNotEmpty(groupKeys)) {
            query.field(InspectionDesignerConfigEntity.F_GROUP_KEY).in(groupKeys);
        }
        if (CollectionUtils.isNotEmpty(keys)) {
            query.field(InspectionDesignerConfigEntity.F_KEY).in(keys);
        }
        return query.asList();
    }

    private void batchSave(List<InspectionDesignerConfigEntity> entities) {
        String tenantId = "";
        for (InspectionDesignerConfigEntity entity : entities) {
            if (entity.getId() == null) {
                entity.setId(new ObjectId());
            }
            tenantId = entity.getTenantId();
        }
        getDBContext(tenantId).save(entities);
    }

    private Query<InspectionDesignerConfigEntity> buildQueryByTenantId(String tenantId) {
        return getDBContext(tenantId)
                .createQuery(InspectionDesignerConfigEntity.class)
                .field(InspectionDesignerConfigEntity.F_TENANT_ID).equal(tenantId);
    }
}
