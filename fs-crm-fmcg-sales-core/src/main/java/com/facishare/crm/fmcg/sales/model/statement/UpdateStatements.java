package com.facishare.crm.fmcg.sales.model.statement;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

public interface UpdateStatements {
    @Data
    class Arg {
        private JSONObject fromArgs;
        private String dataId;
        private String status;
        private Boolean isCancel;
    }

    @Data
    class Result {
        private int errorCode;
        private String message;


        public static Result success() {
            Result result = new Result();
            result.setErrorCode(0);
            result.setMessage("success");
            return result;
        }

        public static Result error(String message) {
            Result result = new Result();
            result.setErrorCode(1);
            result.setMessage(message);
            return result;
        }
    }
}
