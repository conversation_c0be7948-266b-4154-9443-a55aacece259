package com.facishare.crm.fmcg.sales.abs;

import com.alibaba.druid.util.StringUtils;
import com.facishare.crm.fmcg.sales.apiname.InspectionRecordObjApiNames;
import com.facishare.crm.fmcg.sales.business.InspectionBusiness;
import com.facishare.crm.fmcg.sales.enums.MagicEnum;
import com.facishare.crm.fmcg.sales.exception.InspectionVerifyErrorInfo;
import com.facishare.crm.fmcg.sales.exception.InspectionVerifyFailureInterruptException;
import com.facishare.crm.fmcg.sales.model.inspection.InspectionVerify;
import com.facishare.crm.fmcg.sales.utils.I18nUtil;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR> liu<PERSON>yu
 * @create : 2025-06-12 18:12
 **/
@Slf4j
@Component("inspectionAccountInspectionRuleVerifyAction")
public class InspectionAccountInspectionRuleVerifyAction extends AbstractionInspectionRuleVerifyAction {

    @Override
    protected void contextVerify(InspectionVerify.Context context) {
        super.contextVerify(context);
        if (!context.getObjectData().containsField(InspectionRecordObjApiNames.INSPECTION_ACCOUNT_ID)
                || StringUtils.isEmpty(context.getObjectData().get(InspectionRecordObjApiNames.INSPECTION_ACCOUNT_ID, String.class))) {
            throw new InspectionVerifyFailureInterruptException(100001,
                    new InspectionVerifyErrorInfo(I18nUtil.get(MagicEnum.theCurrentRuleMustSelectInspectionAccount), null));
        }
    }

    @Override
    public void verify(InspectionVerify.Context context) {
        super.handleSnBelongData(context);
        String inspectionAccountId = context.getObjectData().get(InspectionRecordObjApiNames.INSPECTION_ACCOUNT_ID, String.class);
        //判断当前码的归属客户是否是当前查询客户
        String belongAccountId = context.getObjectData().get(InspectionRecordObjApiNames.BELONG_ACCOUNT_ID, String.class);
        if (belongAccountId.equals(inspectionAccountId)) {
            context.getObjectData().set(InspectionRecordObjApiNames.INSPECTION_RESULT, "0");
        } else {
            context.getObjectData().set(InspectionRecordObjApiNames.INSPECTION_RESULT, "1");
            context.getObjectData().set(InspectionRecordObjApiNames.ILLEGALLY_GOODS_ACCOUNT_ID, belongAccountId);
        }
    }
}
