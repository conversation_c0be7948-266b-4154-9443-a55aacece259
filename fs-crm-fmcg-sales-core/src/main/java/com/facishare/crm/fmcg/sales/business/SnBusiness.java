package com.facishare.crm.fmcg.sales.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.sales.apiname.CommonApiNames;
import com.facishare.crm.fmcg.sales.apiname.FMCGSerialNumberApiNames;
import com.facishare.crm.fmcg.sales.apiname.FmcgSalesOrderGrayConfig;
import com.facishare.crm.fmcg.sales.apiname.ProductApiNames;
import com.facishare.crm.fmcg.sales.enums.MagicEnum;
import com.facishare.crm.fmcg.sales.exception.ApiException;
import com.facishare.crm.fmcg.sales.model.inspection.InspectionVerify;
import com.facishare.crm.fmcg.sales.utils.CrmFmcgSalesGrayUtil;
import com.facishare.crm.fmcg.sales.utils.I18nUtil;
import com.facishare.crm.fmcg.sales.utils.MengniuEncryptedMarkCode;
import com.facishare.crm.fmcg.sales.utils.MetadataUtil;
import com.facishare.fcp.util.MD5Util;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.fmcg.framework.http.MengNiuProxy;
import com.fmcg.framework.http.contract.mengniu.QueryMarkCodeRelation;
import com.fmcg.framework.http.contract.mengniu.QueryMarkCodeSource;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;

/**
 * <AUTHOR> liuhaoyu
 * @create : 2025-06-19 19:47
 **/
@Component
@Slf4j
public class SnBusiness extends BusinessBase {
    public static final String CLIENT_ID;
    public static final String SK;
    public static final String UNIT_MN;

    static {
        CLIENT_ID = ConfigFactory.getConfig(FmcgSalesOrderGrayConfig.CONFIG_GRAY_REF_FMCG).get("sales_mengniu_openapi_client_id");
        SK = ConfigFactory.getConfig(FmcgSalesOrderGrayConfig.CONFIG_GRAY_REF_FMCG).get("sales_mengniu_openapi_sk");
        UNIT_MN = ConfigFactory.getConfig(FmcgSalesOrderGrayConfig.CONFIG_GRAY_REF_FMCG).get("mengniuSerialNumberUnitId");
    }

    @Resource
    private MengNiuProxy mengNiuProxy;
    @Resource
    private InspectionBusiness inspectionBusiness;

    public List<IObjectData> buildAndCreateNewSnData(JSONObject markCodeRelation, IObjectData productData, InspectionVerify.Context context) {
        if (Objects.isNull(markCodeRelation.get("markTreeVOList")) || CollectionUtils.isEmpty(markCodeRelation.getJSONArray("markTreeVOList")) || Objects.isNull(markCodeRelation.getJSONObject("maInfoBO"))) {
            return null;
        }
        if (Objects.isNull(productData)) {
            return null;
        }

        JSONObject maInfoBO = markCodeRelation.getJSONObject("maInfoBO");
        Long manufactureDate = maInfoBO.getLong("productManufacturingDate");
        String packLevel = maInfoBO.getString("packLevel");
        String manufacturingBatchNo = maInfoBO.getString("manufacturingBatchNo");
        String productId = productData.getId();

        List<JSONObject> markTreeVOList = markCodeRelation.getJSONArray("markTreeVOList").toJavaList(JSONObject.class);
        List<IObjectData> dataList = Lists.newArrayList();
        if ("4".equals(packLevel)) {
            String turnoverBoxCode = MengniuEncryptedMarkCode.getEncryptedMarkCode(context.getSourceCode());
            for (JSONObject object : markTreeVOList) {
                if (!"3".equals(object.getString("packLevel"))) {
                    continue;
                }
                IObjectData snData = new ObjectData();
                snData.setRecordType("default__c");
                snData.setTenantId(context.getUpstreamTenantId());
                snData.setDescribeApiName(FMCGSerialNumberApiNames.OBJECT_API_NAME);
                snData.set(CommonApiNames.NAME, MengniuEncryptedMarkCode.getEncryptedMarkCode(object.getString("code")));
                snData.set(FMCGSerialNumberApiNames.PRODUCT_ID, productId);
                snData.set(FMCGSerialNumberApiNames.MANUFACTURE_DATE, manufactureDate);
                snData.set(FMCGSerialNumberApiNames.BATCH_CODE, manufacturingBatchNo);
                snData.set(FMCGSerialNumberApiNames.CODE_TRUE, object.getString("code"));
                snData.set(FMCGSerialNumberApiNames.UNIT, UNIT_MN);
                snData.setIsPublic(true);
                snData.set(FMCGSerialNumberApiNames.TURNOVER_BOX_CODE, turnoverBoxCode);
                dataList.add(snData);
            }
        } else if ("3".equals(packLevel)) {
            IObjectData snData = new ObjectData();
            snData.setRecordType("default__c");
            snData.setTenantId(context.getUpstreamTenantId());
            snData.setDescribeApiName(FMCGSerialNumberApiNames.OBJECT_API_NAME);
            snData.set(CommonApiNames.NAME, MengniuEncryptedMarkCode.getEncryptedMarkCode(context.getSourceCode()));
            snData.set(FMCGSerialNumberApiNames.PRODUCT_ID, productId);
            snData.set(FMCGSerialNumberApiNames.MANUFACTURE_DATE, manufactureDate);
            snData.set(FMCGSerialNumberApiNames.BATCH_CODE, manufacturingBatchNo);
            snData.set(FMCGSerialNumberApiNames.CODE_TRUE, context.getSourceCode());
            snData.set(FMCGSerialNumberApiNames.UNIT, UNIT_MN);
            snData.setIsPublic(true);
            for (JSONObject object : markTreeVOList) {
                if ("4".equals(object.getString("packLevel"))) {
                    snData.set(FMCGSerialNumberApiNames.TURNOVER_BOX_CODE, MengniuEncryptedMarkCode.getEncryptedMarkCode(object.getString("code")));
                    break;
                }
            }
            dataList.add(snData);
        }
        log.info("buildAndCreateNewSnData bulkSaveObjectData arg: {}", JSON.toJSONString(dataList));
        List<IObjectData> snDataList = serviceFacade.bulkSaveObjectData(dataList, User.systemUser(context.getUpstreamTenantId()));
        log.info("buildAndCreateNewSnData bulkSaveObjectData result: {}", JSON.toJSONString(snDataList));
        context.getFsStopWatch().lap("cof-batchSaveSn");
        return snDataList;
    }

    public IObjectData getProductFromMarkCodeRelation(JSONObject markCodeRelation, InspectionVerify.Context context) {
        JSONObject maInfoBO = markCodeRelation.getJSONObject("maInfoBO");
        if (Objects.isNull(maInfoBO)) {
            return null;
        }
        String productCode = maInfoBO.getString("productCode");
        if (StringUtils.isBlank(productCode)) {
            return null;
        }
        List<IObjectData> dataList = inspectionBusiness.queryWithFields(context.getTenantId(), ProductApiNames.OBJECT_API_NAME,
                Lists.newArrayList(MetadataUtil.filter(ProductApiNames.PRODUCT_CODE, Operator.EQ, Lists.newArrayList(productCode))),
                Lists.newArrayList(CommonApiNames.ID, CommonApiNames.NAME));
        if (org.apache.commons.collections.CollectionUtils.isEmpty(dataList)) {
            return null;
        }
        return dataList.get(0);
    }

    public JSONObject queryMarkCodeRelation(String markCode) {
        if (StringUtils.isEmpty(markCode)) {
            return null;
        }
        long time = new Date().getTime();
        String sign = MD5Util.toMD5Hex((CLIENT_ID + SK + time).getBytes());
        String s = sign.toUpperCase(Locale.ROOT);

        JSONObject requestData = new JSONObject();
        requestData.put("markCode", markCode);
        QueryMarkCodeRelation.Result result;
        try {
            QueryMarkCodeRelation.Arg arg = new QueryMarkCodeRelation.Arg();
            arg.setRequestData(JSON.toJSONString(requestData));
            long start = System.currentTimeMillis();
            log.info("queryMarkCodeRelation Begin.clientId：{}, sign:{}, time:{}", CLIENT_ID, s, time);
            result = mengNiuProxy.queryMarkCodeRelation(CLIENT_ID, s, String.valueOf(time), arg);
            log.info("queryMarkCodeRelation End. existQueryData:{}, cost:{}", JSON.toJSONString(result), (System.currentTimeMillis() - start));
        } catch (Exception e) {
            log.info("queryMarkCodeRelation is error", e);
            throw new ApiException(100101, I18nUtil.get(MagicEnum.queryMarkCodeRelationInterfaceException));
        }
        if (Objects.isNull(result) || Objects.isNull(result.getData())) {
            return null;
        }
        return result.getData();
    }

    public JSONObject queryMarkCodeSource(String markCode) {
        if (StringUtils.isEmpty(markCode)) {
            return null;
        }
        long time = new Date().getTime();
        String sign = MD5Util.toMD5Hex((CLIENT_ID + SK + time).getBytes());
        String s = sign.toUpperCase(Locale.ROOT);

        JSONObject requestData = new JSONObject();
        requestData.put("markCode", markCode);
        QueryMarkCodeSource.Result result;
        try {
            QueryMarkCodeSource.Arg arg = new QueryMarkCodeSource.Arg();
            arg.setRequestData(JSON.toJSONString(requestData));
            long start = System.currentTimeMillis();
            log.info("queryMarkCodeSource Begin.clientId：{}, sign:{}, time:{}", CLIENT_ID, s, time);
            result = mengNiuProxy.queryMarkCodeSource(CLIENT_ID, s, String.valueOf(time), arg);
            log.info("queryMarkCodeSource End. existQueryData:{}, cost:{}", JSON.toJSONString(result), (System.currentTimeMillis() - start));
        } catch (Exception e) {
            log.info("queryMarkCodeSource is error", e);
            throw new ApiException(100101, I18nUtil.get(MagicEnum.queryMarkCodeSourceInterfaceException));
        }
        if (Objects.isNull(result) || Objects.isNull(result.getData())) {
            return null;
        }
        return result.getData();
    }

    /**
     * 3：提，4：箱
     */
    public static String getCodeType(String tenantId, String sourceCode) {
        if (CrmFmcgSalesGrayUtil.isMengNiu(tenantId)) {
            if (StringUtils.isNotEmpty(sourceCode) && sourceCode.length() >= 3) {
                return sourceCode.charAt(2) + "";
            }
        }
        return null;
    }
}
