package com.facishare.crm.fmcg.sales.model.interconnection;

import com.fxiaoke.api.model.BaseResult;
import lombok.Data;
import lombok.ToString;

/**
 * @author: liu<PERSON>yu
 * @create: 2024-04-22 15:23
 **/
public interface CreateRelation {
    @Data
    @ToString
    class Arg {
        private Integer upstreamTenantId;
        private Integer downstreamTenantId;
        private String mapperObjectId;
        private String mapperApiName;
        private String downstreamShortName;
    }

    @Data
    @ToString
    class Result extends BaseResult {
        public static Result success() {
            Result result = new Result();
            result.setErrorCode(0);
            result.setErrorMsg("success");
            return result;
        }
    }
}
