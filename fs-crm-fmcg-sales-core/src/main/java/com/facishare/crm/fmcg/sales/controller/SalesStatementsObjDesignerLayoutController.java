package com.facishare.crm.fmcg.sales.controller;

import com.beust.jcommander.internal.Lists;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardDesignerLayoutController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class SalesStatementsObjDesignerLayoutController extends StandardDesignerLayoutController {

    @Override
    protected Result after(Arg arg, Result result) {
        buttonFilter(result);
        return super.after(arg, result);
    }

    public void buttonFilter(StandardDesignerLayoutController.Result result) {
        List components = (ArrayList) (result.getLayout().get("components"));
        List<String> removeList = Lists.newArrayList();
        removeList.add(ObjectAction.CANCEL_ENTRY.getActionCode());
        removeList.add(ObjectAction.ENTER_ACCOUNT.getActionCode());
        removeButton(components, removeList);
    }

    private void removeButton(List components, List<String> removeList) {
        for (Object component : components) {
            Map com = (Map) component;
            if ("head_info".equals(com.get("api_name"))) {
                ArrayList buttons = (ArrayList) com.get("buttons");
                buttons.removeIf(button -> {
                    Map btn = (Map) (button);
                    return removeList.contains(btn.get("action"));
                });
            }
        }
    }
}