package com.facishare.crm.fmcg.sales.action;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.sales.enums.ButtonAction;
import com.facishare.crm.fmcg.sales.enums.MagicEnum;
import com.facishare.crm.fmcg.sales.service.SalesStatementsService;
import com.facishare.crm.fmcg.sales.utils.I18nUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
public class SalesStatementsObjCloseStatementsAction extends PreDefineAction<SalesStatementsObjCloseStatementsAction.Arg, SalesStatementsObjCloseStatementsAction.Result> {

    private IObjectData objectData;

    private final SalesStatementsService salesStatementsService = SpringUtil.getContext().getBean(SalesStatementsService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ButtonAction.Close_Statements.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getObjectDataId());
    }

    @Override
    protected void init() {
        super.init();
        if (CollectionUtils.notEmpty(dataList)) {
            objectData = dataList.get(0);
        }
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        String statementStatus = objectData.get("statement_status", String.class);
        if (!SalesStatementsObjConformanceStatementsAction.OPEN.equals(statementStatus)) {
            throw new ValidateException(I18nUtil.get(MagicEnum.cannotDirectlyChangeThisState));
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        log.info("actionContext={},objectData{}", actionContext, objectData);
        //修改对账单状态
        Map<String, Object> updateMap = new HashMap<>(4);
        updateMap.put("statement_status", SalesStatementsObjConformanceStatementsAction.CLOSE_OFF);
        updateMap.put("payer", Lists.newArrayList(actionContext.getUser().getUserId()));
        if (Objects.nonNull(arg.getArgs())) {
            if (arg.getArgs().containsKey("form_actual_payment")) {
                updateMap.put("form_actual_payment", arg.getArgs().get("form_actual_payment"));
            }
        }
        ServiceContext context = new ServiceContext(actionContext.getRequestContext(), "", "");
        IObjectData data = salesStatementsService.updateStatementsStatus(objectData, updateMap, SalesStatementsObjConformanceStatementsAction.CLOSE_OFF, objectDescribe, context);
        return Result.of(data);
    }

    @Data
    public static class Arg {
        private String objectDataId;

        private JSONObject args;
    }

    @Data
    public static class Result {
        private ObjectDataDocument objectData;

        public static Result of(IObjectData objectData) {
            Result result = new Result();
            result.setObjectData(ObjectDataDocument.of(objectData));
            return result;
        }
    }
}
