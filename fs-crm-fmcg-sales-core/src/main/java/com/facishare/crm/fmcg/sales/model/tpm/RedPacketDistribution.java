package com.facishare.crm.fmcg.sales.model.tpm;

import com.fxiaoke.api.model.BaseResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @create: 2025-02-25 11:02
 **/
public interface RedPacketDistribution {
    @Data
    @ToString
    class Arg {
        private RedPacketDistribution.ArgBody arg;
    }

    @Data
    @ToString
    class ArgBody {
        private String tenantId;
        private String dataId;
        private String apiName;
    }

    @Data
    @ToString
    class Result extends BaseResult {
        private Content data;

        public static Result error(Integer errorCode, String errorMsg) {
            Result result = new Result();
            result.setErrorCode(errorCode);
            result.setErrorMsg(errorMsg);
            return result;
        }

        public static Result success(String redPackId, String errorMsg) {
            Result result = new Result();
            result.setData(new Content(redPackId));
            result.setErrorCode(0);
            result.setErrorMsg(errorMsg);
            return result;
        }
    }

    @Data
    @ToString
    @AllArgsConstructor
    class Content implements Serializable {
        private String redPackId;
    }
}
