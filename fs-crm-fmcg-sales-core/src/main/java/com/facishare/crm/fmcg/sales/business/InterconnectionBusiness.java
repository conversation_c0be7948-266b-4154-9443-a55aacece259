package com.facishare.crm.fmcg.sales.business;

import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.sales.apiname.AccountObjApiNames;
import com.facishare.crm.fmcg.sales.apiname.EnterpriseRelationObjApiNames;
import com.facishare.crm.fmcg.sales.exception.ApiException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.ActionContextUtil;
import com.fxiaoke.enterpriserelation2.arg.TenantIdCascadeArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.service.EnterpriseRelationService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Slf4j
@Component
public class InterconnectionBusiness {
    @Autowired
    private EnterpriseRelationService enterpriseRelationService;

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private EIEAConverter eieaConverter;

    public IObjectData getTenantInfoInUpstream(String upstreamId, String tenantId, List<String> fields) {
        String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
        IFilter eaFilter = new Filter();
        eaFilter.setFieldValues(Lists.newArrayList(ea));
        eaFilter.setOperator(Operator.EQ);
        eaFilter.setFieldName(EnterpriseRelationObjApiNames.ENTERPRISE_ACCOUNT);

        SearchTemplateQuery queryTemplate = new SearchTemplateQuery();
        queryTemplate.setLimit(1);
        queryTemplate.setFilters(Lists.newArrayList(eaFilter));

        IActionContext actionContext = ActionContextUtil.getNewContext(upstreamId);
        actionContext.setUserId(User.systemUser(upstreamId).getUserId());
        actionContext.setPrivilegeCheck(false);

        List<IObjectData> data = serviceFacade.findBySearchTemplateQueryWithFields(
                actionContext,
                EnterpriseRelationObjApiNames.OBJECT_API_NAME,
                queryTemplate,
                fields).getData();
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        return data.get(0);
    }

    public List<Integer> getUpstreamTenantIds(String tenantId) {
        HeaderObj headerObj = HeaderObj.newInstance(null);
        TenantIdCascadeArg tenantIdCascadeArg = new TenantIdCascadeArg();
        tenantIdCascadeArg.setTenantId(Integer.parseInt(tenantId));
        RestResult<Set<Integer>> result = enterpriseRelationService.listAllUpstreamTenantIds(headerObj, tenantIdCascadeArg);
        if (result.getErrCode() != 0) {
            log.info("getUpstreamTenantIds error : {}, {}", result.getErrCode(), result.getErrMsg());
            throw new ApiException(1, String.format("getUpstreamTenantIds error : %s, %s", result.getErrCode(), result.getErrMsg()));
        }
        if (CollectionUtils.isEmpty(result.getData())) {
            return Lists.newArrayList();
        }
        return new ArrayList<>(result.getData());
    }

    public IObjectData getAccountInfoInUpstream(String tenantId) {
        List<Integer> upstreamTenantIds = getUpstreamTenantIds(tenantId);
        if (CollectionUtils.isEmpty(upstreamTenantIds)) {
            return null;
        }
        String upstreamTenantId = String.valueOf(upstreamTenantIds.get(0));
        return getAccountInfoInUpstream(tenantId, upstreamTenantId);
    }

    public IObjectData getAccountInfoInUpstream(String tenantId, String upstreamTenantId) {
        IObjectData tenantInfoInUpstream = getTenantInfoInUpstream(upstreamTenantId, tenantId, Lists.newArrayList(EnterpriseRelationObjApiNames.MAPPER_ACCOUNT_ID));
        String upstreamAccountId = String.valueOf(tenantInfoInUpstream.get(EnterpriseRelationObjApiNames.MAPPER_ACCOUNT_ID));
        if (Strings.isNullOrEmpty(upstreamAccountId)) {
            return null;
        }

        return serviceFacade.findObjectData(User.systemUser(upstreamTenantId), upstreamAccountId, AccountObjApiNames.OBJECT_API_NAME);
    }
}
