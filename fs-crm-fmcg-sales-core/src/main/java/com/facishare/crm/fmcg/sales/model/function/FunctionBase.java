package com.facishare.crm.fmcg.sales.model.function;

import lombok.Data;

public interface FunctionBase {
    @Data
    class Arg {
        private String tenantId;
        private String objectApiName;
    }

    @Data
    class Result {
        private int code;

        private String message;

        public static <E extends Result> E error(E result, String msg) {
            result.setCode(-2);
            result.setMessage(msg);
            return result;
        }

        public static <E extends Result> E success(E result) {
            result.setCode(0);
            result.setMessage("ok");
            return result;
        }
    }
}
