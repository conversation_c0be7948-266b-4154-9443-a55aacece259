package com.facishare.crm.fmcg.sales.pool;

import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.util.concurrent.ThreadFactoryBuilder;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> l<PERSON><PERSON><PERSON>
 * @create : 2024-07-25 10:28
 **/
public class InspectionBusinessPool {
    public static final Integer DEFAULT_CORE_POOL_SIZE = 5;
    public static final Integer DEFAULT_MAXIMUM_POOL_SIZE = 30;
    public static final Integer DEFAULT_KEEP_ALIVE_TIME = 2000;
    public static final Integer DEFAULT_QUEUE_SIZE = 3000;
    private static final ThreadPoolExecutor INSPECTION_BUSINESS_POOL;

    static {
        INSPECTION_BUSINESS_POOL = new ThreadPoolExecutor(
                DEFAULT_CORE_POOL_SIZE, DEFAULT_MAXIMUM_POOL_SIZE, DEFAULT_KEEP_ALIVE_TIME, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(DEFAULT_QUEUE_SIZE),
                new ThreadFactoryBuilder()
                        .setDaemon(true)
                        .setNameFormat("inspection-business-thread-%s")
                        .build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    public static void execute(Runnable runnable) {
        INSPECTION_BUSINESS_POOL.submit(MonitorTaskWrapper.wrap(runnable));
    }
}
