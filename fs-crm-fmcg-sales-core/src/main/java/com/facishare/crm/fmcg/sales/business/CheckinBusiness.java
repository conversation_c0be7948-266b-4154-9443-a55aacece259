package com.facishare.crm.fmcg.sales.business;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.fmcg.framework.http.CheckinProxy;
import com.fmcg.framework.http.contract.checkin.GetSalesAreaByPoint;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class CheckinBusiness {

    @Resource
    private CheckinProxy checkinProxy;

    @Resource
    private EIEAConverter eieaConverter;


    /**
     * @param geo 定位
     */
    public List<String> getSalesAreaByPoint(Integer tenantId, String geo) {
        try {
            GetSalesAreaByPoint.Arg arg = new GetSalesAreaByPoint.Arg();
            arg.setEa(eieaConverter.enterpriseIdToAccount(tenantId));
            arg.setGeo(geo);
            log.info("checkinProxy.getSalesAreaByPoint arg={}", JSONObject.toJSONString(arg));
            GetSalesAreaByPoint.Result result = checkinProxy.getSalesAreaByPoint(tenantId, -10000, arg);
            log.info("checkinProxy.getSalesAreaByPoint result={}", JSONObject.toJSONString(result));
            if (Objects.nonNull(result) && CollectionUtils.isNotEmpty(result.getSalesAreaIdList())) {
                return result.getSalesAreaIdList();
            }
        } catch (Exception e) {
            log.error("checkinProxy.getSalesAreaByPoint exception ", e);
        }
        return Collections.emptyList();
    }
}
