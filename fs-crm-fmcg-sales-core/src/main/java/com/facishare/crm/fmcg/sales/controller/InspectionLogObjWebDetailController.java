package com.facishare.crm.fmcg.sales.controller;

import com.facishare.crm.fmcg.sales.utils.ButtonUtils;
import com.google.common.collect.Lists;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.google.common.collect.Sets;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> l<PERSON><PERSON><PERSON>
 * @create : 2025-03-04 11:43
 **/
public class InspectionLogObjWebDetailController extends StandardWebDetailController {
    @Override
    protected Result after(Arg arg, Result result) {
        buttonFilter(arg, result);
        return super.after(arg, result);
    }

    public void buttonFilter(Arg arg, Result result) {
        for (Object component : (ArrayList) result.getLayout().get("components")) {
            Map com = (Map) component;
            if ("head_info".equals(com.get("api_name"))) {
                ButtonUtils.deleteOtherThanTheSpecifiedButtonActions((ArrayList) com.get("buttons"),
                        Sets.newHashSet(), true);
                return;
            }
        }
    }
}
