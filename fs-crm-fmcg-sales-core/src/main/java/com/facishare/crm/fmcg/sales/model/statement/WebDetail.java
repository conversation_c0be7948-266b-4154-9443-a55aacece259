package com.facishare.crm.fmcg.sales.model.statement;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface WebDetail {
    @Data
    class Arg {
        /**
         * 业务员姓名
         */
        private List<String> salesmanName;

        /**
         * 时间
         */
        private Long statementDate;
    }

    @Data
    class Result {
        private String statementStatus;

        private Top top;
        private Center center;
        private Bottom bottom;
    }

    @Data
    class Top {
        private LabelAndValue title;
        private List<LabelAndValue> subTitle;
        private List<LabelAndValue> normalList;
    }

    @Data
    class Center {
        private List<CenterItem> dataList;

    }

    @Data
    class CenterItem {
        private LabelAndValue data;
        private List<LabelAndValue> subDataList;
    }

    @Data
    class Bottom {
        private List<BottomIem> dataList;
    }

    @Data
    class BottomIem {
        private String title;
        private String titleTip;
        private List<LabelAndValue> dataList;
    }

    @Data
    @NoArgsConstructor
    class LabelAndValue {
        private String label;
        private String value;
        private String tip;
        private String color;

        public LabelAndValue(String label, String value) {
            this.label = label;
            this.value = value;
        }

        public LabelAndValue(String label, String value, String tip) {
            this.label = label;
            this.value = value;
            this.tip = tip;
        }

        public LabelAndValue(String label, String value, String tip, String color) {
            this.label = label;
            this.value = value;
            this.tip = tip;
            this.color = color;
        }
    }
}
