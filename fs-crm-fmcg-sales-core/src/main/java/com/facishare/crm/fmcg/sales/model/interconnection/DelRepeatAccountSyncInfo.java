package com.facishare.crm.fmcg.sales.model.interconnection;

import com.fxiaoke.api.model.BaseResult;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @create: 2024-01-22 16:08
 **/
public interface DelRepeatAccountSyncInfo {
    @Data
    @ToString
    class Arg implements Serializable {
        private ArgBody arg;
    }

    @Data
    @ToString
    class ArgBody {
        private String dataId;
        private String upstreamTenantId;
    }

    @Data
    @ToString
    class Result extends BaseResult {
    }
}
