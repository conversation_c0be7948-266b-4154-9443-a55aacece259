package com.facishare.crm.fmcg.sales.model.inspection.designer;

import com.fxiaoke.api.model.BaseResult;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


public interface CopyVO {
    @Data
    @ToString
    class Arg {
        private String inspectionDesignerId;

        private String name;
    }


    @EqualsAndHashCode(callSuper = true)
    @Data
    @ToString
    class Result extends BaseResult {

        public static Result success() {
            Result result = new Result();
            result.setErrorCode(0);
            result.setErrorMsg("success");
            return result;
        }

        public static Result error(String errorMsg) {
            Result result = new Result();
            result.setErrorCode(400);
            result.setErrorMsg(errorMsg);
            return result;
        }
    }
}
