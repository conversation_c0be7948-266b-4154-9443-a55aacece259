package com.facishare.crm.fmcg.sales.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.sales.model.bo.ObjectRecordType;
import com.fmcg.framework.http.RecordTypeProxy;
import com.fmcg.framework.http.contract.recordtype.RecordType;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@Service
public class RecordTypeManagerService {

    @Resource
    private RecordTypeProxy recordTypeProxy;

    public void createRecordType(int tenantId, ObjectRecordType.AddOne addOneArg) {
        Map<String, Object> recordTypeInfo = Maps.newHashMapWithExpectedSize(4);
        recordTypeInfo.put("api_name", addOneArg.getRecordType());
        recordTypeInfo.put("label", addOneArg.getLabel());
        recordTypeInfo.put("description", addOneArg.getDescription());
        recordTypeInfo.put("is_active", addOneArg.isActive());

        RecordType.Arg arg = new RecordType.Arg();
        arg.setDescribeApiName(addOneArg.getObjectApiName());
        arg.setRecordType(JSON.toJSONString(recordTypeInfo));
        try {
            recordTypeProxy.create(tenantId, -10000, arg);
        } catch (Exception e) {
            log.error("createRecordType error", e);
        }
    }
}
