package com.facishare.crm.fmcg.sales.action;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAsyncBulkAction;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> liu<PERSON><PERSON>
 * @create : 2025-03-24 15:34
 **/
public class InspectionRecordObjAsyncBulkInspectionRecordOkAction extends AbstractStandardAsyncBulkAction<InspectionRecordObjAsyncBulkInspectionRecordOkAction.Arg, InspectionRecordObjInspectionRecordOkAction.Arg> {

    @Override
    protected String getDataIdByParam(InspectionRecordObjInspectionRecordOkAction.Arg arg) {
        return arg.getObjectDataId();
    }

    @Override
    protected List<InspectionRecordObjInspectionRecordOkAction.Arg> getButtonParams() {
        return arg.getDataIds().stream()
                .map(id -> {
                    InspectionRecordObjInspectionRecordOkAction.Arg arg = new InspectionRecordObjInspectionRecordOkAction.Arg();
                    arg.setObjectDataId(id);
                    return arg;
                })
                .collect(Collectors.toList());
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.INSPECTION_RECORD_OK.getButtonApiName();
    }

    @Override
    protected String getActionCode() {
        return ObjectAction.INSPECTION_RECORD_OK.getActionCode();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.INSPECTION_RECORD_OK.getActionCode());
    }

    @Data
    public static class Arg {
        private List<String> dataIds;
    }
}
