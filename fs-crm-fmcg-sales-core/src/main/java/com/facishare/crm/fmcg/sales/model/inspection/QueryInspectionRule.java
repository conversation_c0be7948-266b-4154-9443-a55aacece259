package com.facishare.crm.fmcg.sales.model.inspection;

import com.fxiaoke.api.model.BaseResult;
import lombok.Data;
import lombok.ToString;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> liu<PERSON><PERSON>
 * @create : 2024-07-25 14:37
 **/
public interface QueryInspectionRule {
    @Data
    @ToString
    class Arg {
        private Set<String> ruleIds;
        private Boolean findAll;
    }

    @Data
    @ToString
    class Result extends BaseResult {
        private List<InspectionRule> rules;

        public static Result success(List<InspectionRule> rules) {
            Result result = new Result();
            result.setErrorCode(0);
            result.setErrorMsg("success");
            result.setRules(rules);
            return result;
        }
    }
}
