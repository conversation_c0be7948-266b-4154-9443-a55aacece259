package com.facishare.crm.fmcg.sales.model.inspection.designer;

import com.fxiaoke.api.model.BaseResult;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Collections;
import java.util.List;

public interface GetConfigUserSetVO {
    @Data
    @ToString
    class Arg {
        private String inspectionDesignerId;

    }


    @EqualsAndHashCode(callSuper = true)
    @Data
    @ToString
    class Result extends BaseResult {
        private List<Integer> effectiveUserIds;
        private List<Integer> unEffectiveUserIds;
        private List<HaveOtherConfig> haveHaveOtherConfigUserIds;


        public static Result emptyResult() {
            Result result = new Result();
            result.setEffectiveUserIds(Collections.emptyList());
            result.setUnEffectiveUserIds(Collections.emptyList());
            result.setHaveHaveOtherConfigUserIds(Collections.emptyList());
            return result;
        }
    }

}
