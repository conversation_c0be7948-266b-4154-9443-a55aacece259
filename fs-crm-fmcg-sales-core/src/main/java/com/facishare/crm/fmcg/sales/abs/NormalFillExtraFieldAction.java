package com.facishare.crm.fmcg.sales.abs;

import com.facishare.crm.fmcg.sales.abs.abstraction.FillExtraFieldAction;
import com.facishare.crm.fmcg.sales.apiname.*;
import com.facishare.crm.fmcg.sales.business.InspectionBusiness;
import com.facishare.crm.fmcg.sales.model.inspection.FillExtraField;
import com.facishare.crm.fmcg.sales.service.InspectionService;
import com.facishare.crm.fmcg.sales.utils.DataUtil;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> liuhaoyu
 * @create : 2025-03-25 15:32
 **/
@Slf4j
@Component("normalFillExtraFieldAction")
public class NormalFillExtraFieldAction implements FillExtraFieldAction<FillExtraField.Context> {
    public static final List<String> FILL_FIELDS_API_NAMES = Lists.newArrayList(InspectionRecordObjApiNames.PRODUCT_NAME, InspectionRecordObjApiNames.PRODUCT_CATEGORY_ID, InspectionRecordObjApiNames.PRODUCT_CATEGORY_NAME,
            InspectionRecordObjApiNames.BELONG_ACCOUNT_NAME, InspectionRecordObjApiNames.BELONG_ACCOUNT_NO, InspectionRecordObjApiNames.COVERING_SALES_AREAS_ID,
            InspectionRecordObjApiNames.ILLEGALLY_GOODS_ACCOUNT_NAME, InspectionRecordObjApiNames.ILLEGALLY_GOODS_TENANT_ID, InspectionRecordObjApiNames.INSPECTION_PERSONNEL_NAME,
            InspectionRecordObjApiNames.INSPECTION_PERSONNEL_ROLE, InspectionRecordObjApiNames.INSPECTION_PERSONNEL_ROLE_ID);

    @Resource
    private InspectionService inspectionService;
    @Resource
    private InspectionBusiness inspectionBusiness;

    @Override
    public Map<String, Map<String, Object>> action(FillExtraField.Context context) {
        Map<String, Map<String, Object>> result = Maps.newHashMap();

        findProduct(context);
        findProductCategory(context);
        findBelongAccount(context);
        Map<String, Object> inspectionPersonnelInfo = inspectionService.findInspectionPersonnelInfo(context.getVerifyContext());
        context.setInspectionPersonnelInfo(inspectionPersonnelInfo);

        for (IObjectData objectData : context.getObjectDataList()) {
            Map<String, Object> processingResult = processingData(context, objectData);
            result.put(objectData.get(InspectionRecordObjApiNames.FMCG_SERIAL_NUMBER_ID, String.class), processingResult);
        }

        this.fillCustomizedData(result, context);
        return result;
    }

    @Override
    public List<String> getFillFieldsApiName(FillExtraField.Context context) {
        List<String> customizedFillFieldsApiName = getCustomizedFillFieldsApiName(context);
        if (CollectionUtils.isEmpty(customizedFillFieldsApiName)) {
            return FILL_FIELDS_API_NAMES;
        }
        List<String> result = Lists.newArrayList(FILL_FIELDS_API_NAMES);
        result.addAll(customizedFillFieldsApiName);
        return result;
    }

    private void findProduct(FillExtraField.Context context) {
        List<String> productIds = DataUtil.collectSpecifiedField(context.getObjectDataList(), InspectionRecordObjApiNames.PRODUCT_ID, String.class);
        if (CollectionUtils.isEmpty(productIds)) {
            return;
        }
        List<IObjectData> products = inspectionBusiness.findByIds(context.getVerifyContext().getTenantId(), productIds, ProductObjApiNames.OBJECT_API_NAME);
        Map<String, IObjectData> collect = products.stream()
                .collect(Collectors.toMap(DBRecord::getId, p -> p));
        context.setProductIdMap(collect);
    }

    private void findProductCategory(FillExtraField.Context context) {
        List<String> productCategoryIds = DataUtil.collectSpecifiedField(context.getProductIdMap().values(), ProductObjApiNames.PRODUCT_CATEGORY_ID, String.class);
        if (CollectionUtils.isEmpty(productCategoryIds)) {
            return;
        }
        List<IObjectData> productCategory = inspectionBusiness.findByIds(context.getVerifyContext().getTenantId(), productCategoryIds, ProductCategoryObjApiNames.OBJECT_API_NAME);
        Map<String, IObjectData> collect = productCategory.stream()
                .collect(Collectors.toMap(DBRecord::getId, p -> p));
        context.setProductCategoryIdMap(collect);
    }

    private void findBelongAccount(FillExtraField.Context context) {
        List<String> accountIds = DataUtil.collectSpecifiedField(context.getObjectDataList(), InspectionRecordObjApiNames.BELONG_ACCOUNT_ID, String.class);
        if (CollectionUtils.isEmpty(accountIds)) {
            return;
        }
        List<IObjectData> accountData = inspectionBusiness.findByIds(context.getVerifyContext().getUpstreamTenantId(), accountIds, AccountObjApiNames.OBJECT_API_NAME);
        Map<String, IObjectData> collect = accountData.stream()
                .collect(Collectors.toMap(DBRecord::getId, p -> p));
        context.setBelongAccountIdMap(collect);
    }

    private Map<String, Object> processingData(FillExtraField.Context context, IObjectData currentObjectData) {
        Map<String, Object> updateMap = Maps.newHashMap();

        String productId = currentObjectData.get(InspectionRecordObjApiNames.PRODUCT_ID, String.class);
        if (context.getProductIdMap().containsKey(productId)) {
            IObjectData objectData = context.getProductIdMap().get(productId);
            updateMap.put(InspectionRecordObjApiNames.PRODUCT_NAME, objectData.get(CommonApiNames.NAME));
            updateMap.put(InspectionRecordObjApiNames.PRODUCT_CATEGORY_ID, objectData.get(ProductObjApiNames.PRODUCT_CATEGORY_ID));
        }
        String productCategoryId = String.valueOf(updateMap.get(InspectionRecordObjApiNames.PRODUCT_CATEGORY_ID));
        if (context.getProductCategoryIdMap().containsKey(productCategoryId)) {
            IObjectData objectData = context.getProductCategoryIdMap().get(productCategoryId);
            updateMap.put(InspectionRecordObjApiNames.PRODUCT_CATEGORY_NAME, objectData.get(CommonApiNames.NAME));
        }
        String belongAccountId = currentObjectData.get(InspectionRecordObjApiNames.BELONG_ACCOUNT_ID, String.class);
        if (context.getBelongAccountIdMap().containsKey(belongAccountId)) {
            IObjectData objectData = context.getBelongAccountIdMap().get(belongAccountId);
            updateMap.put(InspectionRecordObjApiNames.BELONG_ACCOUNT_NAME, objectData.get(CommonApiNames.NAME));
            updateMap.put(InspectionRecordObjApiNames.BELONG_ACCOUNT_NO, objectData.get(AccountObjApiNames.ACCOUNT_NO));
            updateMap.put(InspectionRecordObjApiNames.COVERING_SALES_AREAS_ID, objectData.get(AccountObjApiNames.COVERING_SALES_AREAS));
        }
        updateMap.putAll(context.getInspectionPersonnelInfo());
        if ("1".equals(currentObjectData.get(InspectionRecordObjApiNames.INSPECTION_RESULT, String.class))) {
            if (Objects.nonNull(updateMap.get(InspectionRecordObjApiNames.BELONG_ACCOUNT_NAME))) {
                updateMap.put(InspectionRecordObjApiNames.ILLEGALLY_GOODS_ACCOUNT_NAME, updateMap.get(InspectionRecordObjApiNames.BELONG_ACCOUNT_NAME));
            }
            if (Objects.nonNull(updateMap.get(InspectionRecordObjApiNames.BELONG_TENANT_ID))) {
                updateMap.put(InspectionRecordObjApiNames.ILLEGALLY_GOODS_TENANT_ID, updateMap.get(InspectionRecordObjApiNames.BELONG_TENANT_ID));
            }
        }
        return updateMap;
    }

    protected List<String> getCustomizedFillFieldsApiName(FillExtraField.Context context) {
        return Collections.emptyList();
    }

    protected void fillCustomizedData(Map<String, Map<String, Object>> fillExtraFieldMap, FillExtraField.Context context) {
    }
}
