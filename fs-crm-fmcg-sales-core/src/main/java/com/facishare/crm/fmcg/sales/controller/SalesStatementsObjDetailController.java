package com.facishare.crm.fmcg.sales.controller;

import com.facishare.crm.fmcg.sales.model.statement.SalesStatementsDetail;
import com.facishare.crm.fmcg.sales.service.SalesStatementsService;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class SalesStatementsObjDetailController extends PreDefineController<SalesStatementsDetail.Arg, SalesStatementsDetail.Result> {

    private static final SalesStatementsService salesStatementsService = SpringUtil.getContext().getBean(SalesStatementsService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }

    @Override
    protected SalesStatementsDetail.Result doService(SalesStatementsDetail.Arg arg) {
        ServiceContext serviceContext = new ServiceContext(controllerContext.getRequestContext(), "sales_statements_service", "detail");
        return salesStatementsService.detail(arg, serviceContext);
    }
}
