package com.facishare.crm.fmcg.sales.cache;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.sales.exception.ApiException;
import com.fmcg.framework.http.PaasDescribeProxy;
import com.fmcg.framework.http.contract.paas.describe.PaasDescribeLayout;
import com.fxiaoke.notifier.support.NotifierClient;
import com.google.common.base.Splitter;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> liu<PERSON>yu
 * @create : 2024-12-24 11:36
 **/
@Component
@Slf4j
public class DataLevelI18nFieldSupportLanguagesCache {
    public static final String DESCRIBE_CHANGE_EVENT = "describe-change-event";

    @Resource
    private PaasDescribeProxy paasDescribeProxy;

    private LoadingCache<String, Map<String, List<String>>> cache;

    @PostConstruct
    public void init() {
        cache = CacheBuilder.newBuilder()
                .maximumSize(5000)
                .expireAfterWrite(1, TimeUnit.HOURS)
                .build(new CacheLoader<String, Map<String, List<String>>>() {
                    @Override
                    public Map<String, List<String>> load(String key) {
                        return handleFieldSupportLanguages(key);
                    }
                });

        listenDescribeChangeEventUseFastNotify();
    }

    private Map<String, List<String>> handleFieldSupportLanguages(String key) {
        String[] keys = splitKey(key);
        if (keys.length != 2) {
            throw new ApiException(1000001, "handleFieldSupportLanguages key is error");
        }
        String tenantId = keys[0];
        String apiName = keys[1];

        PaasDescribeLayout.Arg arg = new PaasDescribeLayout.Arg();
        arg.setIncludeDetailDescribe(false);
        arg.setIncludeLayout(false);
        arg.setApiName(apiName);
        arg.setLayoutType("add");
        arg.setRecordTypeApiName("default__c");
        PaasDescribeLayout.Result result = paasDescribeProxy.findDescribeLayoutV1(Integer.parseInt(tenantId), -10000, apiName, arg);
        if (Objects.isNull(result) || Objects.isNull(result.getResult()) || Objects.isNull(result.getResult().getObjectDescribe())) {
            log.info("DataLevelI18nFieldConfigCache findDescribeLayoutV1 no result {}", key);
            throw new ApiException(1000002, "DataLevelI18nFieldConfigCache findDescribeLayoutV1 no result");
        }

        Map<String, List<String>> config = Maps.newHashMap();

        JSONObject objectDescribe = result.getResult().getObjectDescribe();
        JSONObject fields = objectDescribe.getJSONObject("fields");
        for (Map.Entry<String, Object> entry : fields.entrySet()) {
            String fieldApiName = entry.getKey();
            JSONObject fieldAttribute = JSONObject.parseObject(JSON.toJSONString(entry.getValue()));
            if (!Boolean.TRUE.equals(fieldAttribute.getBoolean("enable_multi_lang"))) {
                continue;
            }
            JSONArray supportLanguages = fieldAttribute.getJSONArray("support_languages");
            if (Objects.isNull(supportLanguages)) {
                continue;
            }
            List<String> supportLanguageList = Lists.newArrayList();
            for (Object supportLanguage : supportLanguages) {
                JSONObject object = JSONObject.parseObject(JSON.toJSONString(supportLanguage));
                supportLanguageList.add(object.getString("code"));
            }
            config.put(fieldApiName, supportLanguageList);
        }
        return config;
    }

    public Map<String, List<String>> getNoException(String tenantId, String apiName) {
        try {
            return cache.get(buildKey(tenantId, apiName));
        } catch (Exception e) {
            log.error("DataLevelI18nFieldSupportLanguagesCache getNotException is error " + tenantId + apiName, e);
        }
        return null;
    }

    private void listenDescribeChangeEventUseFastNotify() {
        NotifierClient.register(DESCRIBE_CHANGE_EVENT, (message) -> {
            dealSubMessage(message.getContent());
        });
    }

    void dealSubMessage(String message) {
        Iterator<String> it = Splitter.on('\u0004').split(message).iterator();
        String tenantId = it.next();
        String describeApiName = it.next();
        clear(buildKey(tenantId, describeApiName));
    }

    public void clear(String key) {
        if (containsKey(key)) {
            cache.invalidate(key);
        }
    }

    public boolean containsKey(String key) {
        return cache.asMap().containsKey(key);
    }

    public String buildKey(String tenantId, String describeApiName) {
        return String.format("%s#$%s", tenantId, describeApiName);
    }

    public String[] splitKey(String key) {
        return key.split("#\\$");
    }
}
