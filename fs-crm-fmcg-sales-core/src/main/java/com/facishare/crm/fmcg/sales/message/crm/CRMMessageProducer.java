package com.facishare.crm.fmcg.sales.message.crm;

import com.alibaba.fastjson.JSONObject;
import com.facishare.restful.client.exception.FRestClientException;
import com.fxiaoke.api.CRMNotifyService;
import com.fxiaoke.model.crmNotify.AddRemindRecordArg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * CRM 通知
 */
@Slf4j
@Component
public class CRMMessageProducer {

    @Resource
    private CRMNotifyService crmNotifyService;

    public void sendCRMMessage(AddRemindRecordArg arg) {
        log.info("sendCRMMessage arg={}", JSONObject.toJSONString(arg));
        try {
            crmNotifyService.addRemindRecord(arg);
        } catch (FRestClientException e) {
            StringBuilder sbd = new StringBuilder();
            sbd.append("sendCRMMessage error ").append(e.getMessage());
            try {
                //again
                crmNotifyService.addRemindRecord(arg);
            } catch (FRestClientException againException) {
                sbd.append(", sendCRMMessage error again ").append(againException.getMessage());
            }
            log.error(sbd.toString());
        }
    }
}
