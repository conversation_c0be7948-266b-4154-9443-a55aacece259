package com.facishare.crm.fmcg.sales.model.interconnection;

import com.fxiaoke.api.model.BaseResult;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @create: 2023-11-29 11:26
 **/
public interface UpdatePersonnelPhone {
    @Data
    @ToString
    class Arg {
        private List<String> initPersonnelIds;
        private String tenantId;
    }

    @Data
    @ToString
    class Result extends BaseResult {
    }
}
