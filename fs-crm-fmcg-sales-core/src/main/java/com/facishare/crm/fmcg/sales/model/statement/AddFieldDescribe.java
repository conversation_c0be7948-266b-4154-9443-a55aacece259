package com.facishare.crm.fmcg.sales.model.statement;

import lombok.Data;

import java.util.List;

public interface AddFieldDescribe {
    @Data
    class Arg {
        private List<String> objectApiNames;

        private List<String> tenantIds;
    }

    @Data
    class Result {
        private Integer code;
        private String message;

        public static Result success() {
            Result result = new Result();
            result.setCode(1);
            result.setMessage("success");
            return result;
        }

        public static Result error(String message) {
            Result result = new Result();
            result.setCode(-1);
            result.setMessage(message);
            return result;
        }
    }
}
