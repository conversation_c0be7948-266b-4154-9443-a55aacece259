package com.facishare.crm.fmcg.sales.task.gnomon;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.sales.apiname.FmcgSalesOrderGrayConfig;
import com.fxiaoke.common.release.GrayRelease;
import com.fxiaoke.paas.gnomon.api.NomonProducer;
import com.fxiaoke.paas.gnomon.api.entity.NomonDeleteMessage;
import com.fxiaoke.paas.gnomon.api.entity.NomonMessage;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> liuhaoyu
 * @create : 2024-08-01 11:11
 **/
@Slf4j
@Component
public class GnomonNomonService {
    public static final String ILLEGALLY_GOODS_AUTOMATIC_CONFIRMATION_BIZ = "illegally_goods_automatic_confirmation_biz";

    @Resource
    private NomonProducer nomonProducer;

    public void sendIllegallyGoodsAutomaticConfirmationTask(String tenantId, List<String> inspectionRecordIds) {
        for (String inspectionRecordId : inspectionRecordIds) {
            sendIllegallyGoodsAutomaticConfirmationTask(tenantId, inspectionRecordId);
        }
    }

    public void sendIllegallyGoodsAutomaticConfirmationTask(String tenantId, String inspectionRecordId) {
        if (!GrayRelease.isAllow("fmcg", "ILLEGALLY_GOODS_AUTOMATIC_CONFIRMATION", tenantId)) {
            return;
        }
        if (Strings.isNullOrEmpty(tenantId) || Strings.isNullOrEmpty(inspectionRecordId)) {
            log.info("sendIllegallyGoodsAutomaticConfirmationTask tenantId {}, inspectionRecordId {}", tenantId, inspectionRecordId);
            return;
        }
        JSONObject callArg = new JSONObject();
        callArg.put("tenantId", tenantId);
        callArg.put("dataId", inspectionRecordId);

        long delayTime = ConfigFactory.getConfig(FmcgSalesOrderGrayConfig.CONFIG_GRAY_REF_FMCG).getLong(FmcgSalesOrderGrayConfig.ILLEGALLY_GOODS_AUTOMATIC_CONFIRMATION_DELAY_TIME, 86400000);
        long currentTimeMillis = System.currentTimeMillis();

        NomonMessage nomonMessage = new NomonMessage();
        nomonMessage.setBiz(ILLEGALLY_GOODS_AUTOMATIC_CONFIRMATION_BIZ);
        nomonMessage.setTenantId(tenantId);
        nomonMessage.setDataId(inspectionRecordId);
        nomonMessage.setCallArg(JSON.toJSONString(callArg));
        nomonMessage.setExecuteTime(new Date(currentTimeMillis + delayTime));

        log.info("nomonMessage {}", JSON.toJSONString(nomonMessage));
        nomonProducer.send(nomonMessage);
    }

    public void sendDeleteIllegallyGoodsAutomaticConfirmationTask(String tenantId, String inspectionRecordId) {
        if (!GrayRelease.isAllow("fmcg", "ILLEGALLY_GOODS_AUTOMATIC_CONFIRMATION", tenantId)) {
            return;
        }
        if (Strings.isNullOrEmpty(tenantId) || Strings.isNullOrEmpty(inspectionRecordId)) {
            log.info("sendDeleteIllegallyGoodsAutomaticConfirmationTask tenantId {}, inspectionRecordId {}", tenantId, inspectionRecordId);
            return;
        }
        NomonDeleteMessage nomonDeleteMessage = new NomonDeleteMessage();
        nomonDeleteMessage.setBiz(ILLEGALLY_GOODS_AUTOMATIC_CONFIRMATION_BIZ);
        nomonDeleteMessage.setTenantId(tenantId);
        nomonDeleteMessage.setDataId(inspectionRecordId);

        log.info("nomonDeleteMessage {}", JSON.toJSONString(nomonDeleteMessage));
        nomonProducer.send(nomonDeleteMessage);
    }
}