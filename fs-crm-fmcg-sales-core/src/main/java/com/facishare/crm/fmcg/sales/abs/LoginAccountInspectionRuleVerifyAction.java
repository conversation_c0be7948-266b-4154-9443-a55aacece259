package com.facishare.crm.fmcg.sales.abs;

import com.facishare.crm.fmcg.sales.apiname.InspectionRecordObjApiNames;
import com.facishare.crm.fmcg.sales.business.InspectionBusiness;
import com.facishare.crm.fmcg.sales.enums.MagicEnum;
import com.facishare.crm.fmcg.sales.exception.InspectionVerifyErrorInfo;
import com.facishare.crm.fmcg.sales.exception.InspectionVerifyFailureInterruptException;
import com.facishare.crm.fmcg.sales.model.inspection.InspectionVerify;
import com.facishare.crm.fmcg.sales.utils.I18nUtil;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR> liu<PERSON><PERSON>
 * @create : 2024-07-15 17:09
 **/
@Slf4j
@Component("loginAccountInspectionRuleVerifyAction")
public class LoginAccountInspectionRuleVerifyAction extends AbstractionInspectionRuleVerifyAction {
    @Resource
    private InspectionBusiness inspectionBusiness;

    @Override
    public void verify(InspectionVerify.Context context) {
        super.handleSnBelongData(context);
        IObjectData curAccountInfoInUpstream = inspectionBusiness.findCurLoginAccountInfoInUpstream(context);
        context.getFsStopWatch().lap("findCurLoginAccountInfoInUpstream");
        if (Objects.isNull(curAccountInfoInUpstream)) {
            throw new InspectionVerifyFailureInterruptException(100001, 
                new InspectionVerifyErrorInfo(I18nUtil.get(MagicEnum.upstreamEnterpriseInfoNotFound), null));
        }

        //当前登录客户
        String curLoginAccountId = curAccountInfoInUpstream.getId();
        //判断当前码的归属客户是否是当前登录客户
        String belongAccountId = context.getObjectData().get(InspectionRecordObjApiNames.BELONG_ACCOUNT_ID, String.class);
        if (belongAccountId.equals(curLoginAccountId)) {
            context.getObjectData().set(InspectionRecordObjApiNames.INSPECTION_RESULT, "0");
        } else {
            context.getObjectData().set(InspectionRecordObjApiNames.INSPECTION_RESULT, "1");
            context.getObjectData().set(InspectionRecordObjApiNames.ILLEGALLY_GOODS_ACCOUNT_ID, belongAccountId);
        }
    }
}
