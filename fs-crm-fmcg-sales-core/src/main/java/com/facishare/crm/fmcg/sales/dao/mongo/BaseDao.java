package com.facishare.crm.fmcg.sales.dao.mongo;

import com.github.mongo.support.DatastoreExt;
import com.mongodb.WriteResult;
import org.mongodb.morphia.Key;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.mongodb.morphia.query.UpdateResults;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
public class BaseDao {

    @Resource
    protected DatastoreExt mongoContext;

    public <T> Key<T> save(T entity) {
        return mongoContext.save(entity);
    }


    public <T, V> T get(final Class<T> clazz, final V id) {
        return mongoContext.get(clazz, id);
    }

    public <T, V> WriteResult delete(Class<T> clazz, V id) {
        return mongoContext.delete(clazz, id);
    }

    public <T, V> Query<T> find(Class<T> clazz, String property, V value) {
        return mongoContext.find(clazz, property, value);
    }

    public <T, V> WriteResult delete(Class<T> clazz, Iterable<V> ids) {
        return mongoContext.delete(clazz, ids);
    }

    public <T> UpdateResults update(Query<T> query, UpdateOperations<T> operations) {
        return mongoContext.update(query, operations);
    }
}