package com.facishare.crm.fmcg.sales.business;

import com.facishare.crm.fmcg.sales.apiname.AccountObjApiNames;
import com.facishare.crm.fmcg.sales.apiname.EnterpriseRelationObjApiNames;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.ActionContextUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Component
public class AccountBusiness {
    @Resource
    private ServiceFacade serviceFacade;

    public IObjectData getAccountInfoById(String tenantId, String id, List<String> fields) {
        IFilter eaFilter = new Filter();
        eaFilter.setFieldValues(Lists.newArrayList(id));
        eaFilter.setOperator(Operator.EQ);
        eaFilter.setFieldName("_id");

        SearchTemplateQuery queryTemplate = new SearchTemplateQuery();
        queryTemplate.setLimit(1);
        queryTemplate.setFilters(Lists.newArrayList(eaFilter));

        IActionContext actionContext = ActionContextUtil.getNewContext(tenantId);
        actionContext.setUserId(User.systemUser(tenantId).getUserId());
        actionContext.setPrivilegeCheck(false);
        List<IObjectData> data = serviceFacade.findBySearchTemplateQueryWithFields(
                actionContext,
                AccountObjApiNames.OBJECT_API_NAME,
                queryTemplate,
                fields).getData();
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        return data.get(0);
    }

    public List<IObjectData> getAccountListBySalesArea(String tenantId, List<String> salesAreaByPoints, List<String> fields) {
        IFilter filter = new Filter();
        filter.setFieldValues(salesAreaByPoints);
        filter.setOperator(Operator.HASANYOF);
        filter.setFieldName(AccountObjApiNames.COVERING_SALES_AREAS);

        SearchTemplateQuery queryTemplate = new SearchTemplateQuery();
        queryTemplate.setLimit(10);
        queryTemplate.setFilters(Lists.newArrayList(filter));

        IActionContext actionContext = ActionContextUtil.getNewContext(tenantId);
        actionContext.setUserId(User.systemUser(tenantId).getUserId());
        actionContext.setPrivilegeCheck(false);
        List<IObjectData> data = serviceFacade.findBySearchTemplateQueryWithFields(
                actionContext,
                AccountObjApiNames.OBJECT_API_NAME,
                queryTemplate,
                fields).getData();
        if (CollectionUtils.isEmpty(data)) {
            return Collections.emptyList();
        }
        return data;
    }
}
