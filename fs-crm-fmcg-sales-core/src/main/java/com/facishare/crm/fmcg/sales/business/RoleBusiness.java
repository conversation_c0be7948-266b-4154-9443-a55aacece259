package com.facishare.crm.fmcg.sales.business;

import com.alibaba.fastjson.JSON;
import com.facishare.organization.paas.model.PaaSResult;
import com.facishare.organization.paas.model.permission.QueryRoleUsersByRoles;
import com.facishare.organization.paas.service.PaaSPermissionService;
import com.facishare.organization.paas.util.PaasArgumentUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class RoleBusiness {
    private static final String roleAppId = "CRM";
    @Resource
    private PaaSPermissionService paaSPermissionService;


    /**
     * 获取角色下所有用户
     */
    public List<Integer> queryEmployeeIdsByRoleIds(Integer tenantId, List<String> roleIds, boolean isOnlyMainRole) {
        Map<String, Map<String, Boolean>> roleMap = queryEmployeeRoleMapByRoleIds(tenantId, roleIds);
        if (Objects.isNull(roleMap)) {
            return new ArrayList<>();
        }
        List<Integer> employeeIds = Lists.newArrayList();
        roleMap.values().forEach(roleItem -> {
            if (roleItem != null) {
                roleItem.forEach((key, value) -> {
                    if (isOnlyMainRole) {
                        if (value != null && value) {
                            employeeIds.add(Integer.parseInt(key));
                        }
                    } else {
                        if (value != null) {
                            employeeIds.add(Integer.parseInt(key));
                        }
                    }
                });
            }
        });
        return employeeIds;
    }

    public Map<String, Map<String, Boolean>> queryEmployeeRoleMapByRoleIds(Integer tenantId, List<String> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return null;
        }
        QueryRoleUsersByRoles.Argument argument = PaasArgumentUtil.buildPaaSPermissionArgument(QueryRoleUsersByRoles.Argument.class, tenantId, -10000, roleAppId);
        argument.setRoles(roleIds);
        PaaSResult<Map<String, Map<String, Boolean>>> result = paaSPermissionService.queryRoleUsersByRoles(argument);
        log.info("role result : {}", JSON.toJSONString(result));
        if (Objects.isNull(result) || Objects.isNull(result.getResult())) {
            return null;
        }
        return result.getResult();
    }

}
