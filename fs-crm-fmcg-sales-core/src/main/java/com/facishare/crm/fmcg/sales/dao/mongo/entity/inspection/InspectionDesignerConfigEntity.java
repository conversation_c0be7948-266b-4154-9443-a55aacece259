package com.facishare.crm.fmcg.sales.dao.mongo.entity.inspection;

import lombok.Data;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

@Data
@ToString
@Entity(value = "fmcg_inspection_designer_config", noClassnameStored = true)
public class InspectionDesignerConfigEntity {
    public static final String F_TENANT_ID = "TI";
    public static final String F_CREATE_TIME = "CT";
    public static final String F_INSPECTION_DESIGNER_ID = "IID";
    public static final String F_GROUP_KEY = "GK";
    public static final String F_KEY = "K";
    public static final String F_VALUE = "V";

    @Id
    private ObjectId id;

    @Property(F_TENANT_ID)
    private String tenantId;

    @Property(F_INSPECTION_DESIGNER_ID)
    private String inspectionDesignerId;

    @Property(F_GROUP_KEY)
    private String groupKey;

    @Property(F_KEY)
    private String key;

    @Property(F_VALUE)
    private String value;

    @Property(F_CREATE_TIME)
    private Long createTime;
}
