package com.facishare.crm.fmcg.sales.service;

import com.facishare.crm.fmcg.sales.apiname.CommonApiNames;
import com.facishare.crm.fmcg.sales.apiname.InspectionRecordObjApiNames;
import com.facishare.crm.fmcg.sales.enums.MagicEnum;
import com.facishare.crm.fmcg.sales.exception.ApiException;
import com.facishare.crm.fmcg.sales.exception.BeanFactoryException;
import com.facishare.crm.fmcg.sales.exception.InspectionVerifyErrorInfo;
import com.facishare.crm.fmcg.sales.factory.InspectionRuleVerifyActionFactory;
import com.facishare.crm.fmcg.sales.model.inspection.InspectionRule;
import com.facishare.crm.fmcg.sales.model.inspection.InspectionVerify;
import com.facishare.crm.fmcg.sales.model.inspection.QueryInspectionRule;
import com.facishare.crm.fmcg.sales.utils.I18nUtil;
import com.facishare.organization.paas.model.PaaSResult;
import com.facishare.organization.paas.model.permission.GetMultiEmployeeRoleRelationEntitiesByEmployeeIDs;
import com.facishare.organization.paas.model.permission.RoleListDto;
import com.facishare.organization.paas.model.permission.RoleRelationEntityDto;
import com.facishare.organization.paas.service.PaaSPermissionService;
import com.facishare.organization.paas.util.PaasArgumentUtil;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> liuhaoyu
 * @create : 2024-07-12 16:21
 **/
@Service
@Slf4j
@ServiceModule("inspection")
public class InspectionService {
    @Resource
    protected PaaSPermissionService paaSPermissionService;

    @ServiceMethod("inspection_verify")
    public InspectionVerify.Result inspectionVerify(InspectionVerify.Arg arg, ServiceContext serviceContext) {
        try {
            if (StringUtils.isEmpty(arg.getRuleId())) {
                return InspectionVerify.Result.error(150051, new InspectionVerifyErrorInfo(I18nUtil.get(MagicEnum.ruleIdCannotBeEmpty), null));
            }
            return InspectionRuleVerifyActionFactory.getBeanByRuleId(arg.getRuleId()).action(new InspectionVerify.Context(arg, serviceContext));
        } catch (BeanFactoryException e) {
            return InspectionVerify.Result.error(e.getErrorCode(), new InspectionVerifyErrorInfo(e.getErrorMsg(), null));
        }
    }

    @ServiceMethod("query_inspection_rules")
    public QueryInspectionRule.Result queryInspectionRules(QueryInspectionRule.Arg arg, ServiceContext serviceContext) {
        if (arg.getFindAll()) {
            return QueryInspectionRule.Result.success(InspectionRule.INSPECTION_RULE_LIST);
        }

        if (Objects.isNull(arg.getRuleIds())) {
            return QueryInspectionRule.Result.success(Lists.newArrayList());
        }

        List<InspectionRule> collect = InspectionRule.INSPECTION_RULE_LIST.stream()
                .filter(o -> arg.getRuleIds().contains(o.getRuleId()))
                .collect(Collectors.toList());
        return QueryInspectionRule.Result.success(collect);
    }

    public Boolean isDealer(String tenantId, String upstreamTenantId) {
        if (tenantId == null || upstreamTenantId == null) {
            log.info("isDealer tenantId {} upstreamTenantId {}", tenantId, upstreamTenantId);
            throw new ApiException(100001, "isDealer parameter missing");
        }
        return !tenantId.equals(upstreamTenantId);
    }

    public Map<String, Object> findInspectionPersonnelInfo(InspectionVerify.Context context) {
        Map<String, Object> infoMap = Maps.newHashMap();
        IObjectData curPersonnelInfo = context.getCurPersonnelInfo();
        if (curPersonnelInfo.containsField("find_role_temp")) {
            infoMap.put(InspectionRecordObjApiNames.INSPECTION_PERSONNEL_NAME, curPersonnelInfo.get(CommonApiNames.NAME, String.class));
            infoMap.put(InspectionRecordObjApiNames.INSPECTION_PERSONNEL_ROLE_ID, curPersonnelInfo.get(InspectionRecordObjApiNames.INSPECTION_PERSONNEL_ROLE_ID + "_temp"));
            infoMap.put(InspectionRecordObjApiNames.INSPECTION_PERSONNEL_ROLE, curPersonnelInfo.get(InspectionRecordObjApiNames.INSPECTION_PERSONNEL_ROLE + "_temp"));
            return infoMap;
        } else if (Objects.nonNull(context.getUser().getUserId()) &&
                !Strings.isNullOrEmpty(String.valueOf(context.getUser().getUserId()))) {
            String personId = context.getUser().getUserId();
            infoMap.put(InspectionRecordObjApiNames.INSPECTION_PERSONNEL_NAME, curPersonnelInfo.get(CommonApiNames.NAME, String.class));

            curPersonnelInfo.set("find_role_temp", true);

            GetMultiEmployeeRoleRelationEntitiesByEmployeeIDs.Argument arg = PaasArgumentUtil.
                    buildPaaSPermissionArgument(GetMultiEmployeeRoleRelationEntitiesByEmployeeIDs.Argument.class, Integer.valueOf(context.getTenantId()), -10000, "CRM");
            arg.setUsers(Lists.newArrayList(personId));
            PaaSResult<List<RoleRelationEntityDto>> result = paaSPermissionService.getMultiEmployeeRoleRelationEntitiesByEmployeeIDs(arg);
            if (Objects.isNull(result) || CollectionUtils.isEmpty(result.getResult())) {
                log.info("personnel role not found : {}, {}", context.getTenantId(), personId);
                return infoMap;
            }
            List<RoleRelationEntityDto> collect = result.getResult().stream().filter(f -> Boolean.TRUE.equals(f.getDefaultRole())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                infoMap.put(InspectionRecordObjApiNames.INSPECTION_PERSONNEL_ROLE_ID, collect.get(0).getRoleCode());
            } else {
                infoMap.put(InspectionRecordObjApiNames.INSPECTION_PERSONNEL_ROLE_ID, result.getResult().get(0).getRoleCode());
            }
            curPersonnelInfo.set(InspectionRecordObjApiNames.INSPECTION_PERSONNEL_ROLE_ID + "_temp", infoMap.get(InspectionRecordObjApiNames.INSPECTION_PERSONNEL_ROLE_ID));
            RoleListDto.Argument roleArg = PaasArgumentUtil.buildPaaSPermissionArgument(RoleListDto.Argument.class, Integer.valueOf(context.getTenantId()), -10000, "CRM");
            roleArg.setRoleCode(String.valueOf(infoMap.get(InspectionRecordObjApiNames.INSPECTION_PERSONNEL_ROLE_ID)));
            PaaSResult<RoleListDto.Result> resultPaaSResult = paaSPermissionService.roleList(roleArg);
            if (CollectionUtils.isNotEmpty(resultPaaSResult.getResult().getRoles())) {
                infoMap.put(InspectionRecordObjApiNames.INSPECTION_PERSONNEL_ROLE, resultPaaSResult.getResult().getRoles().get(0).getRoleName());
                curPersonnelInfo.set(InspectionRecordObjApiNames.INSPECTION_PERSONNEL_ROLE + "_temp", infoMap.get(InspectionRecordObjApiNames.INSPECTION_PERSONNEL_ROLE));
            }
        }
        return infoMap;
    }
}
