package com.facishare.crm.fmcg.sales.utils;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.crm.fmcg.sales.apiname.FmcgSalesOrderGrayConfig;
import com.facishare.crm.fmcg.sales.model.sn.CodeScanRule;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> liuhaoyu
 * @create : 2024-07-25 11:11
 **/
@Slf4j
public class FMCGConfigUtil {
    private static Map<String, String> SALES_ACTION_BEAN_NAME_MAP = Maps.newHashMap();

    private static List<CodeScanRule> FMCG_SN_CODE_SCAN_RULES = Lists.newArrayList();

    static {
        ConfigFactory.getConfig(FmcgSalesOrderGrayConfig.CONFIG_GRAY_REF_FMCG, FMCGConfigUtil::handleConfig);
    }

    private static void handleConfig(IConfig config) {
        handleSalesActionBeanNameMap(config);
        handleFMCGSnCodeScanRules(config);
    }

    private static void handleFMCGSnCodeScanRules(IConfig config) {
        try {
            log.info("handleFMCGSnCodeScanRules");
            String rulesJson = config.get(FmcgSalesOrderGrayConfig.FMCG_SN_CODE_SCAN_RULES);
            if (StringUtils.isBlank(rulesJson)) {
                return;
            }
            List<CodeScanRule> rules = JSONObject.parseObject(rulesJson,
                    new TypeReference<List<CodeScanRule>>() {
                    });
            if (CollectionUtils.isEmpty(rules)) {
                return;
            }
            FMCG_SN_CODE_SCAN_RULES = rules;
        } catch (Exception e) {
            log.info("handleFMCGSnCodeScanRules error ", e);
        }
    }

    public static JSONObject getFMCGSnCodeScanRule(Integer tenantId) {
        JSONObject cloudRule = null;
        for (CodeScanRule codeScanRule : FMCG_SN_CODE_SCAN_RULES) {
            String key = codeScanRule.getKey();
            if ("tenant".equals(key) && CollectionUtils.isNotEmpty(codeScanRule.getTenantIds()) && codeScanRule.getTenantIds().contains(tenantId)) {
                return buildFMCGSnCodeScanRule(codeScanRule.getRule(), codeScanRule.getEntryMethod());
            }
            if ("cloud".equals(codeScanRule.getKey())) {
                cloudRule = buildFMCGSnCodeScanRule(codeScanRule.getRule(), codeScanRule.getEntryMethod());
            }
        }
        return Objects.isNull(cloudRule) ? null : cloudRule;
    }

    private static JSONObject buildFMCGSnCodeScanRule(String rule, String entryMethod) {
        if (StringUtils.isEmpty(rule) || StringUtils.isEmpty(entryMethod)) {
            return null;
        }
        JSONObject object = new JSONObject();
        object.put("rule", rule);
        object.put("entryMethod", entryMethod);
        return object;
    }

    private static void handleSalesActionBeanNameMap(IConfig config) {
        String enterpriseToBeanNameJson = config.get(FmcgSalesOrderGrayConfig.SALES_ACTION_BEAN_NAME_MAP);
        if (StringUtils.isBlank(enterpriseToBeanNameJson)) {
            return;
        }
        Map<String, String> enterpriseToBeanNameMap = JSONObject.parseObject(enterpriseToBeanNameJson, new TypeReference<Map<String, String>>() {
        });
        if (Objects.isNull(enterpriseToBeanNameMap)) {
            return;
        }
        Map<String, String> temp = Maps.newHashMap();
        temp.putAll(enterpriseToBeanNameMap);
        SALES_ACTION_BEAN_NAME_MAP = temp;
    }

    public static Map<String, String> getSalesActionBeanNameMap() {
        return SALES_ACTION_BEAN_NAME_MAP;
    }
}
