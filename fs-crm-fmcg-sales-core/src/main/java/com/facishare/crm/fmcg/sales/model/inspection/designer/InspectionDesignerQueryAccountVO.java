package com.facishare.crm.fmcg.sales.model.inspection.designer;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.api.model.BaseResult;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;
import java.util.Map;

public interface InspectionDesignerQueryAccountVO {
    @Data
    @ToString
    class Arg {
        private String inspectionDesignerId;
        private String position;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @ToString
    class Result extends BaseResult {

        private Boolean isDealer;
        private List<AccountInfo> accountInfoList;

        public static Result success(Boolean isDealer, List<AccountInfo> data) {
            Result result = new Result();
            result.setErrorMsg("success");
            result.setErrorCode(0);
            result.setIsDealer(isDealer);
            result.setAccountInfoList(data);
            return result;
        }

        public static Result verifyResult(String msg) {
            Result result = new Result();
            result.setErrorCode(-1);
            result.setErrorMsg(msg);
            return result;
        }

        public static Result error(String msg) {
            Result result = new Result();
            result.setErrorMsg(msg);
            result.setErrorCode(-1);
            return result;
        }

    }


    @Data
    @ToString
    class AccountInfo {
        private String accountId;
        private String accountName;
    }
}
