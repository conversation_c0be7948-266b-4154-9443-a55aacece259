package com.facishare.crm.fmcg.sales.controller;

import com.facishare.crm.fmcg.sales.utils.ButtonUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.ArrayList;

/**
* <AUTHOR> liu<PERSON><PERSON>
* 
* @create : 2025-03-04 11:42
**/
public class InspectionDataAppealRecordObjListHeaderController extends StandardListHeaderController {
    @Override
    protected Result after(Arg arg, Result result) {
        buttonFilter(arg, result);
        return super.after(arg, result);
    }

    public void buttonFilter(Arg arg, Result result) {
        ButtonUtils.deleteOtherThanTheSpecifiedButtonActions((ArrayList) result.getButtons(),
                Sets.newHashSet(), true);

        ButtonUtils.deleteOtherThanTheSpecifiedButtonActions((ArrayList) result.getLayout().get("buttons"),
                Sets.newHashSet(), true);
    }
}
