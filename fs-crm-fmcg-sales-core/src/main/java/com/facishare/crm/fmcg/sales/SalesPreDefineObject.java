package com.facishare.crm.fmcg.sales;

import com.facishare.paas.appframework.core.model.ActionClassInfo;
import com.facishare.paas.appframework.core.model.ControllerClassInfo;
import com.facishare.paas.appframework.core.model.PreDefineObject;
import com.facishare.paas.appframework.core.model.PreDefineObjectRegistry;

/**
 * <AUTHOR>
 */
@SuppressWarnings("all")
public enum SalesPreDefineObject implements PreDefineObject {
    /**
     * 业务员对帐单
     */
    SalesStatementsObj("SalesStatementsObj"),
    /**
     * 商品条码
     */
    FMCGSerialNumberObj("FMCGSerialNumberObj"),
    /**
     * 商品条码状态
     */
    FMCGSerialNumberStatusObj("FMCGSerialNumberStatusObj"),
    /**
     * 组拖比例
     */
    GroupPileRatioObj("GroupPileRatioObj"),
    /**
     * 稽查记录
     */
    InspectionRecordObj("InspectionRecordObj"),
    /**
     * 稽查日志
     */
    InspectionLogObj("InspectionLogObj"),
    /**
     * 稽查数据申诉记录
     */
    InspectionDataAppealRecordObj("InspectionDataAppealRecordObj");

    private final String apiName;
    private static final String PACKAGE_NAME = SalesPreDefineObject.class.getPackage().getName();

    SalesPreDefineObject(String apiName) {
        this.apiName = apiName;
    }

    @Override
    public String getApiName() {
        return apiName;
    }

    @Override
    public String getPackageName() {
        return PACKAGE_NAME;
    }

    @Override
    public ActionClassInfo getDefaultActionClassInfo(String actionCode) {
        String className = PACKAGE_NAME + ".action." + this.name() + actionCode + "Action";
        return new ActionClassInfo(className);
    }

    @Override
    public ControllerClassInfo getControllerClassInfo(String methodName) {
        String className = PACKAGE_NAME + ".controller." + this.name() + methodName + "Controller";
        return new ControllerClassInfo(className);
    }

    public static void init() {
        for (SalesPreDefineObject sales : SalesPreDefineObject.values()) {
            PreDefineObjectRegistry.register(sales);
        }
    }
}
