package com.facishare.crm.fmcg.sales.button.provider;

import com.facishare.crm.fmcg.sales.apiname.ApiNames;
import com.facishare.crm.fmcg.sales.button.AbstractSalesSpecialButtonProvider;
import com.facishare.crm.fmcg.sales.enums.ButtonAction;
import com.facishare.crm.fmcg.sales.utils.ButtonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.github.trace.TraceContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class SalesStatementsObjSpecialButtonProvider extends AbstractSalesSpecialButtonProvider {

    @Resource
    private ServiceFacade serviceFacade;

    @Override
    public String getApiName() {
        return ApiNames.SALES_STATEMENTS_OBJ;
    }

    @Override
    public List<IButton> getSpecialButtons() {
        List<IButton> buttons = super.getSpecialButtons();
        String tenantId = TraceContext.get().getEi();
        List<IUdefButton> buttonList = serviceFacade.findButtonList(User.builder().tenantId(tenantId).userId("-10000").build(), getApiName());
        Set<String> activeButtonApiNames = buttonList.stream().map(IUdefButton::getApiName).collect(Collectors.toSet());
        buttons.add(ButtonUtils.buildButton(ObjectAction.Print_Receipt, activeButtonApiNames.contains(ObjectAction.Print_Receipt.getButtonApiName())));
        buttons.add(ButtonUtils.buildButton(ObjectAction.Conformance_Statements, activeButtonApiNames.contains(ObjectAction.Conformance_Statements.getButtonApiName())));
        buttons.add(ButtonUtils.buildButton(ButtonAction.Close_Statements, activeButtonApiNames.contains(ButtonAction.Close_Statements.getButtonApiName())));
        buttons.add(ButtonUtils.buildButton(ObjectAction.SUBMIT_ACCOUNT, activeButtonApiNames.contains(ObjectAction.SUBMIT_ACCOUNT.getButtonApiName())));
        buttons.add(ButtonUtils.buildButton(ObjectAction.CLOSE_ACCOUNT, activeButtonApiNames.contains(ObjectAction.CLOSE_ACCOUNT.getButtonApiName())));
        buttons.add(ButtonUtils.buildButton(ObjectAction.SETTLE_ACCOUNT, activeButtonApiNames.contains(ObjectAction.SETTLE_ACCOUNT.getButtonApiName())));
        return buttons;
    }
}
