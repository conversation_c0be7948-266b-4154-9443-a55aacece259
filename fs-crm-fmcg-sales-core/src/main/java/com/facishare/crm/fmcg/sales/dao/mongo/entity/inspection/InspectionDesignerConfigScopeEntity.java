package com.facishare.crm.fmcg.sales.dao.mongo.entity.inspection;

import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

@Data
@ToString
@Entity(value = "fmcg_inspection_designer_config_scope", noClassnameStored = true)
public class InspectionDesignerConfigScopeEntity {
    public static final String F_TENANT_ID = "TI";
    public static final String F_MODIFY_TIME = "MT";
    public static final String F_INSPECTION_DESIGNER_ID = "IID";
    public static final String F_USER_TYPE = "UT";


    @Id
    private String userAccount;

    @Property(F_TENANT_ID)
    private String tenantId;

    @Property(F_INSPECTION_DESIGNER_ID)
    private String inspectionDesignerId;
    /**
     * 用户类型 0内部  1外部
     */
    @Property(F_USER_TYPE)
    private Integer userType;

    @Property(F_MODIFY_TIME)
    private Long modifyTime;
}
