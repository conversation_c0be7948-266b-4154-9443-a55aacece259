package com.facishare.crm.fmcg.sales.service.mengniu;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.sales.apiname.AccountObjApiNames;
import com.facishare.crm.fmcg.sales.pool.ThreadPoolUtil;
import com.facishare.crm.fmcg.sales.utils.FileAdapter;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.support.JdbcService;
import com.fmcg.framework.http.MengNiuProxy;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.PaasDescribeProxy;
import com.fmcg.framework.http.contract.mengniu.QueryStoreInfo;
import com.fmcg.framework.http.contract.paas.data.*;
import com.fmcg.framework.http.contract.paas.describe.PaasDescribeGetField;
import com.fxiaoke.common.Pair;
import com.fxiaoke.jdbc.JdbcConnection;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.google.common.util.concurrent.Uninterruptibles;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Callable;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
@ServiceModule("batch_add_store_service")
@Component
public class BatchAddStoreService {
    @Resource
    private MengNiuProxy mengNiuProxy;

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private FileAdapter fileAdapter;

    @Resource
    private PaasDataProxy paasDataProxy;

    @Resource
    private JdbcService jdbcService;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private PaasDescribeProxy paasDescribeProxy;

    @ServiceMethod("delete_account")
    public String deleteAccount(JSONObject arg, ServiceContext serviceContext) {
        new Thread(MonitorTaskWrapper.wrap(() -> {
            try {
                deleteAccount(arg);
            } catch (Exception e) {
                log.error("Error deleteAccount", e);
            }
        })).start();
        return "success";
    }

    public void deleteAccount(JSONObject arg) {
        List<String> tenantIds = arg.getJSONArray("tenantIds").toJavaList(String.class);
        ThreadPoolExecutor pool = new ThreadPoolExecutor(
                5, 5, 60, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(3000),
                new ThreadFactoryBuilder()
                        .setDaemon(true)
                        .setNameFormat("n_check_data-%s")
                        .build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
        for (String tenantId : tenantIds) {
            pool.execute(MonitorTaskWrapper.wrap(() -> {
                try {
                    asyncDeleteAccount(Integer.parseInt(tenantId), arg);
                } catch (Exception e) {
                    log.error("asyncCheckData is error ", e);
                }
            }));
        }
        pool.shutdown();
    }

    public void asyncDeleteAccount(Integer tenantId, JSONObject arg) {
        PaasDataQueryWithFields.FilterDTO recordTypeFilter = new PaasDataQueryWithFields.FilterDTO();
        recordTypeFilter.setFieldName("record_type");
        recordTypeFilter.setOperator("EQ");
        recordTypeFilter.setFieldValues("default__c");

        PaasDataQueryWithFields.FilterDTO createByFilter = new PaasDataQueryWithFields.FilterDTO();
        createByFilter.setFieldName("created_by");
        createByFilter.setOperator("NEQ");
        createByFilter.setFieldValues(Lists.newArrayList("-10000"));

        List<JSONObject> accountData = findAll(tenantId, AccountObjApiNames.OBJECT_API_NAME,
                Lists.newArrayList("_id", "name", "created_by"), Lists.newArrayList(recordTypeFilter, createByFilter));
        log.info("deleteAccount accountData findAll {}, {}", tenantId, JSON.toJSONString(accountData));
        if (CollectionUtils.isEmpty(accountData)) {
            return;
        }

        List<String> accountIds = accountData.stream()
                .map(o -> o.getString("_id"))
                .collect(Collectors.toList());

        PaasDataQueryWithFields.FilterDTO accountIdFilter = new PaasDataQueryWithFields.FilterDTO();
        accountIdFilter.setFieldName("field_n6t3d__c");
        accountIdFilter.setOperator("IN");
        accountIdFilter.setFieldValues(accountIds);

        List<JSONObject> agreementData = findAll(tenantId, "ActivityAgreement__c", Lists.newArrayList("_id", "field_n6t3d__c"),
                Lists.newArrayList(accountIdFilter));

        log.info("deleteAccount agreementData findAll {}, {}", tenantId, JSON.toJSONString(agreementData));
        if (CollectionUtils.isEmpty(agreementData)) {
            return;
        }
        Set<String> hasAgreementAccount = agreementData.stream()
                .map(o -> o.getString("field_n6t3d__c"))
                .collect(Collectors.toSet());
        log.info("deleteAccount hasAgreementAccount {}, {}", tenantId, JSON.toJSONString(hasAgreementAccount));

        List<String> endAccountIds = accountIds.stream()
                .filter(o -> !hasAgreementAccount.contains(o))
                .collect(Collectors.toList());
        log.info("deleteAccount endAccountIds {}, {}", tenantId, endAccountIds);
    }


    @ServiceMethod("batch_update_store")
    public String batchUpdateStore(JSONObject arg, ServiceContext serviceContext) {
        log.info("batchAddStore");
        Integer start = arg.getInteger("start");
        Integer pageNum = arg.getInteger("pageNum");
        Integer pageSize = arg.getInteger("pageSize");
        String aesKey = arg.getString("aesKey");
        new Thread(MonitorTaskWrapper.wrap(() -> {
            try {
                updateAccount(start, pageNum, pageSize, aesKey);
            } catch (Exception e) {
                log.error("Error updating account", e);
            }
        })).start();
        return "success";
    }

    @ServiceMethod("check_data")
    public String checkData(JSONObject arg, ServiceContext serviceContext) {
        new Thread(MonitorTaskWrapper.wrap(() -> {
            try {
                checkData(arg);
            } catch (Exception e) {
                log.error("checkDuplicateData is error ", e);
            }
        })).start();
        return "success";
    }

    public void checkData(JSONObject arg) {
        ThreadPoolExecutor pool = new ThreadPoolExecutor(
                5, 5, 60, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(3000),
                new ThreadFactoryBuilder()
                        .setDaemon(true)
                        .setNameFormat("n_check_data-%s")
                        .build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
        log.info("checkData arg {}", JSON.toJSONString(arg));

        List<String> tenantIds = arg.getJSONArray("tenantIds").toJavaList(String.class);
        List<JSONObject> filters = arg.getJSONArray("filters").toJavaList(JSONObject.class);

        List<PaasDataQueryWithFields.FilterDTO> filterDTOs = Lists.newArrayList();
        for (JSONObject filter : filters) {
            PaasDataQueryWithFields.FilterDTO filterDTO = new PaasDataQueryWithFields.FilterDTO();
            filterDTO.setFieldName(filter.getString("field_name"));
            filterDTO.setOperator(filter.getString("operator"));
            filterDTO.setFieldValues(filter.get("field_values"));
            filterDTOs.add(filterDTO);
        }

        log.info("checkData filterDTOs {}", JSON.toJSONString(filterDTOs));

        for (String tenantId : tenantIds) {
            pool.execute(MonitorTaskWrapper.wrap(() -> {
                try {
                    asyncCheckData(tenantId, arg, filterDTOs);
                } catch (Exception e) {
                    log.error("asyncCheckData is error ", e);
                }
            }));
        }
        pool.shutdown();
    }

    public void asyncCheckData(String tenantId, JSONObject arg, List<PaasDataQueryWithFields.FilterDTO> filterDTOs) {
        String downstreamApiName = arg.getString("downstreamApiName");
        List<String> downstreamFields = arg.getJSONArray("downstreamFields").toJavaList(String.class);
        String upstreamApiName = arg.getString("upstreamApiName");
        List<String> upstreamFields = arg.getJSONArray("upstreamFields").toJavaList(String.class);
        List<String> fieldsType = arg.getJSONArray("fieldsType").toJavaList(String.class);
        String flagField = arg.getString("flagField");
        String flagValue = arg.getString("flagValue");

        String downstreamMasterApiName = arg.getString("downstreamMasterApiName");
        String downstreamAssociatedField = arg.getString("downstreamAssociatedField");

        List<String> tempIds = Lists.newArrayList();

        if (arg.containsKey("downstreamMasterApiName")) {
            List<JSONObject> downstreamMasterData = findAll(Integer.valueOf(tenantId), downstreamMasterApiName, Lists.newArrayList("_id"), filterDTOs);
            log.info("checkDuplicateData findAll {}, {}, {}", tenantId, downstreamMasterApiName, JSON.toJSONString(downstreamMasterData));

            if (CollectionUtils.isEmpty(downstreamMasterData)) {
                return;
            }

            tempIds = downstreamMasterData.stream()
                    .map(o -> o.getString("_id"))
                    .collect(Collectors.toList());
        }

        List<String> differentDataIds = Lists.newArrayList();
        List<JSONObject> downstreamData;
        if (arg.containsKey("downstreamMasterApiName")) {
            PaasDataQueryWithFields.FilterDTO filter = new PaasDataQueryWithFields.FilterDTO();
            filter.setFieldName(downstreamAssociatedField);
            filter.setOperator("IN");
            filter.setFieldValues(tempIds);
            downstreamData = findAll(Integer.valueOf(tenantId), downstreamApiName, downstreamFields, Lists.newArrayList(filter));
        } else {
            downstreamData = findAll(Integer.valueOf(tenantId), downstreamApiName, downstreamFields, filterDTOs);
        }
        log.info("checkDuplicateData findAll {}, {}, {}", tenantId, downstreamApiName, JSON.toJSONString(downstreamData));
        if (CollectionUtils.isEmpty(downstreamData)) {
            return;
        }
        List<String> ids = downstreamData.stream()
                .map(o -> o.getString("_id"))
                .collect(Collectors.toList());

        PaasDataQueryWithFields.FilterDTO filterDTO = new PaasDataQueryWithFields.FilterDTO();
        filterDTO.setFieldName("_id");
        filterDTO.setOperator("IN");
        filterDTO.setFieldValues(ids);

        List<JSONObject> upstreamData = findAll(777421, upstreamApiName, upstreamFields, Lists.newArrayList(filterDTO));
        log.info("checkDuplicateData findAll {}, {}, {}", 777421, upstreamApiName, JSON.toJSONString(upstreamData));
        if (CollectionUtils.isEmpty(upstreamData)) {
            return;
        }
        Map<String, JSONObject> upstreamIdMap = upstreamData.stream()
                .collect(Collectors.toMap(o -> o.getString("_id"), o -> o));

        for (JSONObject downstreamDatum : downstreamData) {
            String id = downstreamDatum.getString("_id");
            if (!upstreamIdMap.containsKey(id)) {
                log.info("checkDuplicateData 777421 not found {}, {}, {}", tenantId, downstreamApiName, id);
                continue;
            }
            JSONObject upstreamDatum = upstreamIdMap.get(id);
            for (int i = 0; i < downstreamFields.size(); i++) {
                String dField = downstreamFields.get(i);
                String uField = upstreamFields.get(i);
                String type = fieldsType.get(i);
                if (Objects.nonNull(downstreamDatum.get(dField)) && Objects.nonNull(upstreamDatum.get(uField))) {
                    if ("String".equals(type)) {
                        String dString = downstreamDatum.getString(dField);
                        String uString = upstreamDatum.getString(uField);
                        if (!dString.equals(uString)) {
                            differentDataIds.add(id);
                            break;
                        }
                    } else if ("number".equals(type)) {
                        BigDecimal dDecimal = BigDecimal.valueOf(downstreamDatum.getDouble(dField));
                        BigDecimal uDecimal = BigDecimal.valueOf(upstreamDatum.getDouble(uField));
                        if (!dDecimal.equals(uDecimal)) {
                            differentDataIds.add(id);
                            break;
                        }
                    }
                } else if (Objects.isNull(downstreamDatum.get(dField)) && Objects.isNull(upstreamDatum.get(uField))) {
                } else {
                    differentDataIds.add(id);
                    break;
                }
            }
        }
        if (CollectionUtils.isNotEmpty(differentDataIds)) {
            log.info("checkDuplicateData tenantId {}, differentDataIds {}", tenantId, JSON.toJSONString(differentDataIds));
            if (arg.containsKey("flagField")) {
                flagData(upstreamApiName, 777421, differentDataIds, flagField, flagValue);
            }
        }
    }

    public void flagData(String apiName, Integer tenantId, List<String> ids, String flagField, String flagValue) {
        PaasDataBatchIncrementUpdate.Arg arg = new PaasDataBatchIncrementUpdate.Arg();
        for (String id : ids) {
            JSONObject object = new JSONObject();
            object.put("_id", id);
            object.put(flagField, flagValue);
            arg.add(object);
        }
        log.info("flagData arg {}", JSON.toJSONString(arg));
        PaasDataBatchIncrementUpdate.Result result = paasDataProxy.batchIncrementUpdate(tenantId, -10000, apiName, arg);
        log.info("flagData result {}", JSON.toJSONString(result));
    }

    public List<JSONObject> findAll(Integer tenantId, String apiName, List<String> fields, List<PaasDataQueryWithFields.FilterDTO> filters) {
        int limit = 1000;
        int total = 0;
        int offset = 0;
        List<JSONObject> resultList = Lists.newArrayList();
        PaasDataQueryWithFields.Result result = find(tenantId, limit, offset, apiName, fields, filters);
        if (result.getErrCode() != 0 || CollectionUtils.isEmpty(result.getResult().getQueryResult().getDataList())) {
            log.info("findAll {}, tenantId: {}, result, {}", apiName, tenantId, JSON.toJSONString(result));
            return resultList;
        }
        total = result.getResult().getQueryResult().getTotalCount();

        resultList.addAll(result.getResult().getQueryResult().getDataList());
        total -= limit;
        offset += limit;
        while (total > 0) {
            PaasDataQueryWithFields.Result temp = find(tenantId, limit, offset, apiName, fields, filters);
            if (temp.getErrCode() != 0 || CollectionUtils.isEmpty(temp.getResult().getQueryResult().getDataList())) {
                log.info("findAll {}, tenantId: {}, result, {}", apiName, tenantId, JSON.toJSONString(temp));
                return resultList;
            }
            resultList.addAll(temp.getResult().getQueryResult().getDataList());
            total -= limit;
            offset += limit;
        }
        return resultList;
    }

    public PaasDataQueryWithFields.Result find(Integer tenantId, Integer limit, Integer offset, String apiName, List<String> fields, List<PaasDataQueryWithFields.FilterDTO> filters) {
        PaasDataQueryWithFields.Arg arg = new PaasDataQueryWithFields.Arg();
        arg.setFieldList(fields);
        PaasDataQueryWithFields.QueryDTO query = new PaasDataQueryWithFields.QueryDTO.Builder()
                .appendFilters(filters)
                .needReturnCountNum(true)
                .limit(limit)
                .offset(offset)
                .appendOrder("_id", true)
                .build();
        arg.setQueryString(JSON.toJSONString(query));
        return paasDataProxy.queryWithFields(tenantId, -10000, apiName, arg);
    }


    @ServiceMethod("n_tpm_xy_duplicate_data")
    public String nTpmXyDuplicateData(JSONObject arg, ServiceContext serviceContext) {
        new Thread(MonitorTaskWrapper.wrap(() -> {
            try {
                processingData(arg);
            } catch (Exception e) {
                log.error("nTpmXyDuplicateData is error ", e);
            }
        })).start();
        return "success";
    }

    @ServiceMethod("updateActivityRequestDetails")
    public String updateActivityRequestDetails(JSONObject arg, ServiceContext serviceContext) {
        String month = arg.getString("month");
        String submitStatus = arg.getString("submitStatus");
        String name = arg.getString("name");
        List<JSONObject> activityRequestDetails = findActivityRequestDetails(month, submitStatus, name);
        if (CollectionUtils.isEmpty(activityRequestDetails)) {
            return "success";
        }
        Map<String, List<JSONObject>> detailMap = Maps.newHashMap();
        for (JSONObject activityRequestDetail : activityRequestDetails) {
            String dealerId = activityRequestDetail.getString("dealer_id__c");
            List<JSONObject> temp;
            if (detailMap.containsKey(dealerId)) {
                temp = detailMap.get(dealerId);
            } else {
                temp = Lists.newArrayList();
                detailMap.put(dealerId, temp);
            }
            temp.add(activityRequestDetail);
        }

        List<String> dealerIds = activityRequestDetails.stream()
                .map(o -> o.getString("dealer_id__c"))
                .distinct()
                .collect(Collectors.toList());

        List<JSONObject> enterpriseRelationObjs = findEnterpriseRelationObj(dealerIds);
        if (CollectionUtils.isEmpty(enterpriseRelationObjs)) {
            return "success";
        }
        Map<String, String> collect = enterpriseRelationObjs.stream()
                .collect(Collectors.toMap(o -> o.getString("mapper_account_id"), o -> o.getString("enterprise_account")));

        for (Map.Entry<String, List<JSONObject>> entry : detailMap.entrySet()) {
            String dealerId = entry.getKey();
            if (!collect.containsKey(dealerId)) {
                continue;
            }
            String ea = collect.get(dealerId);

            for (JSONObject object : entry.getValue()) {
                PaasDataFindById.Arg arg1 = new PaasDataFindById.Arg();
                arg1.setDescribeApiName("TPMActivityAgreementObj");
                arg1.setDataId(object.getString("_id"));
                arg1.setSelectFields(Lists.newArrayList("activity_id"));
                log.info("arg1 arg {}", JSON.toJSONString(arg1));
                PaasDataFindById.Result byId1 = paasDataProxy.findById(Integer.valueOf(ea), -10000, arg1);
                log.info("byId1 result {}", JSON.toJSONString(byId1));
                String activityId = byId1.getData().getObjectData().getString("activity_id");

                PaasDataFindById.Arg arg2 = new PaasDataFindById.Arg();
                arg2.setDescribeApiName("TPMActivityObj");
                arg2.setDataId(activityId);
                arg2.setSelectFields(Lists.newArrayList("field_HV815__c"));
                log.info("arg2 arg {}", JSON.toJSONString(arg2));
                PaasDataFindById.Result byId2 = paasDataProxy.findById(Integer.valueOf(ea), -10000, arg2);
                log.info("byId2 result {}", JSON.toJSONString(byId2));
                String fieldHv815__c = byId2.getData().getObjectData().getString("field_HV815__c");

                PaasDataFindById.Arg arg3 = new PaasDataFindById.Arg();
                arg3.setDescribeApiName("TPMBudgetBusinessSubjectObj");
                arg3.setDataId(fieldHv815__c);
                arg3.setSelectFields(Lists.newArrayList("name"));
                log.info("arg3 arg {}", JSON.toJSONString(arg3));
                PaasDataFindById.Result byId3 = paasDataProxy.findById(Integer.valueOf(ea), -10000, arg3);
                log.info("byId3 result {}", JSON.toJSONString(byId3));
                String name1 = byId3.getData().getObjectData().getString("name");

                PaasDataIncrementUpdate.Arg incrementUpdate = new PaasDataIncrementUpdate.Arg();
                JSONObject obj = new JSONObject();
                obj.put("_id", object.getString("_id"));
                obj.put("field_uOq4K__c", name1);
                obj.put("field_qc01s__c", name1);
                incrementUpdate.setData(obj);
                log.info("incrementUpdate arg {}", JSON.toJSONString(incrementUpdate));
                PaasDataIncrementUpdate.Result result = paasDataProxy.incrementUpdate(777421, -10000, "object_Jk6CW__c", incrementUpdate);
                log.info("incrementUpdate result {}", JSON.toJSONString(result));
            }
        }
        return "success";
    }

    public List<JSONObject> findEnterpriseRelationObj(List<String> accountIds) {
        PaasDataQueryWithFields.Arg arg = new PaasDataQueryWithFields.Arg();
        arg.setFieldList(Lists.newArrayList("_id", "name", "enterprise_account", "mapper_account_id"));
        PaasDataQueryWithFields.QueryDTO query = new PaasDataQueryWithFields.QueryDTO.Builder()
                .appendFilter("mapper_account_id", "IN", accountIds)
                .limit(4000)
                .offset(0)
                .build();
        arg.setQueryString(JSON.toJSONString(query));
        log.info("findEnterpriseRelationObj arg {}", JSON.toJSONString(arg));
        PaasDataQueryWithFields.Result result = paasDataProxy.queryWithFields(777421, -10000, "EnterpriseRelationObj", arg);
        log.info("findEnterpriseRelationObj result {}", JSON.toJSONString(result));
        if (result.getErrCode() != 0) {
            log.info("findEnterpriseRelationObj is error {}, {}", result.getErrCode(), result.getErrMessage());
            return Lists.newArrayList();
        }
        return result.getResult().getQueryResult().getDataList();
    }

    public List<JSONObject> findActivityRequestDetails(String month, String submitStatus, String name) {
        PaasDataQueryWithFields.Arg arg = new PaasDataQueryWithFields.Arg();
        arg.setFieldList(Lists.newArrayList("_id", "name", "dealer_id__c"));
        PaasDataQueryWithFields.QueryDTO query = new PaasDataQueryWithFields.QueryDTO.Builder()
                .appendFilter("field_1dCSF__c", "EQ", submitStatus)
                .appendFilter("field_J57e1__c", "EQ", month)
                .appendFilter("field_qc01s__c", "IS", null)
                .limit(4000)
                .offset(0)
                .build();

        if (!Strings.isNullOrEmpty(name)) {
            query = new PaasDataQueryWithFields.QueryDTO.Builder()
                    .appendFilter("field_1dCSF__c", "EQ", submitStatus)
                    .appendFilter("field_J57e1__c", "EQ", month)
                    .appendFilter("field_qc01s__c", "IS", null)
                    .appendFilter("name", "EQ", name)
                    .limit(4000)
                    .offset(0)
                    .build();
        }
        arg.setQueryString(JSON.toJSONString(query));
        log.info("FindActivityRequestDetails arg {}", JSON.toJSONString(arg));
        PaasDataQueryWithFields.Result result = paasDataProxy.queryWithFields(777421, -10000, "object_Jk6CW__c", arg);
        log.info("FindActivityRequestDetails result {}", JSON.toJSONString(result));
        if (result.getErrCode() != 0) {
            log.info("findActivityRequestDetails is error {}, {}", result.getErrCode(), result.getErrMessage());
            return Lists.newArrayList();
        }
        return result.getResult().getQueryResult().getDataList();
    }

    public void processingData(JSONObject arg) {
        List<String> tenantIds = arg.getJSONArray("tenantIds").toJavaList(String.class);
        String deleteData = arg.getString("deleteData");
        ThreadPoolExecutor pool = new ThreadPoolExecutor(
                5, 5, 60, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(3000),
                new ThreadFactoryBuilder()
                        .setDaemon(true)
                        .setNameFormat("n_tpm_xy_duplicate_data-%s")
                        .build(),
                new ThreadPoolExecutor.CallerRunsPolicy());

        PaasDataQueryWithFields.FilterDTO filter1 = new PaasDataQueryWithFields.FilterDTO();
        filter1.setFieldName("begin_date");
        filter1.setOperator("GTE");
        filter1.setFieldValues(arg.get("begin_date"));
        PaasDataQueryWithFields.FilterDTO filter2 = new PaasDataQueryWithFields.FilterDTO();
        filter2.setFieldName("end_date");
        filter2.setOperator("LTE");
        filter2.setFieldValues(arg.get("end_date"));
        List<PaasDataQueryWithFields.FilterDTO> filters = Lists.newArrayList(filter1, filter2);
        log.info("processingData filters : {}", JSON.toJSONString(filters));
        for (String tenantId : tenantIds) {
            log.info("tenantId : {} start processingData", tenantId);
            pool.execute(MonitorTaskWrapper.wrap(() -> {
                try {
                    List<JSONObject> result = findAllTPMActivityAgreementObj(Integer.parseInt(tenantId), filters);
                    log.info("tenantId : {}, result : {}", tenantId, JSON.toJSONString(result));
                    Map<String, List<String>> repeatData = Maps.newHashMap();
                    List<String> fieldIsNullData = Lists.newArrayList();
                    for (JSONObject object : result) {
                        if (Objects.isNull(object.get("store_id")) ||
                                Objects.isNull(object.get("field_K6Y5h__c")) ||
                                Objects.isNull(object.get("begin_date")) ||
                                Objects.isNull(object.get("end_date"))) {
                            fieldIsNullData.add(object.getString("_id"));
                            continue;
                        }
                        String key = String.format("%s-%s-%s-%s", object.getString("store_id"), object.getString("field_K6Y5h__c"),
                                object.getString("begin_date"), object.getString("end_date"));
                        List<String> temp;
                        if (repeatData.containsKey(key)) {
                            temp = repeatData.get(key);
                        } else {
                            temp = Lists.newArrayList();
                            repeatData.put(key, temp);
                        }
                        temp.add(object.getString("_id"));
                    }
                    Map<String, List<String>> endRepeatData = Maps.newHashMap();
                    for (Map.Entry<String, List<String>> entry : repeatData.entrySet()) {
                        if (entry.getValue().size() > 1) {
                            endRepeatData.put(entry.getKey(), entry.getValue());
                        }
                    }
                    log.info("tenantId : {}, endRepeatData : {}", tenantId, JSON.toJSONString(endRepeatData));
                    log.info("tenantId : {}, fieldIsNullData : {}", tenantId, JSON.toJSONString(fieldIsNullData));
                    if ("1".equals(deleteData)) {
                        deleteData(Integer.parseInt(tenantId), endRepeatData, result);
                    }
                } catch (Exception e) {
                    log.error("handle tpm xy error", e);
                }
            }));
        }
        pool.shutdown();
    }

    public void deleteData(Integer tenantId, Map<String, List<String>> endRepeatData, List<JSONObject> tpmActivityAgreementData) {
        if (endRepeatData.isEmpty()) {
            return;
        }
        Map<String, String> idToActivityIdMap = tpmActivityAgreementData.stream()
                .collect(Collectors.toMap(o -> o.getString("_id"), o -> o.getString("activity_id")));
        //查询活动申请
        Set<String> activityIds = Sets.newHashSet();
        for (Map.Entry<String, List<String>> entry : endRepeatData.entrySet()) {
            for (String s : entry.getValue()) {
                if (idToActivityIdMap.containsKey(s)) {
                    activityIds.add(idToActivityIdMap.get(s));
                }
            }
        }
        log.info("tenantId : {}, activityIds : {}", tenantId, JSON.toJSONString(activityIds));
        PaasDataQueryWithFields.Arg arg = new PaasDataQueryWithFields.Arg();
        //field_CBoh9__c 提交状态 1未提交 ， field_k7dqw__c 协议门店数
        arg.setFieldList(Lists.newArrayList("_id", "name", "field_CBoh9__c", "field_k7dqw__c"));
        PaasDataQueryWithFields.QueryDTO query = new PaasDataQueryWithFields.QueryDTO.Builder()
                .appendFilter("_id", "IN", activityIds)
                .needReturnCountNum(true)
                .limit(2000)
                .offset(0)
                .appendOrder("_id", true)
                .build();
        arg.setQueryString(JSON.toJSONString(query));
        PaasDataQueryWithFields.Result result = paasDataProxy.queryWithFields(tenantId, -10000, "TPMActivityObj", arg);
        log.info("tenantId : {}, find TPMActivityObj result: {}", tenantId, JSON.toJSONString(result));
        if (result.getErrCode() != 0 || CollectionUtils.isEmpty(result.getResult().getQueryResult().getDataList())) {
            return;
        }
        List<String> wrongNameTpmActivityIds = Lists.newArrayList();
        Map<String, JSONObject> tpmActivityIdMap = Maps.newHashMap();
        for (JSONObject object : result.getResult().getQueryResult().getDataList()) {
            tpmActivityIdMap.put(object.getString("_id"), object);
            if (Objects.isNull(object.get("name"))) {
                continue;
            }
            String name = object.getString("name");
            if (name.contains("_null") || name.contains("__")) {
                wrongNameTpmActivityIds.add(object.getString("_id"));
            }
        }

        List<String> wrongTpmActivityIds = Lists.newArrayList();
        for (Map.Entry<String, List<String>> entry : endRepeatData.entrySet()) {
            List<String> value = entry.getValue();
            if (value.size() < 2) {
                continue;
            }
            String xyId0 = value.get(0);
            String xyId1 = value.get(1);
            if (!idToActivityIdMap.containsKey(xyId0) || !idToActivityIdMap.containsKey(xyId1)) {
                continue;
            }
            String sqId0 = idToActivityIdMap.get(xyId0);
            String sqId1 = idToActivityIdMap.get(xyId1);
            if (wrongNameTpmActivityIds.contains(sqId0) || wrongNameTpmActivityIds.contains(sqId1)) {
                continue;
            }
            if (sqId0.equals(sqId1)) {
                log.info("tenantId : {} same tpmActivity {}, {}, {}", tenantId, sqId0, xyId0, xyId1);
                continue;
            }
            if (!tpmActivityIdMap.containsKey(sqId0) || !tpmActivityIdMap.containsKey(sqId1)) {
                continue;
            }
            JSONObject sq0 = tpmActivityIdMap.get(sqId0);
            JSONObject sq1 = tpmActivityIdMap.get(sqId1);

            String sq0Submit = sq0.getString("field_CBoh9__c");
            String sq1Submit = sq1.getString("field_CBoh9__c");
            if ("1".equals(sq0Submit) && !"1".equals(sq1Submit)) {
                wrongTpmActivityIds.add(sqId0);
            } else if (!"1".equals(sq0Submit) && "1".equals(sq1Submit)) {
                wrongTpmActivityIds.add(sqId1);
            } else if ("1".equals(sq0Submit) && "1".equals(sq1Submit)) {
                if (Objects.nonNull(sq0.get("field_k7dqw__c")) && Objects.nonNull(sq1.get("field_k7dqw__c"))) {
                    Integer sq0Num = sq0.getInteger("field_k7dqw__c");
                    Integer sq1Num = sq1.getInteger("field_k7dqw__c");
                    if (sq0Num > sq1Num) {
                        wrongTpmActivityIds.add(sqId1);
                    } else {
                        wrongTpmActivityIds.add(sqId0);
                    }
                }
            }
        }
        wrongTpmActivityIds.addAll(wrongNameTpmActivityIds);
        List<String> wrongTpmActivityIdsCollect = wrongTpmActivityIds.stream()
                .distinct()
                .collect(Collectors.toList());
        log.info("tenantId : {}, wrongTpmActivityIdsCollect {}", tenantId, JSON.toJSONString(wrongTpmActivityIdsCollect));
        deleteData(tenantId, wrongTpmActivityIdsCollect);
    }

    public void deleteData(Integer tenantId, List<String> wrongTpmActivityIds) {
        PaasDataQueryWithFields.FilterDTO filter = new PaasDataQueryWithFields.FilterDTO();
        filter.setFieldName("activity_id");
        filter.setOperator("IN");
        filter.setFieldValues(wrongTpmActivityIds);
        List<JSONObject> allTPMActivityAgreementObj = findAllTPMActivityAgreementObj(tenantId, Lists.newArrayList(filter));
        log.info("tenantId : {}, allTPMActivityAgreementObj : {}", tenantId, JSON.toJSONString(allTPMActivityAgreementObj));
        if (CollectionUtils.isEmpty(allTPMActivityAgreementObj)) {
            return;
        }
        List<String> wrongTpmAgreementIds = Lists.newArrayList();
        for (JSONObject object : allTPMActivityAgreementObj) {
            wrongTpmAgreementIds.add(object.getString("_id"));
        }
        log.info("tenantId : {}, wrongTpmAgreementIds {}", tenantId, JSON.toJSONString(wrongTpmAgreementIds));

        for (List<String> ids : Lists.partition(wrongTpmAgreementIds, 100)) {
            log.info("tenantId : {}, wrongTpmAgreementIds ids {}", tenantId, JSON.toJSONString(ids));
            List<IObjectData> objectDataList = Lists.newArrayList();
            for (String id : ids) {
                IObjectData objectData = new ObjectData();
                objectData.setId(id);
                objectData.setDescribeApiName("TPMActivityAgreementObj");
                objectData.setTenantId(String.valueOf(tenantId));
                objectDataList.add(objectData);
            }
            List<IObjectData> result = serviceFacade.bulkDeleteDirect(objectDataList, User.systemUser(String.valueOf(tenantId)));
            log.info("deleteData deleteTpmAgreementResult {}", JSON.toJSONString(result));
        }

        for (List<String> ids : Lists.partition(wrongTpmActivityIds, 100)) {
            log.info("tenantId : {}, wrongTpmActivityIds ids {}", tenantId, JSON.toJSONString(ids));
            List<IObjectData> objectDataList = Lists.newArrayList();
            for (String id : ids) {
                IObjectData objectData = new ObjectData();
                objectData.setId(id);
                objectData.setDescribeApiName("TPMActivityObj");
                objectData.setTenantId(String.valueOf(tenantId));
                objectDataList.add(objectData);
            }
            List<IObjectData> result = serviceFacade.bulkDeleteDirect(objectDataList, User.systemUser(String.valueOf(tenantId)));
            log.info("deleteData deleteTpmActivityResult {}", JSON.toJSONString(result));
        }
    }

    public List<JSONObject> findAllTPMActivityAgreementObj(Integer tenantId, List<PaasDataQueryWithFields.FilterDTO> filters) {
        int limit = 1000;
        int total = 0;
        int offset = 0;
        List<JSONObject> resultList = Lists.newArrayList();
        PaasDataQueryWithFields.Result result = findTPMActivityAgreementObj(tenantId, limit, offset, filters);
        if (result.getErrCode() != 0 || CollectionUtils.isEmpty(result.getResult().getQueryResult().getDataList())) {
            log.info("finding TPMActivityAgreementObj, tenantId: {}, result, {}", tenantId, JSON.toJSONString(result));
            return resultList;
        }
        total = result.getResult().getQueryResult().getTotalCount();

        resultList.addAll(result.getResult().getQueryResult().getDataList());
        total -= limit;
        offset += limit;
        while (total > 0) {
            PaasDataQueryWithFields.Result temp = findTPMActivityAgreementObj(tenantId, limit, offset, filters);
            if (temp.getErrCode() != 0 || CollectionUtils.isEmpty(temp.getResult().getQueryResult().getDataList())) {
                log.info("finding TPMActivityAgreementObj, tenantId: {}, result, {}", tenantId, JSON.toJSONString(temp));
                return resultList;
            }
            resultList.addAll(temp.getResult().getQueryResult().getDataList());
            total -= limit;
            offset += limit;
        }
        return resultList;
    }

    public PaasDataQueryWithFields.Result findTPMActivityAgreementObj(Integer tenantId, Integer limit, Integer offset, List<PaasDataQueryWithFields.FilterDTO> filters) {
        PaasDataQueryWithFields.Arg arg = new PaasDataQueryWithFields.Arg();
        arg.setFieldList(Lists.newArrayList("_id", "name", "store_id", "field_K6Y5h__c", "begin_date", "end_date", "activity_id"));
        PaasDataQueryWithFields.QueryDTO query = new PaasDataQueryWithFields.QueryDTO.Builder()
                .appendFilters(filters)
                .needReturnCountNum(true)
                .limit(limit)
                .offset(offset)
                .appendOrder("_id", true)
                .build();
        arg.setQueryString(JSON.toJSONString(query));
        return paasDataProxy.queryWithFields(tenantId, -10000, "TPMActivityAgreementObj", arg);
    }


    @ServiceMethod("batch_add_store")
    public String batchAddStore(JSONObject arg, ServiceContext serviceContext) {
        log.info("batchAddStore");
        String tenantId = arg.getString("tenant_id");
        int start = arg.getIntValue("start");
        int maxSize = arg.getIntValue("max_size");
        String clientId = arg.getString("clientId");
        String sk = arg.getString("sk");
        String size = arg.getString("size");
        String bizCode = arg.getString("bizCode");

        new Thread(MonitorTaskWrapper.wrap(() -> {
            try {
                queryStoreInfo(tenantId, start, size, maxSize, clientId, sk, bizCode);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        })).start();

        return "success";
    }

    private void updateAccount(Integer offset, Integer pageNum, Integer pageSize, String aesKey) {
        int start = offset;
        for (int i = 0; i < pageNum; i++) {
            //查询客户
            List<JSONObject> accounts = findAccounts(start, pageSize);
            log.info("accounts : {}", JSON.toJSONString(accounts));
            if (CollectionUtils.isEmpty(accounts)) {
                return;
            }
            start += pageSize;
            //过滤
            List<JSONObject> collect = accounts.stream()
                    .filter(o -> o.getInteger("is_deleted") == 0)
                    .filter(o -> "default__c".equals(o.getString("record_type")))
                    .filter(o -> Objects.isNull(o.get("door_photo")))
                    .collect(Collectors.toList());
            log.info("collect size : {}", collect.size());
            if (CollectionUtils.isEmpty(collect)) {
                continue;
            }
            List<List<JSONObject>> partitions = Lists.partition(collect, 100);
            for (List<JSONObject> partition : partitions) {
                //查询客户中间对象
                List<JSONObject> midAccounts = findMidAccounts(partition);
                if (CollectionUtils.isEmpty(midAccounts)) {
                    continue;
                }
                log.info("midAccounts : {}", JSON.toJSONString(midAccounts));
                ThreadPoolUtil.execute(MonitorTaskWrapper.wrap(() -> {
                    try {
                        //数据转换
                        handleMidAccounts(midAccounts, aesKey);
                        log.info("handleMidAccounts : {}", JSON.toJSONString(midAccounts));
                        //客户编码:客户id
                        Map<String, String> accountNoToId = accounts.stream()
                                .filter(o -> o.containsKey("account_no__c") && !Strings.isNullOrEmpty(o.getString("account_no__c")))
                                .collect(Collectors.toMap(o -> o.getString("account_no__c"), o -> o.getString("_id"), (a, b) -> a));
                        //更新数据
                        PaasDataBatchIncrementUpdate.Arg arg = new PaasDataBatchIncrementUpdate.Arg();
                        for (JSONObject midAccount : midAccounts) {
                            String storeCode = midAccount.getString("storeCode__c");
                            String accountId = accountNoToId.get(storeCode);
                            JSONObject object = new JSONObject();
                            object.put("_id", accountId);
                            object.put("field_ezP1j__c", midAccount.get("field_ezP1j__c"));
                            object.put("door_photo", midAccount.get("door_photo"));
                            object.put("field_2QnR5__c", midAccount.get("field_2QnR5__c"));
                            arg.add(object);
                        }
                        PaasDataBatchIncrementUpdate.Result result = paasDataProxy.batchIncrementUpdate(777421, -10000, AccountObjApiNames.OBJECT_API_NAME, arg);
                        if (result.getCode() != 0) {
                            log.info("Error updating account {}, {}", result.getCode(), result.getMessage());
                        }
                    } catch (Exception e) {
                        log.error("Failed to update account", e);
                    }
                }));
            }
        }
        //查询客户 com.facishare.crm.fmcg.sales.service.mengniu.BatchAddStoreService.readObjectData
        //图片类型转换 com.facishare.crm.fmcg.sales.utils.FileAdapter.uploadPicture
        //身份证解密转换
        //1. 身份证加密方式：AES
        //2.临时解密秘钥：504d7fa8adb11b1bde963584851fa56b
        //更新客户 batchUpdate如果效率高可以不用（ com.facishare.crm.fmcg.sales.pool.ThreadPoolUtils）
    }

    public void handleMidAccounts(List<JSONObject> midAccounts, String aesKey) {
        if (CollectionUtils.isEmpty(midAccounts)) {
            return;
        }
        for (JSONObject midAccount : midAccounts) {
            midAccount.put("field_ezP1j__c", aesDecode(aesKey, midAccount.getString("idCardNumber__c")));
            midAccount.put("door_photo", buildPath(midAccount.getString("facadeImage__c")));
            midAccount.put("field_2QnR5__c", buildPath(midAccount.getString("licensePic__c")));
        }
    }

    private String aesDecode(String key, String source) {
        if (Strings.isNullOrEmpty(source)) {
            return null;
        }
        try {
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(), "AES");
            cipher.init(Cipher.DECRYPT_MODE, keySpec);
            byte[] encrypted = Base64.getDecoder().decode(source);
            byte[] decrypted = cipher.doFinal(encrypted);
            return new String(decrypted);
        } catch (Exception e) {
            log.error("aesDecode is error", e);
        }
        return null;
    }

    @ServiceMethod("batch_update_account")
    public String batchUpdateAccount(JSONObject arg, ServiceContext serviceContext) {
        log.info("batchUpdateAccount");


        return "success";
    }


    private void queryStoreInfo(String tenantId, int start, String size, int maxSize, String clientId, String sk, String bizCode) throws InterruptedException {
        Long time = new Date().getTime();
//        String clientId = "QDRHPT";
//        String sk = "34829EDA-D347-4FDE-A835-4CB00C08DC5B";
//        String clientId = "bf68d8a3-4763-40aa-a8d8-a34763e0aace";
//        String sk = "D7ABC695-6EB3-4902-ABC6-956EB3790228";
        String md5 = DigestUtils.md5Hex(clientId + sk + time).toUpperCase();
        QueryStoreInfo.Arg arg = new QueryStoreInfo.Arg();
        QueryStoreInfo.MessageHeader messageHeader = new QueryStoreInfo.MessageHeader();
        messageHeader.setReceiver("cts");
        messageHeader.setSender("openApi");
        messageHeader.setMessageId(UUID.randomUUID().toString());
        messageHeader.setInterfacePath("/api/queryStoreInfoForPageV2");
        messageHeader.setSendTime(new Date().toString());
        QueryStoreInfo.MessageData messageData = new QueryStoreInfo.MessageData();

        messageData.setSize(size);
        messageData.setBusinessType("1");
        messageData.setBizStoreStatus(1);
        messageData.setBizCode(bizCode);
        arg.setMessageHeader(messageHeader);
        arg.setData(messageData);

        for (int i = start; i <= maxSize; i++) {
            messageData.setPage(String.valueOf(i));
            log.info("query args: {}", JSON.toJSONString(arg));
            QueryStoreInfo.Result result = mengNiuProxy.queryStoreInfo(clientId, md5, time.toString(), arg);
            if (!result.getSuccess()) {
                log.info("query account error : {}", i);
            }
            if (result.getTotal() > 0) {
                log.info("query time : {},total: {}", i, result.getTotal());
                batchAddStore(tenantId, bizCode, result.getResult());
            }
            log.info("addAccount number: {} success", i);
            Thread.sleep(500);
        }


    }

    private void batchAddStore(String tenantId, String bizCode, List<JSONObject> dataList) {
        List<JSONObject> createList = Lists.newArrayList();
        for (JSONObject objectData : dataList) {
            JSONObject data = new JSONObject();
            data.put("bizStoreCode__c", objectData.getString("bizStoreCode"));
            data.put("businessType__c", objectData.getString("businessType"));
            data.put("storeCode__c", objectData.getString("storeCode"));
            data.put("storeName__c", objectData.getString("storeName"));
            data.put("bizCode__c", bizCode);
            data.put("longitude__c", objectData.getString("longitude"));
            data.put("latitude__c", objectData.getString("latitude"));
            data.put("province__c", objectData.getString("province"));
            data.put("city__c", objectData.getString("city"));
            data.put("district__c", objectData.getString("district"));
            data.put("town__c", objectData.getString("town"));
            data.put("village__c", objectData.getString("village"));
            data.put("provinceCode__c", objectData.getString("provinceCode"));
            data.put("cityCode__c", objectData.getString("cityCode"));
            data.put("districtCode__c", objectData.getString("districtCode"));
            data.put("townCode__c", objectData.getString("townCode"));
            data.put("villageCode__c", objectData.getString("villageCode"));
            data.put("storeArea__c", objectData.getString("storeArea"));
            data.put("facadeImage__c", objectData.getString("facadeImage"));
            data.put("storeAddress__c", objectData.getString("storeAddress"));
            data.put("storeStatus__c", objectData.getString("storeStatus"));
            data.put("bizStoreStatus__c", objectData.getString("bizStoreStatus"));
            data.put("poiAuthStatus__c", objectData.getString("poiAuthStatus"));
            data.put("licenseAuthStatus__c", objectData.getString("licenseAuthStatus"));
            data.put("authLevel__c", objectData.getString("authLevel"));
            data.put("storeAuditStatus__c", objectData.getString("storeAuditStatus"));
            data.put("createTime__c", objectData.getString("createTime"));
            data.put("modifyTime__c", objectData.getString("modifyTime"));
            data.put("employeeCode__c", objectData.getString("employeeCode"));
            data.put("userName__c", objectData.getString("userName"));
            data.put("userPhone__c", objectData.getString("userPhone"));
            data.put("idCardNumber__c", objectData.getString("idCardNumber"));
            data.put("userWxOpenId__c", objectData.getString("userWxOpenId"));
            data.put("userWxUnionId__c", objectData.getString("userWxUnionId"));
            data.put("userStatus__c", objectData.getString("userStatus"));
            data.put("wxAuthStatus__c", objectData.getString("wxAuthStatus"));
            data.put("identityAuthStatus__c", objectData.getString("identityAuthStatus"));
            data.put("userCreateTime__c", objectData.getString("userCreateTime"));
            data.put("userModifyTime__c", objectData.getString("userModifyTime"));
            data.put("registerNo__c", objectData.getString("registerNo"));
            data.put("legalPerson__c", objectData.getString("legalPerson"));
            data.put("companyName__c", objectData.getString("companyName"));
            data.put("licensePic__c", objectData.getString("licensePic"));
            data.put("regionType__c", objectData.getString("regionType"));
            data.put("storeType__c", objectData.getString("storeType"));
            data.put("storeLevel__c", objectData.getString("storeLevel"));
            data.put("storeDistribution__c", objectData.getString("storeDistribution"));
            data.put("cityLevel__c", objectData.getString("cityLevel"));
            data.put("channelEnterpriseCode__c", objectData.getString("channelEnterpriseCode"));
            data.put("channelEnterpriseName__c", objectData.getString("channelEnterpriseName"));
            data.put("eRPNo__c", objectData.getString("eRPNo"));
            data.put("channel__c", objectData.getString("channel"));
            data.put("employeeInfos__c", objectData.getString("employeeInfos"));
            data.put("marketInfos__c", objectData.getString("marketInfos"));
            data.put("features__c", objectData.getString("features"));
            data.put("outerStoreCode__c", objectData.getString("outerStoreCode"));
            data.put("linkManName__c", objectData.getString("linkManName"));
            data.put("linkManPhone__c", objectData.getString("linkManPhone"));
            JSONArray employeeList = objectData.getJSONArray("employeeInfos");
            if (CollectionUtils.isNotEmpty(employeeList)) {
                JSONObject employeeInfo = employeeList.getJSONObject(0);
                data.put("employeeName__c", employeeInfo.getString("employeeName"));
                data.put("outEmployeeCode__c", employeeInfo.getString("employeeCode"));
            }
            data.put("owner", Lists.newArrayList("-10000"));

            createList.add(data);
        }

        PaasBatchCreate.Result result = paasDataProxy.batchCreate(Integer.valueOf(tenantId), -10000, "AccountV3__c", createList);
        if (result.getCode() != 0) {
            log.error("create error: {}", result.getMessage());
        }


    }

    private IObjectData buildCreateAccountObjectData(String tenantId, JSONObject object) {
        IObjectData objectData = new ObjectData();
        objectData.setDescribeApiName("AccountObj");
        objectData.setTenantId(tenantId);
        objectData.setRecordType("default__c");
        objectData.setOwner(Lists.newArrayList("1000"));
        objectData.set("name", object.getString("storeName"));
        objectData.set("sale_located__c", object.getOrDefault("storeDistribution", "other"));
        objectData.set("city_level__c", object.getString("cityLevel"));
        //objectData.set("store_type__c",object.getString("storeType"));
        //objectData.set("channel",object.getString("channel"));
        //objectData.set("ka_system__c",object.getString("channelEnterpriseCode"));
        //objectData.set("store_status__c",object.getString(""));
        objectData.set("account_no__c", object.getString("storeCode"));
        //objectData.set("field_2m89p__c",object.getString(""));
        objectData.set("field_ezP1j__c", object.getString("idCardNumber"));
        objectData.set("contacts", object.getString("userName"));
        objectData.set("tel", object.getString("serPhone"));
        //objectData.set("country",object.getString(""));
        //objectData.set("province",object.getString(""));
        //objectData.set("district",object.getString(""));
        //objectData.set("city",object.getString(""));
        //objectData.set("town",object.getString(""));
        objectData.set("address", object.getString("storeAddress"));
        //objectData.set("location",object.getString("storeAddress"));
        //objectData.set("owner",object.getString(""));
        objectData.set("account_level", object.getString("storeLevel"));
        objectData.set("uniform_social_credit_code", object.getString("registerNo"));
        //objectData.set("business_area",object.getString(""));
        objectData.set("store_status", object.getString("storeStatus"));
        //objectData.set("sellers__c",object.getString(""));
        //objectData.set("linked_service_providers__c",object.getString(""));
        objectData.set("record_type", object.getString("default__c"));
        //objectData.set("erp_code__c",object.getString(""));
        objectData.set("check_status__c", object.getString("storeAuditStatus"));
        objectData.set("wechat_status__c", object.getString("wxAuthStatus"));
        objectData.set("cert_level__c", object.getString(""));
        objectData.set("aptitude_status__c", object.getString("licenseAuthStatus"));
        objectData.set("map_certstatus__c", object.getString("poiAuthStatus"));
        //objectData.set("account_type",object.getString(""));
        //objectData.set("sales_area__c",object.getString("bigRegionName"));
        //objectData.set("provinces__c",object.getString("provincialRegionCode"));
        objectData.set("door_photo", buildPath(object.getString("facadeImage")));
        objectData.set("field_2QnR5__c", buildPath(object.getString("licensePic")));

        return objectData;
    }

    private List<JSONObject> buildPath(String url) {
        if (StringUtil.isEmpty(url)) {
            return Lists.newArrayList();
        }
        String ea = "mengniu777";

        String path = fileAdapter.uploadPicture(ea, url);
        JSONObject object = new JSONObject();
        object.put("ext", "png");
        object.put("path", path);
        return Lists.newArrayList(object);
    }

    public List<JSONObject> findAccounts(Integer offset, Integer pageSize) {
        //27:account_no__c
        String sql = "SELECT id, value27, is_deleted,door_photo, record_type FROM biz_account WHERE tenant_id = '777421' order by id OFFSET %d LIMIT %d";
        String formatSql = String.format(sql, offset, pageSize);
        Map<String, Integer> fieldMap = Maps.newHashMap();
        fieldMap.put("_id", 1);
        fieldMap.put("account_no__c", 2);
        fieldMap.put("is_deleted", 3);
        fieldMap.put("door_photo", 4);
        fieldMap.put("record_type", 5);
        log.info("findAccounts sql : {}", formatSql);
        return doCallWithRetries(() -> fetchRangeIdFromDB("777421", formatSql, pageSize, fieldMap));
    }

    public List<JSONObject> findMidAccounts(List<JSONObject> accounts) {
        if (CollectionUtils.isEmpty(accounts)) {
            return null;
        }
        List<String> accountNos = Lists.newArrayList();
        for (JSONObject account : accounts) {
            String accountNo = account.getString("account_no__c");
            if (Strings.isNullOrEmpty(accountNo)) {
                continue;
            }
            accountNos.add(accountNo);
        }
        if (CollectionUtils.isEmpty(accountNos)) {
            return null;
        }
        List<String> collect = accountNos.stream()
                .map(o -> "'" + o + "'")
                .collect(Collectors.toList());
        //22:storeCode__c, 2:idCardNumber__c, 28:facadeImage__c, 30:licensePic__c
        String sql = "SELECT id, value22, value2, value28, value30 FROM account__c WHERE tenant_id = '777421' AND value22 IN (%s)";
        String formatSql = String.format(sql, Joiner.on(",").join(collect));
        Map<String, Integer> fieldMap = Maps.newHashMap();
        fieldMap.put("_id", 1);
        fieldMap.put("storeCode__c", 2);
        fieldMap.put("idCardNumber__c", 3);
        fieldMap.put("facadeImage__c", 4);
        fieldMap.put("licensePic__c", 5);
        log.info("findMidAccounts sql : {}", formatSql);
        return doCallWithRetries(() -> fetchRangeIdFromDB("777421", formatSql, accountNos.size(), fieldMap));
    }

    private <T> T doCallWithRetries(Callable<T> callable) {
        for (int i = 1; i <= 5; i++) {
            try {
                return callable.call();
            } catch (Exception e) {
                log.warn("excute fail,retries: {}/{}, ", i, 5, e);
                Uninterruptibles.sleepUninterruptibly(3, TimeUnit.SECONDS);
            }
        }
        return null;
    }

    private List<JSONObject> fetchRangeIdFromDB(String tenantId, String querySql, int limit, Map<String, Integer> fieldMap) {
        List<JSONObject> accounts = Lists.newArrayListWithCapacity(limit);
        try {
            Pair<JdbcConnection, String> conn = jdbcService.conn(tenantId, querySql);
            conn.first.query(conn.second, rs -> {
                while (rs.next()) {
                    JSONObject o = new JSONObject();
                    for (Map.Entry<String, Integer> entry : fieldMap.entrySet()) {
                        o.put(entry.getKey(), rs.getString(entry.getValue()));
                    }
                    accounts.add(o);
                }
            });
        } catch (Exception e) {
            log.error("query fail, sql={}, ", querySql, e);
        }
        return accounts;
    }

    @ServiceMethod("compareCustomObjField")
    public String compareCustomObjFieldScript(JSONObject arg, ServiceContext serviceContext) {
        log.info("compareCustomObjField arg:{}", arg);
        Thread thread = new Thread(MonitorTaskWrapper.wrap(() -> {
            try {
                compareCustomObjFieldScript(arg);
            } catch (Exception e) {
                log.error("Error compareCustomObjField", e);
            }
        }));
        thread.setDaemon(true);
        thread.start();
        return "success";
    }

    private void compareCustomObjFieldScript(JSONObject arg) {
        List<String> tenantIds = arg.getJSONArray("tenantIds").toJavaList(String.class);
        String objApiName = arg.getString("objApiName");
        String fieldApiName = arg.getString("fieldApiName");
        String mainFieldNum = arg.getString("mainFieldNum");
        log.info("compareCustomObjField tenantIds:{}, objApiName:{}, fieldApiName:{}, mainFieldNum:{}", tenantIds, objApiName, fieldApiName, mainFieldNum);
        // 去重租户ID
        List<String> distinctTenantIds = tenantIds.stream().distinct().collect(Collectors.toList());
        // 符合条件的租户ID集合，field_num == mainFieldNum
        Set<String> matchedTenantIds = Sets.newHashSet();
        // 不同字段编号的租户ID映射
        Map<String, List<String>> otherFieldNumToTenantIdsMap = Maps.newHashMap();
        // 错误码对应的租户ID集合
        Map<Integer, List<String>> errCodeToTenantIdsMap = Maps.newHashMap();
        // 错误码对应的错误信息
        Map<Integer, String> errCodeToMsgMap = Maps.newHashMap();
        // 处理异常的租户信息
        Map<String, String> exceptionTenantMap = Maps.newHashMap();
        for (String tenantId : distinctTenantIds) {
            try {
                PaasDescribeGetField.Arg getDescribeArg = new PaasDescribeGetField.Arg();
                getDescribeArg.setDescribeApiName(objApiName);
                getDescribeArg.setFieldApiName(fieldApiName);
                PaasDescribeGetField.Result customFieldDescribe = paasDescribeProxy.findCustomFieldDescribe(Integer.valueOf(tenantId), -10000, getDescribeArg);
                Integer errCode = customFieldDescribe.getErrCode();
                if (errCode == 0) {
                    JSONObject fieldJson = JSONObject.parseObject(JSON.toJSONString(customFieldDescribe.getResult().getField()));
                    String fieldNum = fieldJson.getString("field_num");
                    if (mainFieldNum.equals(fieldNum)) {
                        matchedTenantIds.add(tenantId);
                    } else {
                        List<String> tenantIdList = otherFieldNumToTenantIdsMap.getOrDefault(fieldNum, new ArrayList<>());
                        tenantIdList.add(tenantId);
                        otherFieldNumToTenantIdsMap.put(fieldNum, tenantIdList);
                    }
                } else {
                    List<String> tenantIdList = errCodeToTenantIdsMap.getOrDefault(errCode, new ArrayList<>());
                    tenantIdList.add(tenantId);
                    errCodeToTenantIdsMap.put(errCode, tenantIdList);
                    if (!errCodeToMsgMap.containsKey(errCode)) {
                        errCodeToMsgMap.put(errCode, customFieldDescribe.getErrMessage());
                    }
                }
            } catch (Exception e) {
                log.error("Error processing tenantId: {}", tenantId, e);
                exceptionTenantMap.put(tenantId, e.getMessage() != null ? e.getMessage() : e.getClass().getName());
            }
        }
        log.info("本批次处理的全部企业数量: {}", distinctTenantIds.size());
        log.info("正确匹配field_num的企业数量: {}", matchedTenantIds.size());
        log.info("不匹配指定field_num的企业: {}", JSON.toJSONString(otherFieldNumToTenantIdsMap));
        log.info("查询描述异常的企业: {}", JSON.toJSONString(errCodeToTenantIdsMap));
        log.info("查询描述异常错误信息: {}", JSON.toJSONString(errCodeToMsgMap));
        log.info("捕获异常的企业及异常信息: {}", JSON.toJSONString(exceptionTenantMap));
    }

}
