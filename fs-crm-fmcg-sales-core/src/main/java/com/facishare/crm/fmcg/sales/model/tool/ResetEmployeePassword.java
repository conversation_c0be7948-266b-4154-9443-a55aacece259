package com.facishare.crm.fmcg.sales.model.tool;

import com.fxiaoke.api.model.BaseResult;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @create: 2024-04-24 16:38
 **/
public interface ResetEmployeePassword {
    @Data
    @ToString
    class Arg implements Serializable {
        private List<InitData> data;
    }

    @Data
    @ToString
    class InitData implements Serializable {
        private Integer tenantId;
        private List<String> personnelIds;
        private String initPassword;
    }

    @Data
    @ToString
    class Result extends BaseResult {
        public static Result success() {
            Result result = new Result();
            result.setErrorMsg("success");
            result.setErrorCode(0);
            return result;
        }
    }
}
