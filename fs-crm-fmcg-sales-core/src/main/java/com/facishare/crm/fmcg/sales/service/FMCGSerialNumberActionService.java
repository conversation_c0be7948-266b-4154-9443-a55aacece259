package com.facishare.crm.fmcg.sales.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.sales.cache.FMCGSerialNumberActionCache;
import com.facishare.crm.fmcg.sales.dao.mongo.FMCGSerialNumberActionDao;
import com.facishare.crm.fmcg.sales.dao.mongo.entity.ConditionEntity;
import com.facishare.crm.fmcg.sales.dao.mongo.entity.ConditionReturnEntity;
import com.facishare.crm.fmcg.sales.dao.mongo.entity.FMCGSerialNumberActionEntity;
import com.facishare.crm.fmcg.sales.dao.mongo.entity.StorageLocationEntity;
import com.facishare.crm.fmcg.sales.model.sn.*;
import com.facishare.crm.fmcg.sales.service.abstraction.IFMCGSerialNumberActionService;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mongodb.WriteResult;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.mongodb.morphia.Key;
import org.mongodb.morphia.query.UpdateResults;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> liuhaoyu
 * @create : 2023-09-19 10:08
 **/
@Service
@Slf4j
@ServiceModule("fmcg_serial_number_action_service")
public class FMCGSerialNumberActionService implements IFMCGSerialNumberActionService {
    @Resource
    private FMCGSerialNumberActionDao fmcgSerialNumberActionDao;
    @Resource
    private FMCGSerialNumberActionCache fmcgSerialNumberActionCache;

    @Override
    @ServiceMethod("query_sn_action")
    public QuerySnAction.Result querySnAction(QuerySnAction.Arg arg, ServiceContext serviceContext) {
        QuerySnAction.Result result = new QuerySnAction.Result();
        List<FMCGSerialNumberActionEntity> actions = fmcgSerialNumberActionDao.findByUniqueId(serviceContext.getTenantId(), arg.getUniqueId());
        result.setErrorCode(0);
        result.setErrorMsg("success");
        result.setActions(actions);
        return result;
    }

    @Override
    @ServiceMethod("insert_mongo_action")
    public InsertMongoAction.Result insertMongoAction(InsertMongoAction.Arg arg, ServiceContext serviceContext) {
        InsertMongoAction.Result result = new InsertMongoAction.Result();
        for (JSONObject o : arg.getData()) {
            FMCGSerialNumberActionEntity fmcgSerialNumberActionEntity = new FMCGSerialNumberActionEntity();
            fmcgSerialNumberActionEntity.setTenantId(arg.getTenantId());
            fmcgSerialNumberActionEntity.setActionName(o.getString("AN"));
            fmcgSerialNumberActionEntity.setActionDescription(o.getString("AD"));
            fmcgSerialNumberActionEntity.setActionStatus(o.getInteger("AS"));
            fmcgSerialNumberActionEntity.setChannelType(JSON.parseArray(o.getString("CNT"), Integer.class));
            fmcgSerialNumberActionEntity.setActionType(o.getInteger("AT"));
            fmcgSerialNumberActionEntity.setAccessType(o.getInteger("AST"));
            fmcgSerialNumberActionEntity.setCodeType(JSON.parseArray(o.getString("CT"), Integer.class));
            fmcgSerialNumberActionEntity.setTriggerObject(o.getString("TO"));
            fmcgSerialNumberActionEntity.setTriggerAction(o.getString("TA"));
            fmcgSerialNumberActionEntity.setUpdateStatus(o.getInteger("US"));
            fmcgSerialNumberActionEntity.setWarehouseField(o.getString("WF"));
            fmcgSerialNumberActionEntity.setConditionPattern(o.getString("CP"));

            if (o.containsKey("C")) {
                List<ConditionEntity> conditionEntityList = Lists.newArrayList();
                JSONArray conditions = o.getJSONArray("C");
                for (Object condition : conditions) {
                    JSONObject c = JSON.parseObject(JSON.toJSONString(condition), JSONObject.class);
                    ConditionEntity conditionEntity = new ConditionEntity();
                    conditionEntity.setFieldName(c.getString("FN"));
                    conditionEntity.setFieldType(c.getString("FT"));
                    conditionEntity.setOpportunity(c.getString("OY"));
                    conditionEntity.setOperator(c.getString("O"));
                    conditionEntity.setFieldValue(c.get("FV"));
                    conditionEntity.setRowNo(c.getInteger("RO"));
                    conditionEntityList.add(conditionEntity);
                }
                fmcgSerialNumberActionEntity.setConditions(conditionEntityList);
            }

            if (o.containsKey("SSL")) {
                List<StorageLocationEntity> snStorageLocationEntityList = Lists.newArrayList();
                JSONArray locations = o.getJSONArray("SSL");
                for (Object location : locations) {
                    JSONObject l = JSON.parseObject(JSON.toJSONString(location), JSONObject.class);
                    StorageLocationEntity snStorageLocationEntity = new StorageLocationEntity();
                    snStorageLocationEntity.setMasterObjectField(l.getString("MOF"));
                    snStorageLocationEntity.setDetailObjectApiName(l.getString("DOAN"));
                    snStorageLocationEntity.setStorageField(l.getString("SF"));
                    snStorageLocationEntity.setProductField(l.getString("PF"));
                    snStorageLocationEntity.setStorageType(l.getString("ST"));
                    snStorageLocationEntityList.add(snStorageLocationEntity);
                }
                fmcgSerialNumberActionEntity.setSnStorageLocations(snStorageLocationEntityList);
            }

            fmcgSerialNumberActionEntity.setAccountField(o.getString("AF"));
            fmcgSerialNumberActionEntity.setUniqueId(o.getString("UI"));
            fmcgSerialNumberActionEntity.setPersonalObjApiName(o.getString("PSO"));
            fmcgSerialNumberActionEntity.setDesignatedPersonalObjApiNameField(o.getString("DPO"));
            fmcgSerialNumberActionEntity.setPersonalField(o.getString("PSF"));
            fmcgSerialNumberActionEntity.setDesignatedTenantIdField(o.getString("DTI"));
            fmcgSerialNumberActionEntity.setBusinessOccurrenceTimeField(o.getString("BOT"));

            if (o.containsKey("FSC")) {
                Map<String, List<ConditionReturnEntity>> map = Maps.newHashMap();
                JSONObject fsc = o.getJSONObject("FSC");
                for (Map.Entry<String, Object> fscEntry : fsc.entrySet()) {
                    String key = fscEntry.getKey();
                    JSONArray value = JSONObject.parseArray(JSON.toJSONString(fscEntry.getValue()));
                    List<ConditionReturnEntity> conditionReturnEntityList = Lists.newArrayList();
                    for (Object object : value) {
                        ConditionReturnEntity conditionReturnEntity = new ConditionReturnEntity();
                        List<ConditionEntity> conditionEntityList = Lists.newArrayList();
                        JSONObject o1 = JSON.parseObject(JSON.toJSONString(object), JSONObject.class);
                        JSONArray conditons = o1.getJSONArray("C");
                        for (Object condition : conditons) {
                            JSONObject c = JSON.parseObject(JSON.toJSONString(condition), JSONObject.class);
                            ConditionEntity conditionEntity = new ConditionEntity();
                            conditionEntity.setFieldName(c.getString("FN"));
                            conditionEntity.setFieldType(c.getString("FT"));
                            conditionEntity.setOpportunity(c.getString("OY"));
                            conditionEntity.setOperator(c.getString("O"));
                            conditionEntity.setFieldValue(c.get("FV"));
                            conditionEntity.setRowNo(c.getInteger("RO"));
                            conditionEntityList.add(conditionEntity);
                        }

                        conditionReturnEntity.setConditionPattern(o1.getString("CP"));
                        conditionReturnEntity.setEntityFieldName(o1.getString("EFN"));
                        conditionReturnEntity.setReturnValue(o1.getString("RV"));
                        conditionReturnEntity.setReturnValueType(o1.getString("RVT"));
                        conditionReturnEntity.setConditions(conditionEntityList);
                        conditionReturnEntityList.add(conditionReturnEntity);
                    }
                    map.put(key, conditionReturnEntityList);
                }
                if (!map.isEmpty()) {
                    fmcgSerialNumberActionEntity.setFieldStorageConditions(map);
                }
            }

            if (o.containsKey("ASL")) {
                List<StorageLocationEntity> snStorageLocationEntityList = Lists.newArrayList();
                JSONArray locations = o.getJSONArray("ASL");
                for (Object location : locations) {
                    JSONObject l = JSON.parseObject(JSON.toJSONString(location), JSONObject.class);
                    StorageLocationEntity snStorageLocationEntity = new StorageLocationEntity();
                    snStorageLocationEntity.setMasterObjectField(l.getString("MOF"));
                    snStorageLocationEntity.setDetailObjectApiName(l.getString("DOAN"));
                    snStorageLocationEntity.setStorageField(l.getString("SF"));
                    snStorageLocationEntity.setStorageType(l.getString("ST"));
                    snStorageLocationEntityList.add(snStorageLocationEntity);
                }
                fmcgSerialNumberActionEntity.setSnStorageLocations(snStorageLocationEntityList);
            }

            log.info("fmcgSerialNumberActionEntity : {}", JSON.toJSONString(fmcgSerialNumberActionEntity));
            Key<FMCGSerialNumberActionEntity> saveData = fmcgSerialNumberActionDao.save(fmcgSerialNumberActionEntity);
            log.info("saveData : {}", JSON.toJSONString(saveData));
        }
        result.setErrorCode(0);
        result.setErrorMsg("success");
        return result;
    }

    @Override
    @ServiceMethod("delete_mongo_action")
    public DeleteMongoAction.Result deleteMongoAction(DeleteMongoAction.Arg arg, ServiceContext serviceContext) {
        DeleteMongoAction.Result result = new DeleteMongoAction.Result();
        result.setErrorCode(0);
        result.setErrorMsg("success");
        if (CollectionUtils.isEmpty(arg.getIds())) {
            return result;
        }
        List<ObjectId> ids = arg.getIds().stream()
                .map(ObjectId::new)
                .collect(Collectors.toList());
        WriteResult deleteResult = fmcgSerialNumberActionDao.delete(FMCGSerialNumberActionEntity.class, ids);
        log.info("deleteResult : {}", JSON.toJSONString(deleteResult));
        return result;
    }

    @Override
    @ServiceMethod("update_mongo_action")
    public UpdateMongoAction.Result updateMongoAction(UpdateMongoAction.Arg arg, ServiceContext serviceContext) {
        UpdateMongoAction.Result result = new UpdateMongoAction.Result();
        result.setErrorCode(0);
        result.setErrorMsg("success");
        for (JSONObject o : arg.getData()) {
            UpdateResults updateResults = fmcgSerialNumberActionDao.updateAction(o);
            log.info("updateResults : {}", JSON.toJSONString(updateResults));
        }
        return result;
    }

    @Override
    @ServiceMethod("clear_action_cache")
    public ClearActionCache.Result clearActionCache(ClearActionCache.Arg arg, ServiceContext serviceContext) {
        ClearActionCache.Result result = new ClearActionCache.Result();
        result.setErrorCode(0);
        result.setErrorMsg("success");
        String tenantId = arg.getTenantId();
        if (Strings.isNullOrEmpty(tenantId)) {
            return result;
        }
        try {
            log.info("before clear {}", JSON.toJSONString(fmcgSerialNumberActionCache.getNoException(tenantId)));
            fmcgSerialNumberActionCache.clear(tenantId);
            log.info("after clear {}", JSON.toJSONString(fmcgSerialNumberActionCache.getNoException(tenantId)));
        } catch (Exception e) {
            log.error("clear is error : ", e);
        }
        return result;
    }
}
