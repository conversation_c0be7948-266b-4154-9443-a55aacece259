package com.facishare.crm.fmcg.sales.dao.mongo.entity;

import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @create : 2023-09-05 18:19
 **/
@Entity(value = "fmcg_sales_number_action", noClassnameStored = true)
@Data
@ToString
public class FMCGSerialNumberActionEntity {
    public static final String F_TENANT_ID = "TI";
    public static final String F_ACTION_NAME = "AN";
    public static final String F_ACTION_DESCRIPTION = "AD";
    public static final String F_ACTION_STATUS = "AS";
    public static final String F_CHANNEL_TYPE = "CNT";
    public static final String F_ACTION_TYPE = "AT";
    public static final String F_ACCESS_TYPE = "AST";
    public static final String F_CODE_TYPE = "CT";
    public static final String F_TRIGGER_OBJECT = "TO";
    public static final String F_TRIGGER_ACTION = "TA";
    public static final String F_UPDATE_STATUS = "US";
    public static final String F_WAREHOUSE_FIELD = "WF";
    public static final String F_CONDITION_PATTERN = "CP";
    public static final String F_CONDITIONS = "C";
    public static final String F_SN_STORAGE_LOCATIONS = "SSL";
    public static final String F_ACCOUNT_FIELD = "AF";
    public static final String F_UNIQUE_ID = "UI";
    public static final String F_PERSONAL_OBJ_API_NAME = "PSO";
    public static final String F_DESIGNATED_PERSONAL_OBJ_API_NAME_FIELD = "DPO";
    public static final String F_PERSONAL_FIELD = "PSF";
    public static final String F_DESIGNATED_TENANT_ID_FIELD = "DTI";
    public static final String F_BUSINESS_OCCURRENCE_TIME_FIELD = "BOT";
    public static final String F_FIELD_STORAGE_CONDITIONS = "FSC";
    public static final String F_ABNORMAL_STORAGE_LOCATIONS = "ASL";

    @Id
    private String id;
    @Property(F_TENANT_ID)
    private String tenantId;
    @Property(F_ACTION_NAME)
    private String actionName;
    @Property(F_ACTION_DESCRIPTION)
    private String actionDescription;
    /**
     * 状态 - 0：不启用，1：启用
     */
    @Property(F_ACTION_STATUS)
    private Integer actionStatus;
    /**
     * 渠道类型 - 0：经销商，1：分销商，2：门店，3：消费者，4：分子公司，5：店仓
     */
    @Property(F_CHANNEL_TYPE)
    private List<Integer> channelType;
    /**
     * 动作类型 - 0：物流，1：库房作业，2：码营销
     */
    @Property(F_ACTION_TYPE)
    private Integer actionType;
    /**
     * 出入类型 - 0：入，1：出，2：收，3：红，4：调，5：盘，6: 退
     */
    @Property(F_ACCESS_TYPE)
    private Integer accessType;
    /**
     * 码类型 - 0：提外码，1：提内码，2：箱外码，3：箱内码
     */
    @Property(F_CODE_TYPE)
    private List<Integer> codeType;
    @Property(F_TRIGGER_OBJECT)
    private String triggerObject;
    /**
     * 触发动作 - i：新建，u：更新
     */
    @Property(F_TRIGGER_ACTION)
    private String triggerAction;
    /**
     * 码状态更新为 - 0：签收入库，1：销售出库，2：退货入库，3：换货入库，4：换货出库，5：扫码入库，6：门店签收，7：已领奖，8：消费者领优惠券，9：调拨入库，10：调拨出库，11：门店库存盘点，12：门店盘点核销，13: 产品激励已解锁，14: 盘点入库，15: 盘点出库，16：采购退货出库，17: 退货出库，18: 退货入库
     */
    @Property(F_UPDATE_STATUS)
    private Integer updateStatus;
    @Property(F_WAREHOUSE_FIELD)
    private String warehouseField;
    /**
     * (0 and 1) or (2)
     */
    @Property(F_CONDITION_PATTERN)
    private String conditionPattern;
    @Embedded(F_CONDITIONS)
    private List<ConditionEntity> conditions;
    @Embedded(F_SN_STORAGE_LOCATIONS)
    private List<StorageLocationEntity> snStorageLocations;
    @Property(F_ACCOUNT_FIELD)
    private String accountField;
    @Property(F_UNIQUE_ID)
    private String uniqueId;
    @Property(F_PERSONAL_OBJ_API_NAME)
    private String personalObjApiName;
    @Property(F_DESIGNATED_PERSONAL_OBJ_API_NAME_FIELD)
    private String designatedPersonalObjApiNameField;
    @Property(F_PERSONAL_FIELD)
    private String personalField;
    /**
     * 指定租户id字段，当前动作业务真实发生企业不一定是当前企业
     */
    @Property(F_DESIGNATED_TENANT_ID_FIELD)
    private String designatedTenantIdField;
    /**
     * 业务时间发生时间字段，默认取create_time，目前指定字段都应该是日期时间类型字段
     */
    @Property(F_BUSINESS_OCCURRENCE_TIME_FIELD)
    private String businessOccurrenceTimeField;
    /**
     * 根据条件动态的指定某个字段存储的值
     */
    @Embedded(F_FIELD_STORAGE_CONDITIONS)
    private Map<String, List<ConditionReturnEntity>> fieldStorageConditions;
    @Embedded(F_ABNORMAL_STORAGE_LOCATIONS)
    private List<StorageLocationEntity> abnormalStorageLocations;
}

