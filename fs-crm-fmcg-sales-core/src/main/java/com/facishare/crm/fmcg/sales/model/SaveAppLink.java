package com.facishare.crm.fmcg.sales.model;

import com.facishare.appserver.checkins.api.model.common.AppHomeLinkType;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

public interface SaveAppLink {

    @Data
    class Arg implements Serializable {
        protected String bizId;
        protected AppHomeLinkType appHomeLinkType;
        protected String ea;
    }

    @Data
    @ToString
    class Result implements Serializable {
        /**
         * 错误消息
         */
        protected String message;
        /**
         * 0 是成功
         */
        protected int errorCode;
        /**
         * 上游企业账号
         */
        protected String upperEa;
    }
}
