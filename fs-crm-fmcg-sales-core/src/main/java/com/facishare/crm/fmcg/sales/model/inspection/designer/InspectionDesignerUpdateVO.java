package com.facishare.crm.fmcg.sales.model.inspection.designer;

import com.beust.jcommander.internal.Lists;
import com.fxiaoke.api.model.BaseResult;
import lombok.*;

import java.util.List;

public interface InspectionDesignerUpdateVO {
    @Data
    @ToString
    class Arg {
        private List<Integer> effectiveUserIds = Lists.newArrayList();
        private List<Integer> unEffectiveUserIds = Lists.newArrayList();
        private String inspectionDesignerId;
        private List<UpdateEntity> updateEntities = Lists.newArrayList();
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @ToString
    class Result extends BaseResult {

        public static Result success() {
            Result result = new Result();
            result.setErrorMsg("success");
            result.setErrorCode(0);
            return result;
        }

        public static Result error(String msg) {
            Result result = new Result();
            result.setErrorMsg(msg);
            result.setErrorCode(400);
            return result;
        }
    }

    @Data
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    class UpdateEntity {
        private String groupKey;
        private String key;
        private Object value;
    }
}
