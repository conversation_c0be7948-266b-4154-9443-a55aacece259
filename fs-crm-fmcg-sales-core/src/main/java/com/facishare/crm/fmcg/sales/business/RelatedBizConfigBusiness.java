package com.facishare.crm.fmcg.sales.business;


import com.fmcg.framework.http.RelatedBizConfigProxy;
import com.fmcg.framework.http.contract.bizconfig.AccountsReceivableConfig;
import com.github.jedis.support.MergeJedisCmd;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class RelatedBizConfigBusiness {
    @Resource
    private RelatedBizConfigProxy relatedBizConfigProxy;

    @Resource
    private MergeJedisCmd redisSupport;


    public boolean isEnableAccountsReceivable(int tenantId) {
        String key = "sso_enableAccountsReceivable_" + tenantId;
        String cacheValue = redisSupport.get(key);
        if (StringUtils.isNotBlank(cacheValue)) {
            return Boolean.parseBoolean(cacheValue);
        }
        boolean isEnable = false;
        AccountsReceivableConfig.Arg arg = new AccountsReceivableConfig.Arg();
        arg.setKeys(com.google.common.collect.Lists.newArrayList("accounts_receivable_status"));
        AccountsReceivableConfig.Result result = relatedBizConfigProxy.queryAccountsReceivableConfigData(tenantId, -10000, arg);
        if (0 == result.getErrCode()) {
            for (AccountsReceivableConfig.Values values : result.getResult().getValues()) {
                if ("accounts_receivable_status".equals(values.getKey()) && 2 == values.getValue()) {
                    isEnable = true;
                    break;
                }
            }
        }
        if (isEnable) {
            redisSupport.setex(key, 86400L, "true");
        } else {
            redisSupport.setex(key, 3600L, "false");
        }
        return isEnable;
    }
}
