package com.facishare.crm.fmcg.sales.abs;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.common.FsStopWatch;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.sales.abs.abstraction.FillExtraFieldAction;
import com.facishare.crm.fmcg.sales.abs.abstraction.HandleFmcgSerialNumberAction;
import com.facishare.crm.fmcg.sales.abs.abstraction.InspectionRuleVerifyAction;
import com.facishare.crm.fmcg.sales.apiname.*;
import com.facishare.crm.fmcg.sales.business.InspectionBusiness;
import com.facishare.crm.fmcg.sales.business.SnBusiness;
import com.facishare.crm.fmcg.sales.enums.InspectionDesignerConfigEnum;
import com.facishare.crm.fmcg.sales.enums.MagicEnum;
import com.facishare.crm.fmcg.sales.exception.*;
import com.facishare.crm.fmcg.sales.factory.EnterpriseActionBeanFactory;
import com.facishare.crm.fmcg.sales.message.crm.CRMMessageService;
import com.facishare.crm.fmcg.sales.model.inspection.FillExtraField;
import com.facishare.crm.fmcg.sales.model.inspection.InspectionVerify;
import com.facishare.crm.fmcg.sales.model.inspection.designer.InspectionDesignerLoadVO;
import com.facishare.crm.fmcg.sales.pool.InspectionBusinessPool;
import com.facishare.crm.fmcg.sales.service.InspectionDesignerService;
import com.facishare.crm.fmcg.sales.service.InspectionService;
import com.facishare.crm.fmcg.sales.task.gnomon.GnomonNomonService;
import com.facishare.crm.fmcg.sales.utils.CrmFmcgSalesGrayUtil;
import com.facishare.crm.fmcg.sales.utils.DataUtil;
import com.facishare.crm.fmcg.sales.utils.I18nUtil;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fmcg.framework.http.contract.i18nsetting.FindNearestArea;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: liuhaoyu
 * @create: 2024-07-12 19:41
 * - ASYNC_FILL_EXTRA_FIELD灰度，保存数据前直接填充，或，finallyDo异步更新
 * - 所有异常数据都在保存数据时发送通知和任务
 * - 当前数据已保存或已存在稽查记录才记录稽查日志，批量保存数据最后要过滤掉已存在数据（已存在稽查记录会在稽查时就记录稽查日志，不会等批量保存数据）
 **/
@Slf4j
public abstract class AbstractionInspectionRuleVerifyAction implements InspectionRuleVerifyAction<InspectionVerify.Result, InspectionVerify.Context> {
    @Resource
    protected ServiceFacade serviceFacade;
    @Resource
    protected EIEAConverter eieaConverter;
    @Resource
    private CRMMessageService crmMessageService;
    @Resource
    protected GnomonNomonService gnomonNomonService;
    @Resource
    protected InspectionBusiness inspectionBusiness;
    @Resource
    protected InspectionService inspectionService;
    @Resource
    private InspectionDesignerService inspectionDesignerService;
    @Resource
    private SnBusiness snBusiness;

    public InspectionVerify.Result action(InspectionVerify.Context context) {
        InspectionVerify.Result result = null;
        try {
            this.contextFilling(context);
            context.getFsStopWatch().lap("contextFilling");

            this.before(context);
            context.getFsStopWatch().lap("before");

            this.contextObjectFilling(context);
            context.getFsStopWatch().lap("contextObjectFilling");

            this.contextVerify(context);
            context.getFsStopWatch().lap("contextVerify");

            this.whetherExistInspection(context);
            context.getFsStopWatch().lap("whetherExistInspection");

            this.whetherRecheck(context);
            context.getFsStopWatch().lap("whetherRecheck");

            this.verify(context);
            context.getFsStopWatch().lap("verify");

            result = this.resolveResult(context);
            context.getFsStopWatch().lap("resolveResult");
            return result;
        } catch (InspectionVerifyFailureInterruptException e) {
            context.getFsStopWatch().lap("failureInterrupt");
            return interruptResult(e.getErrorCode(), e.getErrorInfo());
        } catch (InspectionVerifyNormalInterruptException e) {
            context.getFsStopWatch().lap("normalInterrupt");
            return interruptResult(context);
        } catch (BeanFactoryException e) {
            return interruptResult(e.getErrorCode(), e.getErrorMsg());
        } catch (ApiException e) {
            return interruptResult(e.getErrCode(), e.getErrMsg());
        } finally {
            this.finallyDo(context);
            context.getFsStopWatch().lap("finallyDo");
            context.getFsStopWatch().stop();
        }
    }

    protected void before(InspectionVerify.Context context) {
        //连扫批量保存数据
        if (whetherContinuousScanBatchSave(context)) {
            //清空数据，没什么用了，避免有数据被使用
            context.setObjectData(new ObjectData());
            //这个页面的数据可能会阻断创建, 放前面
            saveExtraData(context);
            batchSaveInspectionData(context);
            throw new InspectionVerifyNormalInterruptException();
        }
    }

    private void saveExtraData(InspectionVerify.Context context) {
        String extraBusinessObjectApiName = InspectionDesignerConfigEnum.getConfigValue(context.getInspectionDesignerData(), InspectionDesignerConfigEnum.extraBusinessObjectApiName);
        Map<String, IObjectData> extraObjectData = context.getExtraSaveData();
        if (MapUtils.isEmpty(extraObjectData)) {
            return;
        }
        if (!extraObjectData.containsKey(extraBusinessObjectApiName)) {
            return;
        }
        mengNiuExtraData(context);
        IObjectData objectData = extraObjectData.get(extraBusinessObjectApiName);
        try {
            BaseObjectSaveAction.Result result = triggerRemoteAction(context, extraBusinessObjectApiName, objectData);
            DataUtil.putAll(objectData, result.getObjectData());
        } catch (ValidateException e) {
            log.info("Object creation failure, objApiName is {}", extraBusinessObjectApiName);
            throw new InspectionVerifyFailureInterruptException(120000, new InspectionVerifyErrorInfo(e.getMessage(), null));
        }
        context.getFsStopWatch().lap("b-saveExtra");
    }

    private BaseObjectSaveAction.Result triggerRemoteAction(InspectionVerify.Context context, String objApiName, IObjectData objectData) {
        ActionContext actionContext = new ActionContext(RequestContext.builder()
                .tenantId(context.getTenantId())
                .user(context.getUser())
                .build(), objApiName, "Add");

        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
        arg.setObjectData(ObjectDataDocument.of(objectData));

        return serviceFacade.triggerRemoteAction(actionContext, arg, BaseObjectSaveAction.Result.class);
    }

    private Boolean whetherContinuousScanBatchSave(InspectionVerify.Context context) {
        return whetherEnableContinuousScan(context) && CollectionUtils.isNotEmpty(context.getBatchSaveData());
    }

    private Boolean whetherEnableContinuousScan(InspectionVerify.Context context) {
        Map<String, JSONObject> inspectionDesignerData = context.getInspectionDesignerData();
        if (!inspectionDesignerData.containsKey(InspectionDesignerConfigEnum.scanCodeContinuous.getGroupKey())) {
            return false;
        }
        JSONObject scanCodeConfig = inspectionDesignerData.get(InspectionDesignerConfigEnum.scanCodeContinuous.getGroupKey());
        return Objects.nonNull(scanCodeConfig) && scanCodeConfig.getBooleanValue(InspectionDesignerConfigEnum.scanCodeContinuous.getKey());
    }

    private void mengNiuExtraData(InspectionVerify.Context context) {
        if (!CrmFmcgSalesGrayUtil.isMengNiu(context.getTenantId())) {
            return;
        }
        List<IObjectData> batchSaveData = context.getBatchSaveData();
        if (CollectionUtils.isEmpty(batchSaveData)) {
            return;
        }
        Map<String, IObjectData> extraSaveData = context.getExtraSaveData();
        if (!extraSaveData.containsKey("InspectionPhoto__c")) {
            return;
        }
        IObjectData objectData = extraSaveData.get("InspectionPhoto__c");
        IObjectData temp = batchSaveData.get(0);
        objectData.set("batch_code__c", temp.get("auxiliary_batch_code"));
        objectData.set("first_area_city__c", temp.get("auxiliary_first_area_city"));
        objectData.set("first_area_country__c", temp.get("auxiliary_first_area_country"));
        objectData.set("first_area_detail_address__c", temp.get("auxiliary_first_area_detail_address"));
        objectData.set("first_area_district__c", temp.get("auxiliary_first_area_district"));
        objectData.set("first_area_location__c", temp.get("auxiliary_first_area_location"));
        objectData.set("first_area_province__c", temp.get("auxiliary_first_area_province"));
        objectData.set("first_area_town__c", temp.get("auxiliary_first_area_town"));

        List<IObjectData> needRecordData = batchSaveData.stream()
                .filter(o -> StringUtils.isEmpty(o.getId()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(needRecordData)) {
            return;
        }

        List<String> inspectionAccountIds = needRecordData.stream()
                .map(o -> o.get(InspectionRecordObjApiNames.INSPECTION_ACCOUNT_ID, String.class))
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());

        List<String> illegallyAccountIds = needRecordData.stream()
                .map(o -> o.get(InspectionRecordObjApiNames.ILLEGALLY_GOODS_ACCOUNT_ID, String.class))
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(inspectionAccountIds)) {
            objectData.set("inspection_account_id__c", inspectionAccountIds.get(0));
        }

        if (CollectionUtils.isNotEmpty(illegallyAccountIds)) {
            objectData.set("illegally_goods_account_ids__c", JSON.toJSONString(illegallyAccountIds));
        }

    }

    /**
     * - 记录 需要记录的 数据，并且为这些数据做下面的处理
     * - 是否 异步 更新字段，(非异步数据已经在稽查时填充返回给前端,可以直接保存)
     * - 异步 异常通知 定时任务，已存在的数据已经处理过
     * - 异步 记录稽查日志，已存在的数据已经处理过
     */
    private void batchSaveInspectionData(InspectionVerify.Context context) {
        List<IObjectData> batchSaveData = context.getBatchSaveData();
        List<IObjectData> needRecordData = batchSaveData.stream()
                .filter(o -> StringUtils.isEmpty(o.getId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needRecordData)) {
            log.info("All the barcodes have been scanned and stored");
            return;
        }

        List<IObjectData> boxCodesInspectionRecord = Lists.newArrayList();

        for (IObjectData record : needRecordData) {
            List<IObjectData> dataList = handleBoxCodesInspectionRecord(record, context);
            if (CollectionUtils.isNotEmpty(dataList)) {
                boxCodesInspectionRecord.addAll(dataList);
            } else {
                boxCodesInspectionRecord.add(record);
            }
        }

        List<IObjectData> dataList = serviceFacade.bulkSaveObjectData(boxCodesInspectionRecord, context.getUser());
        context.getFsStopWatch().lap("b-batchSaveRecord");

        InspectionBusinessPool.execute(() -> {
            //异步更新数据
            try {
                batchUpdateInspectionData(context, dataList);
            } catch (Exception e) {
                log.error("batchUpdateInspectionData Error", e);
            }
            //记录稽查日志
            try {
                batchRecordInspectionLogData(context, dataList);
            } catch (Exception e) {
                log.error("batchRecordInspectionLogData Error", e);
            }
            //发送异常通知
            try {
                crmMessageService.sendMessageToIllegallyGoodsAccount(context, dataList);
            } catch (Exception e) {
                log.error("batchSaveInspectionData sendMessageToIllegallyGoodsAccount Error", e);
            }
            //发送窜货自动确认任务
            try {
                gnomonNomonService.sendIllegallyGoodsAutomaticConfirmationTask(context.getTenantId(),
                        dataList.stream()
                                .filter(o -> "1".equals(o.get(InspectionRecordObjApiNames.INSPECTION_RESULT, String.class)))
                                .map(DBRecord::getId)
                                .collect(Collectors.toList()));
            } catch (Exception e) {
                log.error("batchSaveInspectionData sendIllegallyGoodsAutomaticConfirmationTask Error", e);
            }
            try {
                if (CrmFmcgSalesGrayUtil.isMengNiu(context.getTenantId())) {
                    List<String> ids = dataList.stream()
                            .map(DBRecord::getId)
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(ids) && context.getExtraSaveData().containsKey("InspectionPhoto__c") &&
                            StringUtils.isNotEmpty(context.getExtraSaveData().get("InspectionPhoto__c").getId())) {
                        Map<String, Object> map = Maps.newHashMap();
                        map.put("inspection_records__c", ids);
                        serviceFacade.updateWithMap(context.getUser(), context.getExtraSaveData().get("InspectionPhoto__c"), map);
                    }
                }
            } catch (Exception e) {
                log.error("update InspectionPhoto__c is error", e);
            }
        });
    }

    private void batchRecordInspectionLogData(InspectionVerify.Context context, List<IObjectData> dataList) {
        List<IObjectData> batchCreate = Lists.newArrayList();
        for (IObjectData objectData : dataList) {
            IObjectData logData = new ObjectData();
            backupCurInspectionLogData(context, logData, objectData, objectData.get(InspectionRecordObjApiNames.INSPECTION_TIME, Long.class));
            preRecordInspectionLog(context, logData, objectData);
            batchCreate.add(logData);
        }
        serviceFacade.bulkSaveObjectData(batchCreate, context.getUser());
    }

    private void batchUpdateInspectionData(InspectionVerify.Context context, List<IObjectData> dataList) {
        if (!GrayRelease.isAllow("fmcg", "ASYNC_FILL_EXTRA_FIELD", context.getTenantId())) {
            return;
        }
        //填充字段
        Map<String, Map<String, Object>> fillExtraFieldMap = fillExtraField(context, dataList);
        List<IObjectData> updateDataList = Lists.newArrayList();
        for (IObjectData objectData : dataList) {
            String snId = objectData.get(InspectionRecordObjApiNames.FMCG_SERIAL_NUMBER_ID, String.class);
            if (!fillExtraFieldMap.containsKey(snId)) {
                log.info("batchUpdateInspectionData fillExtraFieldMap not contains snId {}", snId);
                continue;
            }
            Map<String, Object> fillExtraField = fillExtraFieldMap.get(snId);

            ObjectDataDocument contextDocument = ObjectDataDocument.of(context.getObjectData());
            contextDocument.putAll(fillExtraField);
            updateDataList.add(contextDocument.toObjectData());
        }

        List<String> fillFieldsApiName = EnterpriseActionBeanFactory.resolve(context.getTenantId(),
                new TypeReference<FillExtraFieldAction<FillExtraField.Context>>() {
                }
        ).getFillFieldsApiName(new FillExtraField.Context(context, null));

        serviceFacade.batchUpdateByFields(context.getUser(), updateDataList, fillFieldsApiName);
    }

    protected void contextFilling(InspectionVerify.Context context) {
        String upstreamTenantId = inspectionBusiness.getUpstreamTenantIdByPublicObject(context.getTenantId());
        context.setUpstreamTenantId(upstreamTenantId);
        IObjectData curPersonnelInfo = serviceFacade.findObjectData(User.systemUser(context.getTenantId()), context.getUser().getUserId(), PersonnelObjApiNames.OBJECT_API_NAME);
        context.getFsStopWatch().lap("cf-findPersonnel");
        context.setCurPersonnelInfo(curPersonnelInfo);
        loadInspectionDesigner(context);
    }

    protected void contextObjectFilling(InspectionVerify.Context context) {
        querySnInfo(context);
        conversionLocationToRegionalCode(context, "current", context.getObjectData());
        context.getFsStopWatch().lap("cof-conversionLocation1");
        conversionLocationToRegionalCode(context, "auxiliary_first", context.getObjectData());
        context.getFsStopWatch().lap("cof-conversionLocation2");
        Boolean dealer = inspectionService.isDealer(context.getTenantId(), context.getUpstreamTenantId());
        context.getObjectData().set(InspectionRecordObjApiNames.INSPECTION_PERSONNEL_TYPE, dealer ? 1 : 0);
        context.getObjectData().set(InspectionRecordObjApiNames.INSPECTION_TYPE, 0);
        handleInspectionAccount(context, dealer);
        long currentTimeMillis = System.currentTimeMillis();
        if (Objects.isNull(context.getObjectData().get(InspectionRecordObjApiNames.INSPECTION_TIME))) {
            context.getObjectData().set(InspectionRecordObjApiNames.INSPECTION_TIME, currentTimeMillis);
        }
        context.getObjectData().set(InspectionRecordObjApiNames.INSPECTION_TENANT_ID, context.getTenantId());
        context.getObjectData().set(InspectionRecordObjApiNames.INSPECTION_PERSONNEL_ID, context.getUser().getUserId());
        context.getObjectData().set(InspectionRecordObjApiNames.INSPECTION_PERSONNEL_NAME, context.getUser().getUserName());
        if (CollectionUtils.isEmpty(context.getObjectData().getOwner())) {
            context.getObjectData().setOwner(Lists.newArrayList(context.getUser().getUserId()));
        }
        if (CollectionUtils.isEmpty(context.getObjectData().getDataOwnDepartment())) {
            context.getObjectData().setDataOwnDepartment(context.getCurPersonnelInfo().getDataOwnDepartment());
        }
        backupCurInspectionLogData(context, context.getInspectionLogData(), context.getObjectData(), currentTimeMillis);
        enterpriseCustomFieldHandling(context);
    }

    private void conversionLocationToRegionalCode(InspectionVerify.Context context, String fieldPre, IObjectData objectData) {
        if (Objects.nonNull(context.getObjectData().getId())) {
            return;
        }
        List<String> suffix = Lists.newArrayList("_area_country", "_area_province", "_area_city", "_area_district", "_area_town");
        List<String> address = Lists.newArrayList();
        for (String s : suffix) {
            String value = objectData.get(fieldPre + s, String.class);
            if (StringUtils.isNotEmpty(value) && !StringUtils.isNumeric(value)) {
                address.add(value);
            }
        }
        if (CollectionUtils.isEmpty(address)) {
            return;
        }
        FindNearestArea.ResultBody resultBody = inspectionBusiness.getRegionalCode(Integer.parseInt(context.getTenantId()),
                objectData.get(fieldPre + "_area_location", String.class), address, "gaode");
        if (Objects.isNull(resultBody)) {
            resultBody = new FindNearestArea.ResultBody();
        }
        handleRegionalCode(fieldPre + "_area_country", resultBody.getCity(), objectData);
        handleRegionalCode(fieldPre + "_area_province", resultBody.getProvince(), objectData);
        handleRegionalCode(fieldPre + "_area_city", resultBody.getCity(), objectData);
        handleRegionalCode(fieldPre + "_area_district", resultBody.getDistrict(), objectData);
        handleRegionalCode(fieldPre + "_area_town", resultBody.getTown(), objectData);
    }

    private void handleRegionalCode(String apiName, FindNearestArea.LabelAndValue labelAndValue, IObjectData objectData) {
        if (Objects.nonNull(labelAndValue) && !StringUtils.isNumeric(objectData.get(apiName, String.class))) {
            objectData.set(apiName, labelAndValue.getValue());
        } else {
            objectData.set(apiName, null);
        }
    }

    private void enterpriseCustomFieldHandling(InspectionVerify.Context context) {
        if (CrmFmcgSalesGrayUtil.isMengNiu(context.getTenantId())) {
            context.getObjectData().set("batch_code__c", context.getObjectData().get("auxiliary_batch_code"));
            context.getObjectData().set("deviation_distance__c", context.getObjectData().get("auxiliary_deviation_distance"));
            context.getObjectData().set("first_area_city__c", context.getObjectData().get("auxiliary_first_area_city"));
            context.getObjectData().set("first_area_country__c", context.getObjectData().get("auxiliary_first_area_country"));
            context.getObjectData().set("first_area_detail_address__c", context.getObjectData().get("auxiliary_first_area_detail_address"));
            context.getObjectData().set("first_area_district__c", context.getObjectData().get("auxiliary_first_area_district"));
            context.getObjectData().set("first_area_location__c", context.getObjectData().get("auxiliary_first_area_location"));
            context.getObjectData().set("first_area_province__c", context.getObjectData().get("auxiliary_first_area_province"));
            context.getObjectData().set("first_area_town__c", context.getObjectData().get("auxiliary_first_area_town"));

            BigDecimal configValue = InspectionDesignerConfigEnum.getConfigValue(context.getInspectionDesignerData(), InspectionDesignerConfigEnum.scanCodeAllowableDeviationDistance);
            BigDecimal auxiliaryDeviationDistance = context.getObjectData().get("auxiliary_deviation_distance", BigDecimal.class);
            if (Objects.nonNull(configValue) && Objects.nonNull(auxiliaryDeviationDistance)
                    && auxiliaryDeviationDistance.compareTo(configValue) > 0) {
                context.getObjectData().set("status__c", true);
            }
        }
    }

    private void handleInspectionAccount(InspectionVerify.Context context, Boolean dealer) {
        if (!dealer) {
            return;
        }
        IObjectData curAccountInfoInUpstream = inspectionBusiness.findCurLoginAccountInfoInUpstream(context);
        context.getFsStopWatch().lap("cof-findCurLoginAccountInfo");
        context.getObjectData().set(InspectionRecordObjApiNames.INSPECTION_ACCOUNT_ID, curAccountInfoInUpstream.get(CommonApiNames.ID));
        context.getObjectData().set(InspectionRecordObjApiNames.INSPECTION_ACCOUNT_NAME, curAccountInfoInUpstream.get(CommonApiNames.NAME));
    }

    private void loadInspectionDesigner(InspectionVerify.Context context) {
        InspectionDesignerLoadVO.Arg arg = new InspectionDesignerLoadVO.Arg();
        RequestContext requestContext = RequestContext.builder().tenantId(context.getTenantId()).user(context.getUser()).build();
        ServiceContext serviceContext = new ServiceContext(requestContext, "", "");
        InspectionDesignerLoadVO.Result loadResult = inspectionDesignerService.load(arg, serviceContext);
        if (loadResult.getErrorCode() != 0) {
            log.info("before loadInspectionDesigner {}", JSON.toJSONString(loadResult));
            throw new InspectionVerifyFailureInterruptException(loadResult.getErrorCode(), new InspectionVerifyErrorInfo(loadResult.getErrorMsg(), null));
        }
        context.getFsStopWatch().lap("cf-loadDesigner");
        context.setInspectionDesignerId(loadResult.getInspectionDesignerId());
        context.setInspectionDesignerData(loadResult.getData());
    }

    private void querySnInfo(InspectionVerify.Context context) {
        if (Objects.nonNull(context.getObjectData().get(InspectionRecordObjApiNames.FMCG_SERIAL_NUMBER_ID))) {
            return;
        }
        if (Objects.isNull(context.getSnName())) {
            throw new InspectionVerifyFailureInterruptException(100005, new InspectionVerifyErrorInfo("snName is null", null));
        }
        List<String> fields = Lists.newArrayList(CommonApiNames.ID, FMCGSerialNumberApiNames.DELIVERY_NUMBER, FMCGSerialNumberApiNames.PRODUCT_ID, FMCGSerialNumberApiNames.MANUFACTURE_DATE, FMCGSerialNumberApiNames.BATCH_CODE);
        String codeType = context.getObjectData().get(InspectionRecordObjApiNames.CODE_TYPE_TEMP, String.class);
        IObjectData snByName = null;
        if ("3".equals(codeType)) {
            snByName = inspectionBusiness.findOneSnByName(context.getUpstreamTenantId(), context.getSnName(), CommonApiNames.NAME, fields);
            context.getFsStopWatch().lap("cof-find3Sn");
        } else if ("4".equals(codeType)) {
            List<IObjectData> dataList = inspectionBusiness.findSnByName(context.getUpstreamTenantId(), context.getSnName(), FMCGSerialNumberApiNames.TURNOVER_BOX_CODE, fields);
            context.getFsStopWatch().lap("cof-find4Sn");
            if (CollectionUtils.isNotEmpty(dataList)) {
                context.getObjectData().set(InspectionRecordObjApiNames.BOX_CODE_IDS_TEMP,
                        dataList.stream()
                                .map(IObjectData::getId)
                                .collect(Collectors.toList()));
                snByName = dataList.get(0);
            }
        } else {
            snByName = inspectionBusiness.findOneSnByName(context.getUpstreamTenantId(), context.getSnName(), CommonApiNames.NAME, fields);
            context.getFsStopWatch().lap("cof-find3Sn");
            if (Objects.isNull(snByName)) {
                List<IObjectData> dataList = inspectionBusiness.findSnByName(context.getUpstreamTenantId(), context.getSnName(), FMCGSerialNumberApiNames.TURNOVER_BOX_CODE, fields);
                context.getFsStopWatch().lap("cof-find4Sn");
                if (CollectionUtils.isNotEmpty(dataList)) {
                    context.getObjectData().set(InspectionRecordObjApiNames.BOX_CODE_IDS_TEMP,
                            dataList.stream()
                                    .map(IObjectData::getId)
                                    .collect(Collectors.toList()));
                    snByName = dataList.get(0);
                }
            }
        }
        //本地没找到，再去外部拉一次
        if (Objects.isNull(snByName)) {
            if (CrmFmcgSalesGrayUtil.isMengNiu(context.getTenantId())) {
                List<IObjectData> dataList = handleMengNiuExternalSnCode(context);
                if (CollectionUtils.isNotEmpty(dataList)) {
                    context.getObjectData().set(InspectionRecordObjApiNames.BOX_CODE_IDS_TEMP,
                            dataList.stream()
                                    .map(IObjectData::getId)
                                    .collect(Collectors.toList()));
                    snByName = dataList.get(0);
                    context.setCodeSource("external");
                 }
            }
        }
        if (Objects.isNull(snByName)) {
            throw new InspectionVerifyFailureInterruptException(100001, new InspectionVerifyErrorInfo(I18nUtil.get(MagicEnum.codeNotFound), null));
        }
        context.getObjectData().set(InspectionRecordObjApiNames.FMCG_SERIAL_NUMBER_ID, snByName.getId());
        context.getObjectData().set(FMCGSerialNumberApiNames.DELIVERY_NUMBER, snByName.get(FMCGSerialNumberApiNames.DELIVERY_NUMBER));
        context.getObjectData().set(InspectionRecordObjApiNames.PRODUCT_ID, snByName.get(FMCGSerialNumberApiNames.PRODUCT_ID));
        context.getObjectData().set(InspectionRecordObjApiNames.MANUFACTURE_DATE, snByName.get(FMCGSerialNumberApiNames.MANUFACTURE_DATE));
        context.getObjectData().set(InspectionRecordObjApiNames.BATCH_CODE, snByName.get(FMCGSerialNumberApiNames.BATCH_CODE));
    }

    private List<IObjectData> handleMengNiuExternalSnCode(InspectionVerify.Context context) {
        String sourceCode = context.getSourceCode();
        JSONObject markCodeRelation = snBusiness.queryMarkCodeRelation(sourceCode);
        context.getFsStopWatch().lap("cof-queryMCRelation");
        if (markCodeRelation == null) {
            return null;
        }
        IObjectData productData = snBusiness.getProductFromMarkCodeRelation(markCodeRelation, context);
        context.getFsStopWatch().lap("cof-getProduct");
        if (Objects.isNull(productData)) {
            throw new InspectionVerifyFailureInterruptException(100008, new InspectionVerifyErrorInfo(I18nUtil.get(MagicEnum.productNotFound), null));
        }

        return snBusiness.buildAndCreateNewSnData(markCodeRelation, productData, context);
    }

    private void backupCurInspectionLogData(InspectionVerify.Context context, IObjectData target, IObjectData source, Long currentTimeMillis) {
        target.set(InspectionLogObjApiNames.CURRENT_AREA_COUNTRY, source.get(InspectionRecordObjApiNames.CURRENT_AREA_COUNTRY));
        target.set(InspectionLogObjApiNames.CURRENT_AREA_LOCATION, source.get(InspectionRecordObjApiNames.CURRENT_AREA_LOCATION));
        target.set(InspectionLogObjApiNames.CURRENT_AREA_TOWN, source.get(InspectionRecordObjApiNames.CURRENT_AREA_TOWN));
        target.set(InspectionLogObjApiNames.CURRENT_AREA_DETAIL_ADDRESS, source.get(InspectionRecordObjApiNames.CURRENT_AREA_DETAIL_ADDRESS));
        target.set(InspectionLogObjApiNames.CURRENT_AREA_CITY, source.get(InspectionRecordObjApiNames.CURRENT_AREA_CITY));
        target.set(InspectionLogObjApiNames.CURRENT_AREA_PROVINCE, source.get(InspectionRecordObjApiNames.CURRENT_AREA_PROVINCE));
        target.set(InspectionLogObjApiNames.CURRENT_AREA_DISTRICT, source.get(InspectionRecordObjApiNames.CURRENT_AREA_DISTRICT));

        target.set(InspectionLogObjApiNames.INSPECTION_ACCOUNT_ID, source.get(InspectionRecordObjApiNames.INSPECTION_ACCOUNT_ID));
        target.set(InspectionLogObjApiNames.INSPECTION_ACCOUNT_NAME, source.get(InspectionRecordObjApiNames.INSPECTION_ACCOUNT_NAME));

        target.set(InspectionLogObjApiNames.INSPECTION_TENANT_ID, context.getTenantId());
        target.set(InspectionLogObjApiNames.INSPECTION_PERSONNEL_ID, context.getUser().getUserId());
        target.set(InspectionLogObjApiNames.INSPECTION_TIME, currentTimeMillis);

        target.set(InspectionLogObjApiNames.INSPECTION_PERSONNEL_TYPE, source.get(InspectionRecordObjApiNames.INSPECTION_PERSONNEL_TYPE));

        target.setOwner(Lists.newArrayList(context.getUser().getUserId()));
        target.setDataOwnDepartment(context.getCurPersonnelInfo().getDataOwnDepartment());
    }

    protected abstract void verify(InspectionVerify.Context context);

    protected void contextVerify(InspectionVerify.Context context) {
    }

    /**
     * 校验是否是重复稽查
     */
    protected void whetherExistInspection(InspectionVerify.Context context) {
        String serialNumberId = context.getObjectData().get(InspectionRecordObjApiNames.FMCG_SERIAL_NUMBER_ID, String.class);
        IFilter snIdFilter = new Filter();
        snIdFilter.setFieldValues(Lists.newArrayList(serialNumberId));
        snIdFilter.setOperator(Operator.EQ);
        snIdFilter.setFieldName(InspectionRecordObjApiNames.FMCG_SERIAL_NUMBER_ID);

        SearchTemplateQuery queryTemplate = new SearchTemplateQuery();
        queryTemplate.setLimit(1);
        queryTemplate.setFilters(Lists.newArrayList(snIdFilter));

        List<IObjectData> result = serviceFacade.findBySearchQuery(
                context.getSystemUser(),
                InspectionRecordObjApiNames.OBJECT_API_NAME,
                queryTemplate).getData();
        context.getFsStopWatch().lap("we-findExistRecord");

        if (CollectionUtils.isNotEmpty(result)) {
            DataUtil.putAll(context.getObjectData(), result.get(0));
            context.setExistInspection(true);
            throw new InspectionVerifyNormalInterruptException();
        }
    }

    /**
     * 是否是复核
     */
    protected void whetherRecheck(InspectionVerify.Context context) {
        //开启连扫，默认无复核
        if (whetherEnableContinuousScan(context)) {
            return;
        }
        if (!whetherNeedRecheck(context)) {
            return;
        }
        if (context.getRecheckFlag()) {
            handleAbnormalProcess(context);
            throw new InspectionVerifyNormalInterruptException();
        }
    }

    private void handleAbnormalProcess(InspectionVerify.Context context) {
        saveInspectionData(context);
        List<IObjectData> boxRecordDataList = Lists.newArrayList(context.getObjectData());
        if ("4".equals(context.getObjectData().get(InspectionRecordObjApiNames.CODE_TYPE_TEMP, String.class)) && CollectionUtils.isNotEmpty(boxRecordDataList)) {
            boxRecordDataList = context.getBoxRecordDataList();
        }
        List<IObjectData> finalBoxRecordDataList = boxRecordDataList;
        //发送异常通知
        InspectionBusinessPool.execute(() -> {
            try {
                crmMessageService.sendMessageToIllegallyGoodsAccount(context, finalBoxRecordDataList);
            } catch (Exception e) {
                log.error("whetherRecheck sendMessageToIllegallyGoodsAccount Error", e);
            }
        });
        //发送窜货自动确认任务
        InspectionBusinessPool.execute(() -> {
            try {
                gnomonNomonService.sendIllegallyGoodsAutomaticConfirmationTask(context.getTenantId(), finalBoxRecordDataList.stream()
                        .map(DBRecord::getId).collect(Collectors.toList()));
            } catch (Exception e) {
                log.error("whetherRecheck sendIllegallyGoodsAutomaticConfirmationTask Error", e);
            }
        });
    }

    private Boolean whetherNeedRecheck(InspectionVerify.Context context) {
        Map<String, JSONObject> inspectionDesignerData = context.getInspectionDesignerData();
        if (!inspectionDesignerData.containsKey(InspectionDesignerConfigEnum.reviewEnable.getGroupKey())) {
            return false;
        }
        JSONObject reviewEnable = inspectionDesignerData.get(InspectionDesignerConfigEnum.reviewEnable.getGroupKey());
        return Objects.nonNull(reviewEnable) && reviewEnable.getBooleanValue(InspectionDesignerConfigEnum.reviewEnable.getKey());
    }

    protected InspectionVerify.Result resolveResult(InspectionVerify.Context context) {
        //如果开启连扫，填充完成所有字段，待批量数据保存；开启连扫，默认无复核
        if (whetherEnableContinuousScan(context)) {
            preSaveInspectionData(context);
            throw new InspectionVerifyNormalInterruptException();
        }
        //如果稽查校验异常，并且需要复核(开启复核页面)，那么不保存数据，等待复核提交数据保存，并处理后续; 如果稽查异常，但是不需要复核，则保存数据，并处理后续
        if ("1".equals(context.getObjectData().get(InspectionRecordObjApiNames.INSPECTION_RESULT, String.class))) {
            if (whetherNeedRecheck(context)) {
                return InspectionVerify.Result.success(context);
            }
            handleAbnormalProcess(context);
            return InspectionVerify.Result.success(context);
        }
        saveInspectionData(context);
        return InspectionVerify.Result.success(context);
    }

    protected InspectionVerify.Result interruptResult(Integer errorCode, InspectionVerifyErrorInfo errorInfo) {
        return InspectionVerify.Result.error(errorCode, errorInfo);
    }

    protected InspectionVerify.Result interruptResult(Integer errorCode, String errorMsg) {
        return InspectionVerify.Result.error(errorCode, new InspectionVerifyErrorInfo(errorMsg, null));
    }

    protected InspectionVerify.Result interruptResult(InspectionVerify.Context context) {
        return InspectionVerify.Result.success(context);
    }

    protected void finallyDo(InspectionVerify.Context context) {
        InspectionBusinessPool.execute(() -> {
            if (Objects.isNull(context.getObjectData().getId())) {
                log.info("InspectionRecordObj Not generate");
                return;
            }
            List<IObjectData> boxInspectionRecords = findBoxInspectionRecords(context);
            try {
                if (!context.getExistInspection() && GrayRelease.isAllow("fmcg", "ASYNC_FILL_EXTRA_FIELD", context.getTenantId())) {
                    //填充字段
                    Map<String, Map<String, Object>> fillExtraFieldMap = fillExtraField(context, Lists.newArrayList(context.getObjectData()));
                    String snId = context.getObjectData().get(InspectionRecordObjApiNames.FMCG_SERIAL_NUMBER_ID, String.class);
                    if (!fillExtraFieldMap.containsKey(snId)) {
                        log.info("finallyDo fillExtraFieldMap not contains snId {}", snId);
                        return;
                    }
                    updateInspectionData(context, boxInspectionRecords, fillExtraFieldMap.get(snId));
                }
            } catch (Exception e) {
                log.error("recordInspectionLog Error", e);
            }
            try {
                //记录稽查日志
                recordInspectionLog(context, boxInspectionRecords);
            } catch (Exception e) {
                log.error("recordInspectionLog Error", e);
            }
        });
    }

    private List<IObjectData> findBoxInspectionRecords(InspectionVerify.Context context) {
        if (CollectionUtils.isNotEmpty(context.getBoxRecordDataList())) {
            return context.getBoxRecordDataList();
        }
        List<String> boxCodeIds = getBoxCodeIds(context.getObjectData(), context);
        if (CollectionUtils.isEmpty(boxCodeIds)) {
            return null;
        }

        IFilter snIdFilter = new Filter();
        snIdFilter.setFieldValues(boxCodeIds);
        snIdFilter.setOperator(Operator.IN);
        snIdFilter.setFieldName(InspectionRecordObjApiNames.FMCG_SERIAL_NUMBER_ID);

        return inspectionBusiness.queryWithFields(context.getUpstreamTenantId(), InspectionRecordObjApiNames.OBJECT_API_NAME,
                Lists.newArrayList(snIdFilter),
                Lists.newArrayList(CommonApiNames.ID, InspectionRecordObjApiNames.FMCG_SERIAL_NUMBER_ID, InspectionRecordObjApiNames.PRODUCT_ID, InspectionRecordObjApiNames.PRODUCT_NAME), boxCodeIds.size(), 0);
    }

    private Map<String, Map<String, Object>> fillExtraField(InspectionVerify.Context context, List<IObjectData> objectDataList) {
        return EnterpriseActionBeanFactory.resolve(context.getTenantId(),
                new TypeReference<FillExtraFieldAction<FillExtraField.Context>>() {
                }
        ).action(new FillExtraField.Context(context, objectDataList));
    }

    private void preRecordInspectionLog(InspectionVerify.Context context, IObjectData target, IObjectData source) {
        target.set(CommonApiNames.TENANT_ID, context.getTenantId());
        target.setDescribeApiName(InspectionLogObjApiNames.OBJECT_API_NAME);
        target.setIsPublic(true);
        target.setRecordType("default__c");

        target.set(InspectionLogObjApiNames.INSPECTION_RECORD_ID, source.getId());
        target.set(InspectionLogObjApiNames.INSPECTION_METHODS, "0");
        target.set(InspectionLogObjApiNames.PRODUCT_ID, source.get(InspectionRecordObjApiNames.PRODUCT_ID));
        target.set(InspectionLogObjApiNames.PRODUCT_NAME, source.get(InspectionRecordObjApiNames.PRODUCT_NAME));
        target.set(InspectionLogObjApiNames.FMCG_SERIAL_NUMBER_ID, source.get(InspectionRecordObjApiNames.FMCG_SERIAL_NUMBER_ID));
        Map<String, Object> inspectionPersonnelInfo = inspectionService.findInspectionPersonnelInfo(context);
        target.set(InspectionLogObjApiNames.INSPECTION_PERSONNEL_NAME, inspectionPersonnelInfo.get(InspectionRecordObjApiNames.INSPECTION_PERSONNEL_NAME));
        target.set(InspectionLogObjApiNames.INSPECTION_PERSONNEL_ROLE_ID, inspectionPersonnelInfo.get(InspectionRecordObjApiNames.INSPECTION_PERSONNEL_ROLE_ID));
        target.set(InspectionLogObjApiNames.INSPECTION_PERSONNEL_ROLE, inspectionPersonnelInfo.get(InspectionRecordObjApiNames.INSPECTION_PERSONNEL_ROLE));
    }

    private void recordInspectionLog(InspectionVerify.Context context, List<IObjectData> boxInspectionRecords) {
        if (CollectionUtils.isEmpty(boxInspectionRecords)) {
            preRecordInspectionLog(context, context.getInspectionLogData(), context.getObjectData());
            serviceFacade.saveObjectData(context.getUser(), context.getInspectionLogData());
        } else {
            List<IObjectData> dataList = Lists.newArrayList();
            for (IObjectData boxInspectionRecord : boxInspectionRecords) {
                IObjectData objectData = new ObjectData();
                DataUtil.putAll(objectData, context.getInspectionLogData());
                preRecordInspectionLog(context, objectData, boxInspectionRecord);
                dataList.add(objectData);
            }
            serviceFacade.bulkSaveObjectData(dataList, context.getUser());
        }
    }

    private void saveInspectionData(InspectionVerify.Context context) {
        String snId = context.getObjectData().get(InspectionRecordObjApiNames.FMCG_SERIAL_NUMBER_ID, String.class);
        preSaveInspectionData(context);
        List<IObjectData> boxCodesInspectionRecord = handleBoxCodesInspectionRecord(context.getObjectData(), context);
        if (CollectionUtils.isNotEmpty(boxCodesInspectionRecord)) {
            List<IObjectData> dataList = serviceFacade.bulkSaveObjectData(boxCodesInspectionRecord, context.getUser());
            context.setBoxRecordDataList(dataList);
            List<IObjectData> collect = dataList.stream()
                    .filter(o -> snId.equals(o.get(InspectionRecordObjApiNames.FMCG_SERIAL_NUMBER_ID)))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)) {
                log.info("saveInspectionData bulkSaveObjectData collect is empty");
                DataUtil.putAll(context.getObjectData(), dataList.get(0));
                return;
            }
            DataUtil.putAll(context.getObjectData(), collect.get(0));
        } else {
            IObjectData objectData = serviceFacade.saveObjectData(context.getUser(), context.getObjectData());
            DataUtil.putAll(context.getObjectData(), objectData);
        }
        context.getFsStopWatch().lap("saveInspectionData");
    }

    private List<IObjectData> handleBoxCodesInspectionRecord(IObjectData objectData, InspectionVerify.Context context) {
        List<String> boxCodeIds = getBoxCodeIds(objectData, context);
        if (CollectionUtils.isEmpty(boxCodeIds)) {
            return null;
        }
        List<IObjectData> dataList = Lists.newArrayList();
        for (String boxCodeId : boxCodeIds) {
            IObjectData temp = new ObjectData();
            DataUtil.putAll(temp, objectData);
            temp.set(InspectionRecordObjApiNames.FMCG_SERIAL_NUMBER_ID, boxCodeId);
            dataList.add(temp);
        }
        return dataList;
    }

    private List<String> getBoxCodeIds(IObjectData objectData, InspectionVerify.Context context) {
        if (!objectData.containsField(InspectionRecordObjApiNames.BOX_CODE_IDS_TEMP)) {
            return null;
        }
        List<String> boxCodeIds = JSONObject.parseObject(JSON.toJSONString(objectData.get(InspectionRecordObjApiNames.BOX_CODE_IDS_TEMP)), new TypeReference<List<String>>() {
        });
        if (CollectionUtils.isEmpty(boxCodeIds)) {
            return null;
        }
        return boxCodeIds;
    }

    private void preSaveInspectionData(InspectionVerify.Context context) {
        context.getObjectData().setIsPublic(true);
        context.getObjectData().setRecordType("default__c");
        context.getObjectData().setTenantId(context.getTenantId());
        context.getObjectData().setDescribeApiName(InspectionRecordObjApiNames.OBJECT_API_NAME);
        editDataVisibleRange(context);
        if (!GrayRelease.isAllow("fmcg", "ASYNC_FILL_EXTRA_FIELD", context.getTenantId())) {
            Map<String, Map<String, Object>> fillExtraFieldMap = fillExtraField(context, Lists.newArrayList(context.getObjectData()));
            context.getFsStopWatch().lap("preSave-fillExtraField");
            String snId = context.getObjectData().get(InspectionRecordObjApiNames.FMCG_SERIAL_NUMBER_ID, String.class);
            if (!fillExtraFieldMap.containsKey(snId)) {
                log.info("preSaveInspectionData fillExtraFieldMap not contains snId {}", snId);
                return;
            }
            DataUtil.putAll(context.getObjectData(), fillExtraFieldMap.get(snId));
        }
    }

    /**
     * 非/窜货 1端和当前企业可以看见
     * 1端稽查 窜货 1端、查询企业(被窜货企业)、窜货企业 可见
     * 下游稽查 窜货 1端、查询企业(被窜货企业=当前企业)、窜货企业
     */
    private void editDataVisibleRange(InspectionVerify.Context context) {
        Set<String> range = Sets.newHashSet();
        range.add(context.getUpstreamTenantId());
        range.add(context.getTenantId());

        if (!"1".equals(context.getObjectData().get(InspectionRecordObjApiNames.INSPECTION_RESULT, String.class))) {
            DataUtil.editDataVisibleRange(context.getObjectData(), Lists.newArrayList(range));
            return;
        }

        if (Objects.nonNull(context.getObjectData().get(InspectionRecordObjApiNames.BELONG_TENANT_ID, String.class))) {
            range.add(context.getObjectData().get(InspectionRecordObjApiNames.BELONG_TENANT_ID, String.class));
        }

        Boolean dealer = inspectionService.isDealer(context.getTenantId(), context.getUpstreamTenantId());

        if (!dealer && StringUtils.isNotEmpty(context.getObjectData().get(InspectionRecordObjApiNames.INSPECTION_ACCOUNT_ID, String.class))) {
            List<String> accountEnterpriseIds = inspectionBusiness.findAccountEnterprise(context.getUpstreamTenantId(),
                    Lists.newArrayList(context.getObjectData().get(InspectionRecordObjApiNames.INSPECTION_ACCOUNT_ID, String.class)));
            if (CollectionUtils.isNotEmpty(accountEnterpriseIds)) {
                range.add(accountEnterpriseIds.get(0));
            }
        }

        DataUtil.editDataVisibleRange(context.getObjectData(), Lists.newArrayList(range));
    }

    private void updateInspectionData(InspectionVerify.Context context, List<IObjectData> boxInspectionRecords, Map<String, Object> updateMap) {
        if (MapUtils.isEmpty(updateMap)) {
            return;
        }
        if (CollectionUtils.isEmpty(boxInspectionRecords)) {
            IObjectData objectData = serviceFacade.updateWithMap(context.getUser(), context.getObjectData(), updateMap);
            DataUtil.putAll(context.getObjectData(), objectData);
        } else {
            List<IObjectData> dataList = serviceFacade.batchUpdateWithMap(context.getUser(), boxInspectionRecords, updateMap);
            String snId = context.getObjectData().get(InspectionRecordObjApiNames.FMCG_SERIAL_NUMBER_ID, String.class);
            List<IObjectData> collect = dataList.stream()
                    .filter(o -> snId.equals(o.get(InspectionRecordObjApiNames.FMCG_SERIAL_NUMBER_ID)))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)) {
                log.info("updateInspectionData collect is empty");
                DataUtil.putAll(context.getObjectData(), dataList.get(0));
                return;
            }
            DataUtil.putAll(context.getObjectData(), collect.get(0));
        }
    }

    protected void handleSnBelongData(InspectionVerify.Context context) {
        EnterpriseActionBeanFactory.resolve(context.getTenantId(),
                new TypeReference<HandleFmcgSerialNumberAction<InspectionVerify.Context>>() {
                }
        ).action(context);
        context.getFsStopWatch().lap("SnBelongAction");
        if (Objects.isNull(context.getObjectData().get(InspectionRecordObjApiNames.BELONG_ACCOUNT_ID))) {
            //如果开启连扫，填充完成所有字段，待批量数据保存；开启连扫，默认无复核
            if (whetherEnableContinuousScan(context)) {
                preSaveInspectionData(context);
                throw new InspectionVerifyNormalInterruptException();
            }
            saveInspectionData(context);
            throw new InspectionVerifyNormalInterruptException();
        }
        List<String> accountEnterpriseIds = inspectionBusiness.findAccountEnterprise(context.getUpstreamTenantId(),
                Lists.newArrayList(context.getObjectData().get(InspectionRecordObjApiNames.BELONG_ACCOUNT_ID, String.class)));
        if (CollectionUtils.isNotEmpty(accountEnterpriseIds)) {
            context.getObjectData().set(InspectionRecordObjApiNames.BELONG_TENANT_ID, accountEnterpriseIds.get(0));
        }
        context.getFsStopWatch().lap("fillBelongAccountEnterprise");
    }
}
