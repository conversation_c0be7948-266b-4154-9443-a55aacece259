package com.facishare.crm.fmcg.sales.abs;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.crm.fmcg.sales.apiname.AccountObjApiNames;
import com.facishare.crm.fmcg.sales.apiname.DepartmentObjApiNames;
import com.facishare.crm.fmcg.sales.apiname.InspectionRecordObjApiNames;
import com.facishare.crm.fmcg.sales.business.InspectionBusiness;
import com.facishare.crm.fmcg.sales.model.inspection.FillExtraField;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> liuhaoyu
 * @create : 2025-03-25 15:23
 **/
@Slf4j
@Component("mengNiuFillExtraFieldAction")
public class MengNiuFillExtraFieldAction extends NormalFillExtraFieldAction {
    public static final List<String> CUSTOM_FILL_FIELDS_API_NAMES = Lists.newArrayList("counterfeit_code_area__c", "large_area_code__c", "counterflow_party_sap_code__c",
            "counterfeit_sap_code__c", "counterfeit_province_area__c", "affected_region_info__c", "customer_category__c", "counterfeit_customer_categ__c", "distributor_region__c", "overrun_region__c");

    @Resource
    private InspectionBusiness inspectionBusiness;

    @Override
    protected void fillCustomizedData(Map<String, Map<String, Object>> fillExtraFieldMap, FillExtraField.Context context) {
        List<IObjectData> accounts = findAccounts(context);
        if (CollectionUtils.isEmpty(accounts)) {
            return;
        }
        fillAccounts(context, fillExtraFieldMap, accounts);
        List<IObjectData> departments = findDepartments(context, accounts);
        if (CollectionUtils.isEmpty(departments)) {
            return;
        }
        fillDepartments(context, fillExtraFieldMap, departments);
        List<IObjectData> parentDepartments = findParentDepartments(context, departments);
        if (CollectionUtils.isEmpty(parentDepartments)) {
            return;
        }
        fillParentDepartments(context, fillExtraFieldMap, parentDepartments);
    }

    private void fillParentDepartments(FillExtraField.Context context, Map<String, Map<String, Object>> fillExtraFieldMap, List<IObjectData> parentDepartments) {
        Map<String, IObjectData> collect = parentDepartments.stream()
                .collect(Collectors.toMap(DBRecord::getId, o -> o));
        for (IObjectData objectData : context.getObjectDataList()) {
            String snId = objectData.get(InspectionRecordObjApiNames.FMCG_SERIAL_NUMBER_ID, String.class);
            Map<String, Object> extraFieldMap = Maps.newHashMap();
            if (fillExtraFieldMap.containsKey(snId)) {
                extraFieldMap = fillExtraFieldMap.get(snId);
            } else {
                fillExtraFieldMap.put(snId, extraFieldMap);
            }
            if (extraFieldMap.containsKey("counterfeit_code_area__c_id_temp")) {
                String temp = String.valueOf(extraFieldMap.get("counterfeit_code_area__c_id_temp"));
                if (collect.containsKey(temp)) {
                    extraFieldMap.put("counterfeit_code_area__c", collect.get(temp).get(DepartmentObjApiNames.DEPT_CODE));
                    extraFieldMap.put("distributor_region__c", collect.get(temp).getName());
                }
            }
            if (extraFieldMap.containsKey("large_area_code__c_id_temp")) {
                String temp = String.valueOf(extraFieldMap.get("large_area_code__c_id_temp"));
                if (collect.containsKey(temp)) {
                    extraFieldMap.put("large_area_code__c", collect.get(temp).get(DepartmentObjApiNames.DEPT_CODE));
                    extraFieldMap.put("overrun_region__c", collect.get(temp).getName());
                }
            }
        }
    }

    private void fillDepartments(FillExtraField.Context context, Map<String, Map<String, Object>> fillExtraFieldMap, List<IObjectData> departments) {
        Map<String, IObjectData> collect = departments.stream()
                .collect(Collectors.toMap(DBRecord::getId, o -> o));
        for (IObjectData objectData : context.getObjectDataList()) {
            String snId = objectData.get(InspectionRecordObjApiNames.FMCG_SERIAL_NUMBER_ID, String.class);
            Map<String, Object> extraFieldMap = Maps.newHashMap();
            if (fillExtraFieldMap.containsKey(snId)) {
                extraFieldMap = fillExtraFieldMap.get(snId);
            } else {
                fillExtraFieldMap.put(snId, extraFieldMap);
            }
            if (extraFieldMap.containsKey("counterfeit_province_area__c_temp_id")) {
                String temp = String.valueOf(extraFieldMap.get("counterfeit_province_area__c_temp_id"));
                if (collect.containsKey(temp)) {
                    extraFieldMap.put("counterfeit_province_area__c", collect.get(temp).getName());
                    if (CollectionUtils.isNotEmpty(collect.get(temp).get(DepartmentObjApiNames.PARENT_ID, List.class))) {
                        List<String> parentIds = JSONObject.parseObject(JSON.toJSONString(collect.get(temp).get(DepartmentObjApiNames.PARENT_ID)), new TypeReference<List<String>>() {
                        });
                        extraFieldMap.put("counterfeit_code_area__c_id_temp", parentIds.get(0));
                    }
                }
            }
            if (extraFieldMap.containsKey("affected_region_info__c_temp_id")) {
                String temp = String.valueOf(extraFieldMap.get("affected_region_info__c_temp_id"));
                if (collect.containsKey(temp)) {
                    extraFieldMap.put("affected_region_info__c", collect.get(temp).getName());
                    if (CollectionUtils.isNotEmpty(collect.get(temp).get(DepartmentObjApiNames.PARENT_ID, List.class))) {
                        List<String> parentIds = JSONObject.parseObject(JSON.toJSONString(collect.get(temp).get(DepartmentObjApiNames.PARENT_ID)), new TypeReference<List<String>>() {
                        });
                        extraFieldMap.put("large_area_code__c_id_temp", parentIds.get(0));
                    }
                }
            }
        }
    }

    private void fillAccounts(FillExtraField.Context context, Map<String, Map<String, Object>> fillExtraFieldMap, List<IObjectData> accounts) {
        Map<String, IObjectData> collect = accounts.stream()
                .collect(Collectors.toMap(DBRecord::getId, o -> o));
        for (IObjectData objectData : context.getObjectDataList()) {
            String snId = objectData.get(InspectionRecordObjApiNames.FMCG_SERIAL_NUMBER_ID, String.class);
            Map<String, Object> extraFieldMap = Maps.newHashMap();
            if (fillExtraFieldMap.containsKey(snId)) {
                extraFieldMap = fillExtraFieldMap.get(snId);
            } else {
                fillExtraFieldMap.put(snId, extraFieldMap);
            }
            String illegallyGoodsAccountId = objectData.get(InspectionRecordObjApiNames.ILLEGALLY_GOODS_ACCOUNT_ID, String.class);
            String inspectionAccountId = objectData.get(InspectionRecordObjApiNames.INSPECTION_ACCOUNT_ID, String.class);
            if (collect.containsKey(illegallyGoodsAccountId)) {
                IObjectData illegallyGoodsAccount = collect.get(illegallyGoodsAccountId);
                extraFieldMap.put("counterflow_party_sap_code__c", illegallyGoodsAccount.get("account_no__c"));
                extraFieldMap.put("customer_category__c", illegallyGoodsAccount.get("dealer_level"));
                if (CollectionUtils.isNotEmpty(illegallyGoodsAccount.getDataOwnDepartment())) {
                    extraFieldMap.put("counterfeit_province_area__c_temp_id", illegallyGoodsAccount.getDataOwnDepartment().get(0));
                }
            }
            if (collect.containsKey(inspectionAccountId)) {
                IObjectData inspectionAccount = collect.get(inspectionAccountId);
                extraFieldMap.put("counterfeit_sap_code__c", inspectionAccount.get("account_no__c"));
                extraFieldMap.put("counterfeit_customer_categ__c", inspectionAccount.get("dealer_level"));
                if (CollectionUtils.isNotEmpty(inspectionAccount.getDataOwnDepartment())) {
                    extraFieldMap.put("affected_region_info__c_temp_id", inspectionAccount.getDataOwnDepartment().get(0));
                }
            }
        }
    }

    private List<IObjectData> findParentDepartments(FillExtraField.Context context, List<IObjectData> departments) {
        Set<String> parentDepartmentIds = Sets.newHashSet();
        for (IObjectData department : departments) {
            if (CollectionUtils.isEmpty(department.get(DepartmentObjApiNames.PARENT_ID, List.class))) {
                continue;
            }
            List<String> parentIds = JSONObject.parseObject(JSON.toJSONString(department.get(DepartmentObjApiNames.PARENT_ID)), new TypeReference<List<String>>() {
            });
            parentDepartmentIds.add(parentIds.get(0));
        }
        if (CollectionUtils.isEmpty(parentDepartmentIds)) {
            return Lists.newArrayList();
        }
        List<IObjectData> parentDepartments = inspectionBusiness.findByIds(context.getVerifyContext().getUpstreamTenantId(), Lists.newArrayList(parentDepartmentIds), DepartmentObjApiNames.OBJECT_API_NAME);
        if (CollectionUtils.isEmpty(parentDepartments)) {
            return Lists.newArrayList();
        }
        return parentDepartments;
    }

    private List<IObjectData> findDepartments(FillExtraField.Context context, List<IObjectData> accounts) {
        Set<String> departmentIds = Sets.newHashSet();
        for (IObjectData account : accounts) {
            if (CollectionUtils.isEmpty(account.getDataOwnDepartment())) {
                continue;
            }
            departmentIds.addAll(account.getDataOwnDepartment());
        }
        if (CollectionUtils.isEmpty(departmentIds)) {
            return Lists.newArrayList();
        }
        List<IObjectData> departments = inspectionBusiness.findByIds(context.getVerifyContext().getUpstreamTenantId(), Lists.newArrayList(departmentIds), DepartmentObjApiNames.OBJECT_API_NAME);
        if (CollectionUtils.isEmpty(departments)) {
            return Lists.newArrayList();
        }
        return departments;
    }

    private List<IObjectData> findAccounts(FillExtraField.Context context) {
        List<String> accountIds = Lists.newArrayList();
        for (IObjectData objectData : context.getObjectDataList()) {
            if (!"1".equals(objectData.get(InspectionRecordObjApiNames.INSPECTION_RESULT, String.class))) {
                continue;
            }
            if (Objects.nonNull(objectData.get(InspectionRecordObjApiNames.ILLEGALLY_GOODS_ACCOUNT_ID))) {
                accountIds.add(objectData.get(InspectionRecordObjApiNames.ILLEGALLY_GOODS_ACCOUNT_ID, String.class));
            }
            if (Objects.nonNull(objectData.get(InspectionRecordObjApiNames.INSPECTION_ACCOUNT_ID))) {
                accountIds.add(objectData.get(InspectionRecordObjApiNames.INSPECTION_ACCOUNT_ID, String.class));
            }
        }
        if (CollectionUtils.isEmpty(accountIds)) {
            return Lists.newArrayList();
        }
        List<IObjectData> accounts = inspectionBusiness.findByIds(context.getVerifyContext().getUpstreamTenantId(), accountIds, AccountObjApiNames.OBJECT_API_NAME);
        if (CollectionUtils.isEmpty(accounts)) {
            return Lists.newArrayList();
        }
        return accounts;
    }

    @Override
    protected List<String> getCustomizedFillFieldsApiName(FillExtraField.Context context) {
        return CUSTOM_FILL_FIELDS_API_NAMES;
    }
}
