package com.facishare.crm.fmcg.sales.utils;

import com.fxiaoke.i18n.client.I18nClient;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import org.apache.commons.lang3.StringUtils;

/**
 * @description: 多语言国际码处理类
 * @author: lijingsong
 * @date: 2022/4/1
 **/
public class I18N {

    private I18N() {
        // close for instance
    }

    static {
        I18nClient.getInstance().initWithTags("server");
    }

    public static final String DEFAULT_LOCALE = "zh-CN";
    public static final String DEFAULT_VALUE = "";
    public static final int DEFAULT_TENANT_ID = 0;
    public static final String FMCG_SALES_PREFIX = "FMCG.SALES";

    public static String get(String key, Object... args) {
        return getWithLocale(formatKey(key), TraceContext.get().getLocale(), args);
    }

    public static String getWithLocale(String key, String locale, Object... args) {
        return getWithTenantIdAndLocale(formatKey(key), DEFAULT_TENANT_ID, locale, args);
    }

    public static String getWithTenantId(String key, long tenantId, Object... args) {
        return getWithTenantIdAndLocale(key, tenantId, TraceContext.get().getLocale(), args);
    }

    public static String getWithTenantIdAndLocale(String key, long tenantId, String locale, Object... args) {
        if (args != null && args.length != 0)
            return String.format(I18nClient.getInstance().getOrDefault(formatKey(key), tenantId, formatLocale(locale), DEFAULT_VALUE), args);
        return I18nClient.getInstance().getOrDefault(formatKey(key), tenantId, formatLocale(locale), DEFAULT_VALUE);
    }

    private static String formatKey(String key) {
        return !key.startsWith(FMCG_SALES_PREFIX) ? String.format("%s.%s", FMCG_SALES_PREFIX, key) : key;
    }

    private static String formatLocale(String locale) {
        return Strings.isNullOrEmpty(locale) ? DEFAULT_LOCALE : locale;
    }

    public static String getBySpecificKey(String key, Object... args) {
        return getWithTenantIdAndLocale(DEFAULT_TENANT_ID, TraceContext.get().getLocale(), key, args);
    }

    public static String getByTenantIdAndSpecificKey(int tenantId, String key, Object... args) {
        return getWithTenantIdAndLocale(tenantId, TraceContext.get().getLocale(), key, args);
    }

    private static String getWithTenantIdAndLocale(long tenantId, String locale, String key, Object... args) {
        if (args != null && args.length != 0)
            return String.format(I18nClient.getInstance().getOrDefault(key, tenantId, formatLocale(locale), DEFAULT_VALUE), args);
        return I18nClient.getInstance().getOrDefault(key, tenantId, formatLocale(locale), DEFAULT_VALUE);
    }

    public static String getOrDefault(String key, String defaultValue, Object... args) {
        String value = get(key, args);
        return StringUtils.isNotBlank(value) ? value : defaultValue;
    }
}
