package com.facishare.crm.fmcg.sales.service.abstraction;

import com.facishare.crm.fmcg.sales.model.sn.*;
import com.facishare.paas.appframework.core.model.ServiceContext;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @create: 2023-09-18 20:39
 **/
public interface IFMCGSerialNumberActionService {
    QuerySnAction.Result querySnAction(QuerySnAction.Arg arg, ServiceContext serviceContext);

    InsertMongoAction.Result insertMongoAction(InsertMongoAction.Arg arg, ServiceContext serviceContext);

    DeleteMongoAction.Result deleteMongoAction(DeleteMongoAction.Arg arg, ServiceContext serviceContext);

    UpdateMongoAction.Result updateMongoAction(UpdateMongoAction.Arg arg, ServiceContext serviceContext);

    ClearActionCache.Result clearActionCache(ClearActionCache.Arg arg, ServiceContext serviceContext);
}
