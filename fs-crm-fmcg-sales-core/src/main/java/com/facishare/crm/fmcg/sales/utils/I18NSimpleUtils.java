package com.facishare.crm.fmcg.sales.utils;

import com.facishare.paas.appframework.core.util.Lang;
import com.fxiaoke.i18n.client.I18nClient;

public class I18NSimpleUtils {
    private I18NSimpleUtils() {
    }

    static {
        I18nClient.getInstance().initWithTags("server", "pre_object");
    }

    public static String get(String key, Lang lang, String defaultValue) {
        return get(key, 0, lang.getValue(), defaultValue);
    }

    public static String get(String key, String lang, String defaultValue) {
        return get(key, 0, lang, defaultValue);
    }

    public static String get(String key, Integer tenantId, String lang, String defaultValue) {
        return I18nClient.getInstance().get(key, tenantId, lang, defaultValue);
    }
}
