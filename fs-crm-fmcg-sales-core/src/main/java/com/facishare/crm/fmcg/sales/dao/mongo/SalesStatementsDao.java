package com.facishare.crm.fmcg.sales.dao.mongo;

import com.facishare.crm.fmcg.sales.dao.mongo.entity.SalesStatementsDetailEntity;
import org.springframework.stereotype.Service;

import java.time.Instant;

/**
 * <AUTHOR>
 */
@Service
public class SalesStatementsDao extends BaseDao {

    public void saveSalesStatementsDetailEntity(String mongoId, String tenantId, String data) {
        SalesStatementsDetailEntity salesStatementsDetailEntity = new SalesStatementsDetailEntity();
        salesStatementsDetailEntity.setId(mongoId);
        salesStatementsDetailEntity.setCreateTime(Instant.now().toEpochMilli());
        salesStatementsDetailEntity.setData(data);
        salesStatementsDetailEntity.setTenantId(tenantId);
        save(salesStatementsDetailEntity);
    }
}
