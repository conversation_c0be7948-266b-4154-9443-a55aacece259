package com.facishare.crm.fmcg.sales.controller;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.sales.action.SalesStatementsObjConformanceStatementsAction;
import com.facishare.crm.fmcg.sales.enums.ButtonAction;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class SalesStatementsObjWebDetailController extends StandardWebDetailController {

    @Override
    protected Result after(Arg arg, Result result) {
        buttonFilter(arg, result);
        return super.after(arg, result);
    }

    public void buttonFilter(Arg arg, Result result) {
        List components = (ArrayList) (result.getLayout().get("components"));
        List<String> removeList = Lists.newArrayList("Edit");
        if (!"mobile".equals(arg.getLayoutAgentType())) {
            removeList.add(ObjectAction.Print_Receipt.getActionCode());
        }
        String statementStatus = data.get("statement_status", String.class, SalesStatementsObjConformanceStatementsAction.CLOSE);
        if (SalesStatementsObjConformanceStatementsAction.OPEN.equals(statementStatus)) {
            removeList.add(ObjectAction.Conformance_Statements.getActionCode());
        } else if (SalesStatementsObjConformanceStatementsAction.CLOSE_OFF.equals(statementStatus)) {
            removeList.add(ObjectAction.Conformance_Statements.getActionCode());
            removeList.add(ButtonAction.Close_Statements.getActionCode());
        }
        removeButton(components, removeList);
    }

    private void removeButton(List components, List<String> removeList) {
        for (Object component : components) {
            Map com = (Map) component;
            if ("head_info".equals(com.get("api_name"))) {
                ArrayList buttons = (ArrayList) com.get("buttons");
                buttons.removeIf(button -> {
                    Map btn = (Map) (button);
                    return removeList.contains(btn.get("action"));
                });
            }
        }
    }
}
