package com.facishare.crm.fmcg.sales.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.sales.apiname.AccountObjApiNames;
import com.facishare.crm.fmcg.sales.apiname.CommonApiNames;
import com.facishare.crm.fmcg.sales.apiname.ContactObjApiNames;
import com.facishare.crm.fmcg.sales.apiname.EnterpriseRelationObjApiNames;
import com.facishare.crm.fmcg.sales.model.interconnection.*;
import com.facishare.crm.fmcg.sales.service.abstraction.IInterconnectionService;
import com.facishare.organization.paas.model.PaaSResult;
import com.facishare.organization.paas.model.permission.BatchAddUserRoleDto;
import com.facishare.organization.paas.model.permission.PaasRoleDetail;
import com.facishare.organization.paas.model.permission.RoleListDto;
import com.facishare.organization.paas.model.permission.UpdateUserDefaultRole;
import com.facishare.organization.paas.service.PaaSPermissionService;
import com.facishare.organization.paas.util.PaasArgumentUtil;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseImportAction;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportDataAction;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.ActionContextUtil;
import com.facishare.uc.api.model.employee.arg.ResetEmployeePasswordArg;
import com.facishare.uc.api.model.employee.result.ResetEmployeePasswordResult;
import com.facishare.uc.api.service.EmployeeEditionService;
import com.fmcg.framework.http.*;
import com.fmcg.framework.http.contract.data.auth.UpdateEntityOpenness;
import com.fmcg.framework.http.contract.paas.data.*;
import com.fmcg.framework.http.contract.personnel.AsyncBulkResetPassword;
import com.fmcg.framework.http.contract.personnel.AsyncBulkResume;
import com.fmcg.framework.http.contract.personnel.AsyncBulkStop;
import com.fmcg.framework.http.contract.syncdataall.DelSyncDataMapping;
import com.fmcg.framework.http.contract.syncdataall.ListByPloyDetailId;
import com.fmcg.framework.http.contract.syncdataall.QuerySyncDataMapping;
import com.fxiaoke.enterpriserelation2.arg.AddOutAppRoleAssignsArg;
import com.fxiaoke.enterpriserelation2.arg.CreateDownstreamPublicEmployeeByMapperObjectIdOutArg;
import com.fxiaoke.enterpriserelation2.arg.CreateEnterpriseRelationArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.OuterAccountVo;
import com.fxiaoke.enterpriserelation2.service.EnterpriseRelationService;
import com.fxiaoke.enterpriserelation2.service.LinkAppService;
import com.fxiaoke.enterpriserelation2.service.PublicEmployeeService;
import com.github.trace.TraceContext;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> liuhaoyu
 * @create : 2023-10-26 16:04
 **/
@Service
@Slf4j
@ServiceModule("interconnection_service")
public class InterconnectionService implements IInterconnectionService {
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private PublicEmployeeService publicEmployeeService;
    @Resource
    private PaasDataProxy paasDataProxy;
    @Resource
    private PaaSPermissionService paaSPermissionService;
    @Resource
    private EIEAConverter eieaConverter;

    @Resource
    private DataAuthProxy paasDataAuthProxy;

    @Resource
    private EnterpriseRelationProxy enterpriseRelationProxy;

    @Resource
    private SyncDataAllProxy syncDataAllProxy;
    @Resource
    private PersonnelProxy personnelProxy;
    @Resource
    private LinkAppService linkAppService;
    @Autowired
    private EnterpriseRelationService enterpriseRelationService;
    @Resource
    private EmployeeEditionService employeeEditionService;

    @ServiceMethod("del_repeat_account_sync_info")
    public DelRepeatAccountSyncInfo.Result delRepeatAccountSyncInfo(DelRepeatAccountSyncInfo.Arg arg, ServiceContext serviceContext) {
        DelRepeatAccountSyncInfo.Result result = new DelRepeatAccountSyncInfo.Result();
        result.setErrorCode(0);
        result.setErrorMsg("success");
        log.info("delRepeatAccountSyncInfo arg : {}", JSON.toJSONString(arg));
        DelRepeatAccountSyncInfo.ArgBody argBody = arg.getArg();
        if (Objects.isNull(argBody)) {
            result.setErrorCode(500000);
            result.setErrorMsg("Parameter error");
            return result;
        }

        String dTenantId = serviceContext.getTenantId();
        String dDataId = argBody.getDataId();
        String upstreamTenantId = argBody.getUpstreamTenantId();

        String uDataId = queryUpStreamDataIdFromSyncInfo(Integer.parseInt(upstreamTenantId), dTenantId, dDataId);
        log.info("delRepeatAccountSyncInfo uDataId : {}", uDataId);

        if (Strings.isNullOrEmpty(uDataId)) {
            result.setErrorCode(500001);
            result.setErrorMsg("The upstream data id is not found");
            return result;
        }

        delSyncInfo(dTenantId, dDataId, upstreamTenantId, uDataId);
        delData(dTenantId, dDataId, upstreamTenantId, uDataId);
        return result;
    }

    public String queryUpStreamDataIdFromSyncInfo(Integer upstreamTenantId, String dTenantId, String dDataId) {
        //查询采集记录
        QuerySyncDataMapping.Arg queryCollectMappingArg = new QuerySyncDataMapping.Arg();
        QuerySyncDataMapping.ArgBody queryCollectMappingArgBody = new QuerySyncDataMapping.ArgBody();
        queryCollectMappingArgBody.setSourceDataIds(Lists.newArrayList(dDataId));
        queryCollectMappingArgBody.setSourceTenantId(dTenantId);
        queryCollectMappingArgBody.setSourceObjectApiName("AccountObj");
        queryCollectMappingArgBody.setDestObjectApiName("AccountObj");
        queryCollectMappingArg.setArg(queryCollectMappingArgBody);

        log.info("queryCollectMappingArg : {}", JSON.toJSONString(queryCollectMappingArg));
        QuerySyncDataMapping.Result queryCollectMappingResult = syncDataAllProxy.querySyncDataMapping(upstreamTenantId, -10000, queryCollectMappingArg);
        log.info("queryCollectMappingResult : {}", JSON.toJSONString(queryCollectMappingResult));
        if (queryCollectMappingResult.getErrCode() == 0 && CollectionUtils.isNotEmpty(queryCollectMappingResult.getData())) {
            return queryCollectMappingResult.getData().get(0).getString("destDataId");
        }

        //查询下发记录
        QuerySyncDataMapping.Arg queryIssueMappingArg = new QuerySyncDataMapping.Arg();
        QuerySyncDataMapping.ArgBody queryIssueMappingArgBody = new QuerySyncDataMapping.ArgBody();
        queryIssueMappingArgBody.setDestDataIds(Lists.newArrayList(dDataId));
        queryIssueMappingArgBody.setDestTenantId(dTenantId);
        queryIssueMappingArgBody.setDestObjectApiName("AccountObj");
        queryIssueMappingArgBody.setSourceObjectApiName("AccountObj");
        queryIssueMappingArg.setArg(queryIssueMappingArgBody);
        log.info("queryIssueMappingArg : {}", JSON.toJSONString(queryIssueMappingArg));
        QuerySyncDataMapping.Result queryIssueMappingResult = syncDataAllProxy.querySyncDataMapping(upstreamTenantId, -10000, queryIssueMappingArg);
        log.info("queryCollectMappingResult : {}", JSON.toJSONString(queryIssueMappingResult));
        if (queryIssueMappingResult.getErrCode() == 0 && CollectionUtils.isNotEmpty(queryIssueMappingResult.getData())) {
            return queryCollectMappingResult.getData().get(0).getString("sourceDataId");
        }
        return null;
    }

    public void delSyncInfo(String dTenantId, String dDataId, String upstreamTenantId, String uDataId) {
        //删除采集记录
        DelSyncDataMapping.Arg delCollectMappingArg = new DelSyncDataMapping.Arg();
        DelSyncDataMapping.ArgBody delCollectMappingArgBody = new DelSyncDataMapping.ArgBody();
        delCollectMappingArgBody.setDestObjectApiName("AccountObj");
        delCollectMappingArgBody.setSourceObjectApiName("AccountObj");
        delCollectMappingArgBody.setSourceTenantId(dTenantId);
        delCollectMappingArgBody.setSourceDataIds(Lists.newArrayList(dDataId));
        delCollectMappingArg.setArg(delCollectMappingArgBody);
        log.info("delCollectMappingArg : {}", JSON.toJSONString(delCollectMappingArg));
        DelSyncDataMapping.Result delCollectMappingResult = syncDataAllProxy.delSyncDataMapping(Integer.parseInt(upstreamTenantId), -10000, delCollectMappingArg);
        log.info("delCollectMappingResult : {}", JSON.toJSONString(delCollectMappingResult));

        //删除下发记录
        DelSyncDataMapping.Arg delIssueMappingArg = new DelSyncDataMapping.Arg();
        DelSyncDataMapping.ArgBody delIssueMappingArgBody = new DelSyncDataMapping.ArgBody();
        delIssueMappingArgBody.setSourceObjectApiName("AccountObj");
        delIssueMappingArgBody.setDestObjectApiName("AccountObj");
        delIssueMappingArgBody.setDestTenantId(dTenantId);
        delIssueMappingArgBody.setDestDataIds(Lists.newArrayList(dDataId));
        delIssueMappingArg.setArg(delIssueMappingArgBody);
        log.info("delIssueMappingArg : {}", JSON.toJSONString(delIssueMappingArg));
        DelSyncDataMapping.Result delIssueMappingResult = syncDataAllProxy.delSyncDataMapping(Integer.parseInt(upstreamTenantId), -10000, delIssueMappingArg);
        log.info("delIssueMappingResult : {}", JSON.toJSONString(delIssueMappingResult));
    }

    public void delData(String dTenantId, String dDataId, String upstreamTenantId, String uDataId) {
        PaasDataBatchBulkDelete.Arg deleteTpmActivityArg = new PaasDataBatchBulkDelete.Arg();
        deleteTpmActivityArg.setDescribeApiName("AccountObj");
        deleteTpmActivityArg.setIdList(Lists.newArrayList(dDataId));
        log.info("delData deleteTpmActivityArg {}", JSON.toJSONString(deleteTpmActivityArg));
        PaasDataBatchBulkDelete.Result deleteTpmActivityResult = paasDataProxy.batchBulkDelete(Integer.parseInt(dTenantId), -10000, "AccountObj", deleteTpmActivityArg);
        log.info("delData deleteTpmActivityResult {}", JSON.toJSONString(deleteTpmActivityResult));

        List<IObjectData> objectDataList = Lists.newArrayList();
        IObjectData objectData = new ObjectData();
        objectData.setId(uDataId);
        objectData.setDescribeApiName("AccountObj");
        objectData.setTenantId(upstreamTenantId);
        objectDataList.add(objectData);
        log.info("delUpstreamData arg : {}", JSON.toJSONString(objectDataList));
        List<IObjectData> result = serviceFacade.bulkDeleteDirect(objectDataList, User.systemUser(upstreamTenantId));
        log.info("delUpstreamData result : {}", JSON.toJSONString(result));
    }

    @Override
    @ServiceMethod("batch_open_downstream")
    public BatchOpenDownstream.Result batchOpenDownstream(BatchOpenDownstream.Arg arg, ServiceContext serviceContext) {
        BatchOpenDownstream.Result result = new BatchOpenDownstream.Result();
        String tenantId = serviceContext.getTenantId();
        List<String> accountIds = arg.getArg().getAccountIds();
        List<IObjectData> accountDataList = serviceFacade.findObjectDataByIds(tenantId, accountIds, AccountObjApiNames.OBJECT_API_NAME);
        List<IObjectData> accountList = accountDataList.stream()
                .filter(o -> Objects.isNull(o.get(AccountObjApiNames.ENTERPRISERELATION_ID)))
                .filter(o -> "dealer__c".equals(o.getRecordType()) || "secondary_dealer__c".equals(o.getRecordType()) || "mollercular_company__c".equals(o.getRecordType()))
                .collect(Collectors.toList());
        for (IObjectData accountData : accountList) {
            try {
                createDownstream(tenantId, accountData, arg);
            } catch (Exception e) {
                log.info("createDownstream error accountId : {}", accountData.getId());
                log.error("createDownstream is error : ", e);
                result.setErrorMsg("101");
                result.setErrorMsg("system error");
                return result;
            }
        }
        result.setErrorCode(0);
        result.setErrorMsg("Success");
        return result;
    }

    @ServiceMethod("create_relation")
    public CreateRelation.Result createRelation(CreateRelation.Arg arg, ServiceContext serviceContext) {
        createEnterpriseRelation(arg);
        return CreateRelation.Result.success();
    }

    private void createEnterpriseRelation(CreateRelation.Arg arg) {
        HeaderObj headerObj = HeaderObj.newInstance(arg.getUpstreamTenantId());
        CreateEnterpriseRelationArg createEnterpriseRelationArg = new CreateEnterpriseRelationArg();
        createEnterpriseRelationArg.setUpstreamTenantId(arg.getUpstreamTenantId());
        createEnterpriseRelationArg.setDownstreamTenantId(arg.getDownstreamTenantId());
        createEnterpriseRelationArg.setMapperApiName(arg.getMapperApiName());
        createEnterpriseRelationArg.setMapperObjectId(arg.getMapperObjectId());
        createEnterpriseRelationArg.setDownstreamShortName(arg.getDownstreamShortName());
        RestResult<Void> createEnterpriseRelationResult = enterpriseRelationService.createEnterpriseRelation(headerObj, createEnterpriseRelationArg);
        System.out.println(createEnterpriseRelationResult);
    }

    @Override
    @ServiceMethod("update_personnel_phone")
    public UpdatePersonnelPhone.Result updatePersonnelPhone(UpdatePersonnelPhone.Arg arg, ServiceContext serviceContext) {
        UpdatePersonnelPhone.Result result = new UpdatePersonnelPhone.Result();
        List<String> initPersonnelIds = arg.getInitPersonnelIds();
        String tenantId = arg.getTenantId();

        PaasDataFindByIds.Arg findInitPersonnelArg = new PaasDataFindByIds.Arg();
        findInitPersonnelArg.setDescribeApiName("object_7Nhkj__c");
        findInitPersonnelArg.setDataIdList(initPersonnelIds);
        findInitPersonnelArg.setSelectFields(Lists.newArrayList("field_voO4m__c", "phone__c", "field_Mbmxk__c"));
        PaasDataFindByIds.Result findInitPersonnelResult = paasDataProxy.findByIds(Integer.parseInt(tenantId), -10000, findInitPersonnelArg);
        if (findInitPersonnelResult.getCode() != 0) {
            log.info("find by ids is error {}, {}", findInitPersonnelResult.getCode(), findInitPersonnelResult.getMessage());
            result.setErrorCode(findInitPersonnelResult.getCode());
            result.setErrorMsg(findInitPersonnelResult.getMessage());
            return result;
        }
        if (CollectionUtils.isEmpty(findInitPersonnelResult.getData().getDataList())) {
            log.info("data list is empty");
            result.setErrorCode(100);
            result.setErrorMsg("data list is empty");
            return result;
        }

        List<JSONObject> initPersonnelList = findInitPersonnelResult.getData().getDataList();
        log.info("initPersonnelList is {}", JSON.toJSONString(initPersonnelList));
        List<String> accountIds = initPersonnelList.stream()
                .map(o -> o.getString("field_Mbmxk__c"))
                .distinct()
                .collect(Collectors.toList());

        PaasDataQueryWithFields.Arg findEnterpriseRelationArg = new PaasDataQueryWithFields.Arg();
        PaasDataQueryWithFields.QueryDTO query = new PaasDataQueryWithFields.QueryDTO.Builder()
                .appendFilter(EnterpriseRelationObjApiNames.MAPPER_ACCOUNT_ID, "IN", accountIds)
                .needReturnCountNum(false)
                .needReturnQuote(false)
                .limit(1000).offset(0).build();
        findEnterpriseRelationArg.setQueryString(JSON.toJSONString(query));
        findEnterpriseRelationArg.setFieldList(Lists.newArrayList(EnterpriseRelationObjApiNames.ENTERPRISE_ACCOUNT, EnterpriseRelationObjApiNames.MAPPER_ACCOUNT_ID));
        PaasDataQueryWithFields.Result findEnterpriseRelationResult = paasDataProxy.queryWithFields(Integer.parseInt(tenantId), -10000, EnterpriseRelationObjApiNames.OBJECT_API_NAME, findEnterpriseRelationArg);
        if (findEnterpriseRelationResult.getErrCode() != 0) {
            log.info("find EnterpriseRelationObj is error {}, {}", findEnterpriseRelationResult.getErrCode(), findEnterpriseRelationResult.getErrMessage());
            result.setErrorCode(findEnterpriseRelationResult.getErrCode());
            result.setErrorMsg(findEnterpriseRelationResult.getErrMessage());
            return result;
        }
        if (CollectionUtils.isEmpty(findEnterpriseRelationResult.getResult().getQueryResult().getDataList())) {
            log.info("EnterpriseRelationObj list is empty");
            result.setErrorCode(100);
            result.setErrorMsg("EnterpriseRelationObj list is empty");
            return result;
        }

        Map<String, Integer> accountIdToEi = findEnterpriseRelationResult.getResult().getQueryResult().getDataList().stream()
                .collect(Collectors.toMap(
                        o -> o.getString(EnterpriseRelationObjApiNames.MAPPER_ACCOUNT_ID),
                        o -> eieaConverter.enterpriseAccountToId(o.getString(EnterpriseRelationObjApiNames.ENTERPRISE_ACCOUNT))
                        , (a, b) -> a));

        updatePersonnelPhone(initPersonnelList, accountIdToEi);
        return result;
    }

    private void updatePersonnelPhone(List<JSONObject> initPersonnelList, Map<String, Integer> accountIdToEi) {
        for (JSONObject initPersonnelInfo : initPersonnelList) {
            try {
                String accountId = initPersonnelInfo.getString("field_Mbmxk__c");
                if (Strings.isNullOrEmpty(accountId) || !accountIdToEi.containsKey(accountId)) {
                    log.info("accountId is empty {}", accountId);
                    continue;
                }

                String prePhone = initPersonnelInfo.getString("field_voO4m__c");
                String phone = initPersonnelInfo.getString("phone__c");
                Integer ei = accountIdToEi.get(accountId);

                PaasDataFindOne.Arg findOneArg = new PaasDataFindOne.Arg();
                findOneArg.setDescribeApiName("PersonnelObj");
                findOneArg.setSelectFields(Lists.newArrayList("_id"));
                PaasDataFindOne.FilterDTO filterDTO = new PaasDataFindOne.FilterDTO("phone", "EQ", prePhone);
                PaasDataFindOne.QueryDTO queryDTO = new PaasDataFindOne.QueryDTO();
                queryDTO.setFilters(Lists.newArrayList(filterDTO));
                findOneArg.setSearchQueryInfo(JSONObject.toJSONString(queryDTO));
                PaasDataFindOne.Result findOneResult = paasDataProxy.findOne(ei, -10000, findOneArg);
                if (findOneResult.getCode() != 0) {
                    log.info("personnel not found {}, {}", ei, prePhone);
                    continue;
                }
                JSONObject personnelInfo = findOneResult.getData().getObjectData();
                String personnelId = personnelInfo.getString("_id");
                PaasDataIncrementUpdate.Arg updatePersonnelArg = new PaasDataIncrementUpdate.Arg();
                Map<String, Object> params = Maps.newHashMap();
                params.put("_id", personnelId);
                params.put("phone", phone);
                updatePersonnelArg.setData(params);
                PaasDataIncrementUpdate.Result updatePersonnelResult = paasDataProxy.incrementUpdate(ei, -10000, "PersonnelObj", updatePersonnelArg);
                log.info("personnel update phone {}, {}, {}", ei, JSON.toJSONString(updatePersonnelArg), JSON.toJSONString(updatePersonnelResult));
            } catch (Exception e) {
                log.info("personnel update is exception {}", JSON.toJSONString(initPersonnelInfo));
                log.error("personnel update is exception", e);
            }
        }
    }

    @ServiceMethod("update_openness")
    public String updateEntityOpenness(JSONObject arg, ServiceContext serviceContext) {
        List<String> tenantIds = arg.getJSONArray("tenantIds").toJavaList(String.class);
        String apiName = arg.getString("api_name");
        Integer permission = arg.getInteger("permission");
        Integer scope = arg.getInteger("scope");
        new Thread(() -> {
            for (String tenantId : tenantIds) {
                UpdateEntityOpenness.Arg updateArg = new UpdateEntityOpenness.Arg();
                UpdateEntityOpenness.Context context = new UpdateEntityOpenness.Context();
                context.setTenantId(Integer.valueOf(tenantId));
                context.setAppId("CRM");
                context.setUserId(-10000);

                UpdateEntityOpenness.Entity entity = new UpdateEntityOpenness.Entity();
                entity.setTenantId(Integer.valueOf(tenantId));
                entity.setApiName(apiName);
                entity.setAppId("CRM");
                entity.setPermission(permission);
                entity.setScope(scope);

                updateArg.setContext(context);
                updateArg.setEntityOpenness(Lists.newArrayList(entity));

                UpdateEntityOpenness.Result result = paasDataAuthProxy.updateEntityOpenness(Integer.valueOf(tenantId), -10000, updateArg);
                log.info("update result : {}", JSON.toJSONString(result));
            }
        }).start();

        return "success";
    }

    @ServiceMethod("stop_public_employee")
    public String bulkStopPublicEmployee(JSONObject arg, ServiceContext serviceContext) {
        List<String> dataList = arg.getJSONArray("dataList").toJavaList(String.class);
        log.info("dataList : {}", dataList.size());
        TraceContext traceContext = TraceContext.get();
        Map<String, String> mdcMap = MDC.getCopyOfContextMap();
        new Thread(() -> {
            MDC.setContextMap(mdcMap);
            TraceContext._set(traceContext);
            for (String s : dataList) {
                log.info("start: {}", s);
                String[] arr = s.split("\\|");
                String tenantId = arr[0];
                String phone = arr[1];
                List<IObjectData> invalidEmployeeList = queryNeedInvalidEmployeeInfo(tenantId, phone);
                IObjectData publicEmployee = handleInvalidEmployeeId(invalidEmployeeList);
                if (publicEmployee != null) {
                    log.info("start stop public employee tenantId : {},outer_tenantId : {},phone: {}", tenantId, publicEmployee.getOutTenantId(), phone);
                    stopPublicEmployee(tenantId, publicEmployee);
                    invalidAndRemovePublicEmployee(Integer.valueOf(tenantId), publicEmployee.getId(), publicEmployee.getDescribeApiName());

                } else {
                    log.info("public employee is null tenantId : {},phone : {}", tenantId, phone);
                }

            }
        }).start();

        return "success";
    }

    @ServiceMethod("stop_enterprise_relation")
    public String bulkStopEnterpriseRelation(JSONObject arg, ServiceContext serviceContext) {
        List<String> dataList = arg.getJSONArray("dataList").toJavaList(String.class);
        TraceContext traceContext = TraceContext.get();
        Map<String, String> mdcMap = MDC.getCopyOfContextMap();
        new Thread(() -> {
            MDC.setContextMap(mdcMap);
            TraceContext._set(traceContext);
            for (String s : dataList) {
                log.info("start: {}", s);
                String[] arr = s.split("\\|");
                String tenantId = arr[0];
                String accountId = arr[1];
                IObjectData objectData = queryEnterpriseAccountByAccountId(tenantId, accountId);
                if (objectData == null) {
                    log.info("enterprise relation is null tenantId : {},accountId : {}", tenantId, accountId);
                    continue;
                }
                stopEnterpriseRelation(tenantId, objectData);
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                invalidAndRemovePublicEmployee(Integer.valueOf(tenantId), objectData.getId(), "EnterpriseRelationObj");
                invalidAndRemovePublicEmployee(Integer.valueOf(tenantId), accountId, "AccountObj");
            }

        }).start();

        return "success";
    }

    @ServiceMethod("recover_default_account")
    public String recoverDefaultAccount(JSONObject arg, ServiceContext serviceContext) {
        new Thread(MonitorTaskWrapper.wrap(() -> {
            try {
                recoverDefaultAccount(arg);
            } catch (Exception e) {
                log.error("invalidDefaultAccount exception", e);
            }
        })).start();
        return "success";
    }

    private void recoverDefaultAccount(JSONObject arg) {
        Integer pageNumber = arg.getInteger("pageNumber");
        Integer pageSize = arg.getInteger("pageSize");
        Integer tenantId = arg.getInteger("tenantId");

        for (int i = 0; i < pageNumber; i++) {
            try {
                List<JSONObject> findInvalidDefaultAccount = findInvalidDefaultAccount(pageSize, tenantId);
                if (CollectionUtils.isEmpty(findInvalidDefaultAccount)) {
                    return;
                }
                List<String> ids = findInvalidDefaultAccount.stream()
                        .map(o -> o.getString("_id"))
                        .collect(Collectors.toList());
                for (List<String> partition : Lists.partition(ids, 20)) {
                    bulkRecoverAccount(tenantId, partition);
                }
            } catch (Exception e) {
                log.error("recoverDefaultAccount error", e);
            }
        }
    }

    private List<JSONObject> findInvalidDefaultAccount(Integer pageSize, Integer tenantId) {
        PaasDataFindByQuery.Arg arg = new PaasDataFindByQuery.Arg();
        arg.setDescribeApiName("AccountObj");
        PaasDataFindByQuery.FilterDTO lifeStatusFilter = new PaasDataFindByQuery.FilterDTO("life_status", "EQ", "invalid");
        PaasDataFindByQuery.FilterDTO recordTypeFilter = new PaasDataFindByQuery.FilterDTO("record_type", "EQ", "default__c");
        PaasDataFindByQuery.QueryDTO queryDTO = new PaasDataFindByQuery.QueryDTO();
        queryDTO.setFilters(Lists.newArrayList(lifeStatusFilter, recordTypeFilter));
        queryDTO.setLimit(pageSize);
        arg.setSearchQueryInfo(JSON.toJSONString(queryDTO));
        arg.setSelectFields(Lists.newArrayList("_id"));
        arg.setIncludeInvalid(true);
        log.info("invalidAccount findByQuery arg : {}", JSON.toJSONString(arg));
        PaasDataFindByQuery.Result result = paasDataProxy.findByQuery(tenantId, -10000, arg);
        log.info("invalidAccount findByQuery result : {}", JSON.toJSONString(result));
        if (result.getCode() != 0) {
            return null;
        }
        if (Objects.isNull(result.getData()) || Objects.isNull(result.getData().getQueryResult())) {
            return null;
        }
        return result.getData().getQueryResult().getDataList();
    }

    @ServiceMethod("recover_account")
    public String recoverAccount(JSONObject arg, ServiceContext serviceContext) {
        new Thread(MonitorTaskWrapper.wrap(() -> {
            try {
                recoverAccount(arg);
            } catch (Exception e) {
                log.error("recoverAccount exception", e);
            }
        })).start();
        return "success";
    }

    private void recoverAccount(JSONObject arg) {
        Integer pageNumber = arg.getInteger("pageNumber");
        Integer pageSize = arg.getInteger("pageSize");
        String recover = arg.getString("recover");
        List<String> ids = arg.getJSONArray("ids").toJavaList(String.class);
        for (int i = 1; i <= pageNumber; i++) {
            recoverAccount(pageSize, i, recover, ids);
        }
    }

    private void recoverAccount(Integer pageSize, Integer pageNumber, String recover, List<String> ids) {
        //查询失败的同步记录
        List<JSONObject> dealerOrderObjSyncFailInfo = findDealerOrderObjSyncFailInfo(pageSize, pageNumber, ids);
        if (CollectionUtils.isEmpty(dealerOrderObjSyncFailInfo)) {
            return;
        }
        //按下游企业分组
        Map<String, List<JSONObject>> sourceTenantIdMap = dealerOrderObjSyncFailInfo.stream()
                .collect(Collectors.groupingBy(o -> o.getString("sourceTenantId"),
                        Collectors.mapping(o -> o, Collectors.toList())));

        for (Map.Entry<String, List<JSONObject>> entry : sourceTenantIdMap.entrySet()) {
            String sourceTenantId = entry.getKey();
            List<JSONObject> list = entry.getValue();
            //查询销售订单
            List<String> sourceDataIds = list.stream()
                    .map(o -> o.getString("sourceDataId"))
                    .collect(Collectors.toList());
            List<JSONObject> salesOrders = findSalesOrderObj(Integer.parseInt(sourceTenantId), sourceDataIds, Lists.newArrayList("account_id"));
            if (CollectionUtils.isEmpty(salesOrders)) {
                continue;
            }
            //查询作废的客户
            List<String> accountIds = salesOrders.stream()
                    .map(o -> o.getString("account_id"))
                    .collect(Collectors.toList());
            List<JSONObject> invalidAccounts = findInvalidAccount(0, 0, accountIds);
            if (CollectionUtils.isNotEmpty(invalidAccounts) && "y".equals(recover)) {
                bulkRecoverAccount(777421,
                        invalidAccounts.stream()
                                .map(o -> o.getString("_id")).collect(Collectors.toList()));
            }
        }
    }

    private void bulkRecoverAccount(Integer tenantId, List<String> ids) {
        PaasDataBulkRecover.Arg arg = new PaasDataBulkRecover.Arg();
        arg.setDescribeApiName("AccountObj");
        arg.setIdList(ids);
        log.info("bulkRecoverAccount arg : {}", JSON.toJSONString(arg));
        PaasDataBulkRecover.Result result = paasDataProxy.bulkRecover(tenantId, -10000, "AccountObj", arg);
        log.info("bulkRecoverAccount result : {}", JSON.toJSONString(result));
    }

    private List<JSONObject> findSalesOrderObj(Integer tenantId, List<String> ids, List<String> fields) {
        PaasDataFindByIds.Arg arg = new PaasDataFindByIds.Arg();
        arg.setDataIdList(ids);
        arg.setDescribeApiName("SalesOrderObj");
        arg.setSelectFields(fields);
        log.info("findSalesOrderObj arg : {}", JSON.toJSONString(arg));
        PaasDataFindByIds.Result result = paasDataProxy.findByIds(tenantId, -10000, arg);
        log.info("findSalesOrderObj result : {}", JSON.toJSONString(result));
        if (result.getCode() != 0) {
            return null;
        }
        if (Objects.isNull(result.getData()) || Objects.isNull(result.getData().getDataList())) {
            return null;
        }
        return result.getData().getDataList();
    }

    private List<JSONObject> findDealerOrderObjSyncFailInfo(Integer pageSize, Integer pageNumber, List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            ListByPloyDetailId.Arg arg = new ListByPloyDetailId.Arg();
            arg.setObjectApiName("DealerOrderObj");
            arg.setPageSize(pageSize);
            arg.setPageNumber(pageNumber);
            arg.setSyncPloyType(1);
            arg.setStatus(2);
            log.info("findDealerOrderObjSyncFailInfo arg {}", JSON.toJSONString(arg));
            ListByPloyDetailId.Result result = syncDataAllProxy.listByPloyDetailId(777421, 1000, arg);
            log.info("findDealerOrderObjSyncFailInfo result {}", JSON.toJSONString(result));
            if (result.getErrCode() != 0) {
                log.info("findDealerOrderObjSyncFailInfo is error {}, {}", result.getErrCode(), result.getErrMsg());
                return null;
            }
            return result.getData().getSyncDataMappingList();
        } else {
            List<JSONObject> dataList = Lists.newArrayList();
            for (String id : ids) {
                ListByPloyDetailId.Arg arg = new ListByPloyDetailId.Arg();
                arg.setObjectApiName("DealerOrderObj");
                arg.setPageSize(10);
                arg.setPageNumber(1);
                arg.setSyncPloyType(1);
                arg.setStatus(2);
                arg.setSearchText(id);
                log.info("findDealerOrderObjSyncFailInfo arg {}", JSON.toJSONString(arg));
                ListByPloyDetailId.Result result = syncDataAllProxy.listByPloyDetailId(777421, 1000, arg);
                log.info("findDealerOrderObjSyncFailInfo result {}", JSON.toJSONString(result));
                if (result.getErrCode() != 0) {
                    log.info("findDealerOrderObjSyncFailInfo is error {}, {}", result.getErrCode(), result.getErrMsg());
                    continue;
                }
                if (CollectionUtils.isEmpty(result.getData().getSyncDataMappingList())) {
                    continue;
                }
                dataList.addAll(result.getData().getSyncDataMappingList());
            }
            return dataList;
        }
    }

    @ServiceMethod("delete_account")
    public String deleteAccount(JSONObject arg, ServiceContext serviceContext) {
        new Thread(MonitorTaskWrapper.wrap(() -> {
            try {
                deleteAccount(arg);
            } catch (Exception e) {
                log.error("invalidAccount exception", e);
            }
        })).start();
        return "success";
    }

    private void deleteAccount(JSONObject arg) {
        List<JSONObject> dataList = arg.getJSONArray("dataList").toJavaList(JSONObject.class);
        Map<Integer, List<String>> tiToAccountIds = Maps.newHashMap();
        for (JSONObject data : dataList) {
            try {
                handleData(data, tiToAccountIds);
            } catch (Exception e) {
                log.info("deleteAccount exception", e);
                log.info("deleteAccount cur data : {}", JSON.toJSONString(data));
            }
        }
        log.info("deleteAccount tiToAccountIds : {}", JSON.toJSONString(tiToAccountIds));
        for (Map.Entry<Integer, List<String>> entry : tiToAccountIds.entrySet()) {
            Integer tenantId = entry.getKey();
            List<String> accountIds = entry.getValue();
            if (CollectionUtils.isEmpty(accountIds)) {
                continue;
            }
            deleteData(tenantId, accountIds);
        }
    }

    private void handleData(JSONObject data, Map<Integer, List<String>> tiToAccountIds) {
        String uId = data.getString("uId");
        String dId = data.getString("dId");
        Integer dTId = data.getInteger("dTId");

        Boolean hasSuccessSyncInfo = hasSuccessSyncInfo(uId, dId, String.valueOf(dTId));
        if (hasSuccessSyncInfo) {
            return;
        }

        Boolean uBoolean = hasData(777421, "DealerOrderObj", "account_id", uId) || hasData(777421, "ActivityAgreement__c", "field_n6t3d__c", uId);
        Boolean dBoolean = hasData(dTId, "SalesOrderObj", "account_id", dId) || hasData(dTId, "ActivityAgreement__c", "field_n6t3d__c", dId);

        if (uBoolean && dBoolean) {
            //都有数据更新下游客户 mutual_benefit_code__c 赋值给 account_no__c
            updateAccount(dTId, dId);
            log.info("updateAccount data info : {}", JSON.toJSONString(data));
        } else if (!uBoolean && !dBoolean) {
            //都没有数据 删除下游客户
            putData(tiToAccountIds, dTId, dId);
            log.info("putData info : {}, {}", 777421, uId);
        } else if (!uBoolean) {
            putData(tiToAccountIds, 777421, uId);
            log.info("putData info : {}, {}", dTId, dId);
        } else {
            putData(tiToAccountIds, dTId, dId);
            log.info("putData info : {}, {}", 777421, uId);
        }
    }

    private Boolean hasSuccessSyncInfo(String uId, String dId, String dTId) {
        //查询采集记录
        QuerySyncDataMapping.Arg queryCollectMappingArg = new QuerySyncDataMapping.Arg();
        QuerySyncDataMapping.ArgBody queryCollectMappingArgBody = new QuerySyncDataMapping.ArgBody();
        queryCollectMappingArgBody.setSourceDataIds(Lists.newArrayList(dId));
        queryCollectMappingArgBody.setSourceTenantId(dTId);
        queryCollectMappingArgBody.setSourceObjectApiName("AccountObj");
        queryCollectMappingArgBody.setDestDataIds(Lists.newArrayList(uId));
        queryCollectMappingArgBody.setDestTenantId("777421");
        queryCollectMappingArgBody.setDestObjectApiName("AccountObj");
        queryCollectMappingArg.setArg(queryCollectMappingArgBody);

        log.info("queryCollectMappingArg : {}", JSON.toJSONString(queryCollectMappingArg));
        QuerySyncDataMapping.Result queryCollectMappingResult = syncDataAllProxy.querySyncDataMapping(777421, -10000, queryCollectMappingArg);
        log.info("queryCollectMappingResult : {}", JSON.toJSONString(queryCollectMappingResult));
        if (queryCollectMappingResult.getErrCode() != 0) {
            log.info("queryCollectMappingResult is error {}, {}", queryCollectMappingResult.getErrCode(), queryCollectMappingResult.getErrMsg());
            throw new RuntimeException(queryCollectMappingResult.getErrMsg());
        }
        if (CollectionUtils.isNotEmpty(queryCollectMappingResult.getData())) {
            Integer lastSyncStatus = queryCollectMappingResult.getData().get(0).getInteger("lastSyncStatus");
            if (Objects.equals(lastSyncStatus, 6)) {
                return true;
            }
        }

        //查询下发记录
        QuerySyncDataMapping.Arg queryIssueMappingArg = new QuerySyncDataMapping.Arg();
        QuerySyncDataMapping.ArgBody queryIssueMappingArgBody = new QuerySyncDataMapping.ArgBody();
        queryIssueMappingArgBody.setSourceDataIds(Lists.newArrayList(uId));
        queryIssueMappingArgBody.setSourceTenantId("777421");
        queryIssueMappingArgBody.setSourceObjectApiName("AccountObj");
        queryIssueMappingArgBody.setDestDataIds(Lists.newArrayList(dId));
        queryIssueMappingArgBody.setDestTenantId(dTId);
        queryIssueMappingArgBody.setDestObjectApiName("AccountObj");

        queryIssueMappingArg.setArg(queryIssueMappingArgBody);
        log.info("queryIssueMappingArg : {}", JSON.toJSONString(queryIssueMappingArg));
        QuerySyncDataMapping.Result queryIssueMappingResult = syncDataAllProxy.querySyncDataMapping(777421, -10000, queryIssueMappingArg);
        log.info("queryIssueMappingResult : {}", JSON.toJSONString(queryIssueMappingResult));
        if (queryIssueMappingResult.getErrCode() != 0) {
            log.info("queryIssueMappingResult is error {}, {}", queryIssueMappingResult.getErrCode(), queryIssueMappingResult.getErrMsg());
            throw new RuntimeException(queryIssueMappingResult.getErrMsg());
        }
        if (CollectionUtils.isNotEmpty(queryIssueMappingResult.getData())) {
            Integer lastSyncStatus = queryIssueMappingResult.getData().get(0).getInteger("lastSyncStatus");
            if (Objects.equals(lastSyncStatus, 6)) {
                return true;
            }
        }
        return false;
    }

    private void putData(Map<Integer, List<String>> tiToAccountIds, Integer tenantId, String accountId) {
        if (tiToAccountIds.containsKey(tenantId)) {
            tiToAccountIds.get(tenantId).add(accountId);
        } else {
            tiToAccountIds.put(tenantId, Lists.newArrayList(accountId));
        }
    }

    private void updateAccount(Integer tenantId, String accountId) {
        PaasDataFindById.Arg arg = new PaasDataFindById.Arg();
        arg.setDataId(accountId);
        arg.setDescribeApiName("AccountObj");
        arg.setSelectFields(Lists.newArrayList("mutual_benefit_code__c"));
        log.info("updateAccount arg : {}", arg);
        PaasDataFindById.Result result = paasDataProxy.findById(tenantId, -10000, arg);
        log.info("updateAccount result : {}", JSON.toJSONString(result));
        if (result.getCode() != 0) {
            return;
        }
        if (Objects.isNull(result.getData()) || Objects.isNull(result.getData().getObjectData())) {
            return;
        }
        if (Objects.isNull(result.getData().getObjectData().get("mutual_benefit_code__c"))) {
            log.info("updateAccount mutual_benefit_code__c is null {} , {}", tenantId, accountId);
            return;
        }
        String accountNo = result.getData().getObjectData().getString("mutual_benefit_code__c");
        JSONObject updateData = new JSONObject();
        updateData.put("account_no__c", accountNo);
        updateData.put("_id", accountId);
        PaasDataIncrementUpdate.Arg updateArg = new PaasDataIncrementUpdate.Arg();
        updateArg.setData(updateData);
        log.info("updateAccount updateArg : {}", updateArg);
        PaasDataIncrementUpdate.Result updateResult = paasDataProxy.incrementUpdate(tenantId, -10000, "AccountObj", updateArg);
        log.info("updateAccount updateResult : {}", JSON.toJSONString(updateResult));
    }

    private void deleteData(Integer tenantId, List<String> accountIds) {
        //查询下游客户互联企业
        List<JSONObject> relationObjByAccountId = findEnterpriseRelationObjByAccountId(tenantId, accountIds, Lists.newArrayList(CommonApiNames.ID));
        if (CollectionUtils.isNotEmpty(relationObjByAccountId)) {
            //停用互联企业
            for (JSONObject object : relationObjByAccountId) {
                String objectId = object.getString(CommonApiNames.ID);
                stopEnterpriseRelation(tenantId, objectId);
            }
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                log.error("stopEnterpriseRelation thread sleep error", e);
            }
            //作废互联企业
            batchInvalidData(tenantId, "EnterpriseRelationObj",
                    relationObjByAccountId.stream().map(o -> o.getString(CommonApiNames.ID)).collect(Collectors.toList()));
            batchDeleteData(tenantId, "EnterpriseRelationObj",
                    relationObjByAccountId.stream().map(o -> o.getString(CommonApiNames.ID)).collect(Collectors.toList()));
        }
        //查询联系人
        List<JSONObject> contactObjByAccountId = findContactObjByAccountId(tenantId, accountIds, Lists.newArrayList(CommonApiNames.ID));
        //作废联系人
        if (CollectionUtils.isNotEmpty(contactObjByAccountId)) {
            batchInvalidData(tenantId, "ContactObj",
                    contactObjByAccountId.stream().map(o -> o.getString(CommonApiNames.ID)).collect(Collectors.toList()));
            batchDeleteData(tenantId, "ContactObj",
                    contactObjByAccountId.stream().map(o -> o.getString(CommonApiNames.ID)).collect(Collectors.toList()));
        }
        //查询客户
        List<JSONObject> accountObjByAccountId = findAccountObjByIds(tenantId, accountIds, Lists.newArrayList(CommonApiNames.ID));
        if (CollectionUtils.isNotEmpty(accountObjByAccountId)) {
            //作废客户
            batchInvalidData(tenantId, "AccountObj",
                    accountObjByAccountId.stream().map(o -> o.getString(CommonApiNames.ID)).collect(Collectors.toList()));
            batchDeleteData(tenantId, "AccountObj",
                    accountObjByAccountId.stream().map(o -> o.getString(CommonApiNames.ID)).collect(Collectors.toList()));
        }
    }

    private Boolean hasData(Integer tenantId, String objApiName, String filedApiName, String accountId) {
        PaasDataFindOne.Arg arg = new PaasDataFindOne.Arg();
        arg.setDescribeApiName(objApiName);
        arg.setSelectFields(Lists.newArrayList("_id"));
        PaasDataFindOne.QueryDTO queryDTO = new PaasDataFindOne.QueryDTO();
        queryDTO.setFilters(Lists.newArrayList(new PaasDataFindOne.FilterDTO(filedApiName, "EQ", accountId)));
        arg.setSearchQueryInfo(JSONObject.toJSONString(queryDTO));
        log.info("hasData arg : {}", JSON.toJSONString(arg));
        PaasDataFindOne.Result result = paasDataProxy.findOne(tenantId, -10000, arg);
        log.info("hasData result : {}", JSON.toJSONString(result));
        if (result.getCode() != 0) {
            throw new RuntimeException("hasData error " + result.getCode() + " " + result.getMessage());
        }
        if (Objects.isNull(result.getData()) || Objects.isNull(result.getData().getObjectData())) {
            return false;
        }
        return true;
    }

    @ServiceMethod("invalid_account_by_type")
    public String invalidAccountByLocation(JSONObject arg, ServiceContext serviceContext) {
        String type = arg.getString("type");
        new Thread(MonitorTaskWrapper.wrap(() -> {
            try {
                if ("1".equals(type)) {
                    invalidAccountByLocation(arg);
                } else if ("2".equals(type)) {
                    invalidAccountByCustom(arg);
                }
            } catch (Exception e) {
                log.error("invalid_account_by_type exception", e);
            }
        })).start();
        return "success";
    }

    private void invalidAccountByCustom(JSONObject arg) {
        Integer limit = arg.getInteger("limit");
        Integer start = arg.getInteger("start");
        Integer end = arg.getInteger("end");
        List<JSONObject> dataList = arg.getJSONArray("dataList").toJavaList(JSONObject.class);

        ThreadPoolExecutor pool = new ThreadPoolExecutor(
                10, 10, 60, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(500),
                new ThreadFactoryBuilder()
                        .setDaemon(true)
                        .setNameFormat("invalidAccountByCustom-%s")
                        .build(),
                new ThreadPoolExecutor.CallerRunsPolicy());

        if (CollectionUtils.isNotEmpty(dataList)) {
            for (JSONObject data : dataList) {
                pool.execute(MonitorTaskWrapper.wrap(() -> {
                    try {
                        handAccount(data);
                    } catch (Exception e) {
                        log.info("invalidAccountByCustom handAccount exception {}", data);
                        log.error("invalidAccountByCustom handAccount exception", e);
                    }
                }));
            }
        } else {
            for (int i = start; i <= end; i++) {
                List<JSONObject> accountData = findAccountByCustom(limit, i * limit);
                if (CollectionUtils.isEmpty(accountData)) {
                    return;
                }
                pool.execute(MonitorTaskWrapper.wrap(() -> {
                    for (JSONObject account : accountData) {
                        try {
                            handAccount(account);
                        } catch (Exception e) {
                            log.info("invalidAccountByCustom handAccount exception {}", account);
                            log.error("invalidAccountByCustom handAccount exception", e);
                        }
                    }
                }));
            }
        }
        pool.shutdown();
    }

    private void invalidAccountByLocation(JSONObject arg) {
        Integer limit = arg.getInteger("limit");
        Integer start = arg.getInteger("start");
        Integer end = arg.getInteger("end");
        List<JSONObject> dataList = arg.getJSONArray("dataList").toJavaList(JSONObject.class);

        ThreadPoolExecutor pool = new ThreadPoolExecutor(
                10, 10, 60, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(500),
                new ThreadFactoryBuilder()
                        .setDaemon(true)
                        .setNameFormat("invalidAccountByLocation-%s")
                        .build(),
                new ThreadPoolExecutor.CallerRunsPolicy());

        if (CollectionUtils.isNotEmpty(dataList)) {
            for (JSONObject data : dataList) {
                pool.execute(MonitorTaskWrapper.wrap(() -> {
                    try {
                        handAccount(data);
                    } catch (Exception e) {
                        log.info("invalidAccountByLocation handAccount exception {}", data);
                        log.error("invalidAccountByLocation handAccount exception", e);
                    }
                }));
            }
        } else {
            for (int i = start; i <= end; i++) {
                List<JSONObject> accountData = findAccountByLocation(limit, i * limit);
                if (CollectionUtils.isEmpty(accountData)) {
                    return;
                }
                pool.execute(MonitorTaskWrapper.wrap(() -> {
                    for (JSONObject account : accountData) {
                        try {
                            handAccount(account);
                        } catch (Exception e) {
                            log.info("invalidAccountByLocation handAccount exception {}", account);
                            log.error("invalidAccountByLocation handAccount exception", e);
                        }
                    }
                }));
            }
        }

        pool.shutdown();
    }

    private List<JSONObject> findAccountByCustom(Integer limit, Integer offset) {
        PaasDataQueryWithFields.Arg findAccountArg = new PaasDataQueryWithFields.Arg();
        PaasDataQueryWithFields.QueryDTO queryDTO = new PaasDataQueryWithFields.QueryDTO.Builder()
                .limit(limit).offset(offset).build();

        queryDTO.getFilters().add(new PaasDataQueryWithFields.FilterDTO("life_status", "EQ", "normal"));
        findAccountArg.setQueryString(JSON.toJSONString(queryDTO));
        findAccountArg.setFieldList(Lists.newArrayList(CommonApiNames.ID, "field_mr4kX__c"));
        log.info("findAccountByCustom findAccountArg : {}", findAccountArg);
        PaasDataQueryWithFields.Result findAccountResult = paasDataProxy.queryWithFields(777421, -10000, "object_nj803__c", findAccountArg);
        log.info("findAccountByCustom findAccountResult : {}", findAccountResult);
        if (findAccountResult.getErrCode() != 0) {
            return null;
        }
        if (Objects.isNull(findAccountResult.getResult()) || Objects.isNull(findAccountResult.getResult().getQueryResult())) {
            return null;
        }
        List<JSONObject> dataList = findAccountResult.getResult().getQueryResult().getDataList();
        List<String> accountIds = dataList.stream()
                .map(o -> o.getString("field_mr4kX__c"))
                .collect(Collectors.toList());

        PaasDataFindByIds.Arg arg = new PaasDataFindByIds.Arg();
        arg.setDescribeApiName("AccountObj");
        arg.setDataIdList(accountIds);
        arg.setSelectFields(Lists.newArrayList(CommonApiNames.ID, "sellers__c"));
        log.info("findAccountByCustom arg : {}", arg);
        PaasDataFindByIds.Result result = paasDataProxy.findByIds(777421, -10000, arg);
        log.info("findAccountByCustom result : {}", result);
        if (result.getCode() != 0) {
            return null;
        }
        if (Objects.isNull(result.getData()) || Objects.isNull(result.getData().getDataList())) {
            return null;
        }
        return result.getData().getDataList();
    }

    private void handAccount(JSONObject account) {
        String accountId = account.getString("_id");
        List<JSONObject> sellersData = Lists.newArrayList();
        if (account.containsKey("sellers__c") && Objects.nonNull(account.get("sellers__c"))) {
            List<String> sellers = account.getJSONArray("sellers__c").toJavaList(String.class);
            if (CollectionUtils.isNotEmpty(sellers)) {
                sellersData = findSellersData(sellers);
            }
        }
        if (CollectionUtils.isEmpty(sellersData)) {
            boolean upHasData = hasData(777421, "DealerOrderObj", "account_id", accountId)
                    || hasData(777421, "ActivityAgreement__c", "field_n6t3d__c", accountId);
            if (!upHasData) {
                //查询联系人
                List<JSONObject> contactObjByAccountId = findContactObjByAccountId(777421, Lists.newArrayList(accountId), Lists.newArrayList(CommonApiNames.ID));
                //作废联系人
                if (CollectionUtils.isNotEmpty(contactObjByAccountId)) {
                    batchInvalidData(777421, "ContactObj",
                            contactObjByAccountId.stream().map(o -> o.getString(CommonApiNames.ID)).collect(Collectors.toList()));
                }
                batchInvalidData(777421, "AccountObj", Lists.newArrayList(accountId));
            }
            return;
        }
        boolean downHasData = false;
        for (JSONObject sellersDatum : sellersData) {
            Integer tenantId = sellersDatum.getInteger("enterprise_account");
            boolean hasData = hasData(tenantId, "SalesOrderObj", "account_id", accountId)
                    || hasData(tenantId, "ActivityAgreement__c", "field_n6t3d__c", accountId);
            if (hasData) {
                downHasData = true;
                break;
            }
        }
        if (downHasData) {
            return;
        }
        for (JSONObject sellersDatum : sellersData) {
            Integer tenantId = sellersDatum.getInteger("enterprise_account");
            //查询下游客户互联企业
            List<JSONObject> relationObjByAccountId = findEnterpriseRelationObjByAccountId(tenantId, Lists.newArrayList(accountId), Lists.newArrayList(CommonApiNames.ID));
            if (CollectionUtils.isNotEmpty(relationObjByAccountId)) {
                //停用互联企业
                for (JSONObject object : relationObjByAccountId) {
                    String objectId = object.getString(CommonApiNames.ID);
                    stopEnterpriseRelation(tenantId, objectId);
                }
                try {
                    Thread.sleep(3000);
                } catch (InterruptedException e) {
                    log.error("stopEnterpriseRelation thread sleep error", e);
                }
                //作废互联企业
                batchInvalidData(tenantId, "EnterpriseRelationObj",
                        relationObjByAccountId.stream().map(o -> o.getString(CommonApiNames.ID)).collect(Collectors.toList()));
            }
            //查询联系人
            List<JSONObject> contactObjByAccountId = findContactObjByAccountId(tenantId, Lists.newArrayList(accountId), Lists.newArrayList(CommonApiNames.ID));
            //作废联系人
            if (CollectionUtils.isNotEmpty(contactObjByAccountId)) {
                batchInvalidData(tenantId, "ContactObj",
                        contactObjByAccountId.stream().map(o -> o.getString(CommonApiNames.ID)).collect(Collectors.toList()));
            }
            //查询客户
            List<JSONObject> accountObjByAccountId = findAccountObjByIds(tenantId, Lists.newArrayList(accountId), Lists.newArrayList(CommonApiNames.ID));
            if (CollectionUtils.isNotEmpty(accountObjByAccountId)) {
                //作废客户
                batchInvalidData(tenantId, "AccountObj",
                        accountObjByAccountId.stream().map(o -> o.getString(CommonApiNames.ID)).collect(Collectors.toList()));
            }
        }

        //查询联系人
        List<JSONObject> contactObjByAccountId = findContactObjByAccountId(777421, Lists.newArrayList(accountId), Lists.newArrayList(CommonApiNames.ID));
        //作废联系人
        if (CollectionUtils.isNotEmpty(contactObjByAccountId)) {
            batchInvalidData(777421, "ContactObj",
                    contactObjByAccountId.stream().map(o -> o.getString(CommonApiNames.ID)).collect(Collectors.toList()));
        }
        //作废客户
        batchInvalidData(777421, "AccountObj", Lists.newArrayList(accountId));
    }

    @ServiceMethod("stop_account_flow")
    public String stopAccountFLow(JSONObject arg, ServiceContext serviceContext) {
        new Thread(MonitorTaskWrapper.wrap(() -> {
            try {
                stopAccountFLow(arg);
            } catch (Exception e) {
                log.error("stop_account_flow exception", e);
            }
        })).start();
        return "success";
    }

    private void stopAccountFLow(JSONObject arg) {
        List<JSONObject> dataList = arg.getJSONArray("dataList").toJavaList(JSONObject.class);
        boolean isNeedDelete = arg.getBoolean("isNeedDelete");
        for (JSONObject object : dataList) {
            try {
                flow(object.getInteger("tenantId"), object.getString("accountId"), isNeedDelete);
            } catch (Exception e) {
                log.error("stopAccountFLow exception", e);
                log.info("stopAccountFLow exception {}", object);
            }
        }
    }

    private void flow(Integer tenantId, String accountId, Boolean isNeedDelete) {
        //查询下游客户互联企业
        List<JSONObject> relationObjByAccountId = findEnterpriseRelationObjByAccountId(tenantId, Lists.newArrayList(accountId), Lists.newArrayList(CommonApiNames.ID));
        if (CollectionUtils.isNotEmpty(relationObjByAccountId)) {
            JSONObject hlData = relationObjByAccountId.get(0);
            String hlId = hlData.getString(CommonApiNames.ID);
            //停用互联企业
            stopEnterpriseRelation(tenantId, hlData.getString(CommonApiNames.ID));
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                log.error("flow stopEnterpriseRelation thread sleep error", e);
            }
            //作废互联企业
            batchInvalidData(tenantId, "EnterpriseRelationObj", Lists.newArrayList(hlId));
            if (isNeedDelete) {
                batchDeleteData(tenantId, "EnterpriseRelationObj", Lists.newArrayList(hlId));
            }

        }
        //查询联系人
        List<JSONObject> contactObjByAccountId = findContactObjByAccountId(tenantId, Lists.newArrayList(accountId), Lists.newArrayList(CommonApiNames.ID));
        //作废联系人
        if (CollectionUtils.isNotEmpty(contactObjByAccountId)) {
            batchInvalidData(tenantId, "ContactObj", Lists.newArrayList(contactObjByAccountId.get(0).getString(CommonApiNames.ID)));
            if (isNeedDelete) {
                batchDeleteData(tenantId, "ContactObj", Lists.newArrayList(contactObjByAccountId.get(0).getString(CommonApiNames.ID)));
            }
        }
        //查询客户
        List<JSONObject> accountObjByAccountId = findAccountObjByIds(tenantId, Lists.newArrayList(accountId), Lists.newArrayList(CommonApiNames.ID));
        if (CollectionUtils.isNotEmpty(accountObjByAccountId)) {
            //作废客户
            batchInvalidData(tenantId, "AccountObj", Lists.newArrayList(accountObjByAccountId.get(0).getString(CommonApiNames.ID)));
            if (isNeedDelete) {
                batchDeleteData(tenantId, "AccountObj", Lists.newArrayList(accountObjByAccountId.get(0).getString(CommonApiNames.ID)));
            }
        }
    }

    private List<JSONObject> findSellersData(List<String> accountIds) {
        PaasDataQueryWithFields.Arg arg = new PaasDataQueryWithFields.Arg();
        PaasDataQueryWithFields.QueryDTO queryDTO = new PaasDataQueryWithFields.QueryDTO.Builder()
                .limit(10).offset(0).build();
        queryDTO.setFilters(Lists.newArrayList(new PaasDataQueryWithFields.FilterDTO("mapper_account_id", "IN", accountIds)));
        arg.setQueryString(JSON.toJSONString(queryDTO));
        arg.setFieldList(Lists.newArrayList(CommonApiNames.ID, "enterprise_account"));
        log.info("findSellersTenantId arg : {}", arg);
        PaasDataQueryWithFields.Result result = paasDataProxy.queryWithFields(777421, -10000, "EnterpriseRelationObj", arg);
        log.info("findSellersTenantId result : {}", JSON.toJSONString(result));
        if (result.getErrCode() != 0) {
            return null;
        }
        if (Objects.isNull(result.getResult()) || Objects.isNull(result.getResult().getQueryResult())) {
            return null;
        }
        return result.getResult().getQueryResult().getDataList();
    }

    private List<JSONObject> findAccountByLocation(Integer limit, Integer offset) {
        PaasDataQueryWithFields.Arg findAccountArg = new PaasDataQueryWithFields.Arg();
        PaasDataQueryWithFields.QueryDTO queryDTO = new PaasDataQueryWithFields.QueryDTO.Builder()
                .appendOrder(new PaasDataQueryWithFields.OrderDTO("sellers__c", true))
                .limit(limit).offset(offset).build();
        for (String field : Lists.newArrayList("location", "province", "city", "district")) {
            queryDTO.getWheres().add(new PaasDataQueryWithFields.WhereDTO.Builder()
                    .appendFilter(field, "IS", null)
                    .appendFilter("record_type", "EQ", "default__c")
                    .build());
        }

        findAccountArg.setQueryString(JSON.toJSONString(queryDTO));
        findAccountArg.setFieldList(Lists.newArrayList(CommonApiNames.ID, "sellers__c"));
        log.info("findAccountByLocation arg : {}", findAccountArg);
        PaasDataQueryWithFields.Result result = paasDataProxy.queryWithFields(777421, -10000, "AccountObj", findAccountArg);
        log.info("findAccountByLocation result : {}", result);
        if (result.getErrCode() != 0) {
            return null;
        }
        if (Objects.isNull(result.getResult()) || Objects.isNull(result.getResult().getQueryResult())) {
            return null;
        }
        return result.getResult().getQueryResult().getDataList();
    }

    @ServiceMethod("invalid_account")
    public String invalidAccount(JSONObject arg, ServiceContext serviceContext) {
        new Thread(MonitorTaskWrapper.wrap(() -> {
            try {
                invalidAccount(arg);
            } catch (Exception e) {
                log.error("invalidAccount exception", e);
            }
        })).start();
        return "success";
    }

    private void invalidAccount(JSONObject arg) {
        Integer corePoolSize = arg.getInteger("corePoolSize");
        Integer maxPoolSize = arg.getInteger("maxPoolSize");
        ThreadPoolExecutor pool = new ThreadPoolExecutor(
                corePoolSize, maxPoolSize, 60, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(500),
                new ThreadFactoryBuilder()
                        .setDaemon(true)
                        .setNameFormat("invalidAccount_data-%s")
                        .build(),
                new ThreadPoolExecutor.CallerRunsPolicy());

        Integer limit = arg.getInteger("limit");
        Integer page = arg.getInteger("page");
        String delete = arg.getString("delete");
        List<String> ids = arg.getJSONArray("ids").toJavaList(String.class);
        for (int i = 0; i <= page; i++) {
            int finalI = i;
            pool.execute(MonitorTaskWrapper.wrap(() -> {
                try {
                    invalidAccount(limit, finalI * limit, delete, ids);
                } catch (Exception e) {
                    log.error("invalidAccount is error ", e);
                }
            }));
        }
        pool.shutdown();
    }

    private void invalidAccount(Integer limit, Integer offset, String delete, List<String> ids) {
        //查询当前批次1端作废的客户数据
        List<JSONObject> invalidAccountList = findInvalidAccount(limit, offset, ids);
        if (CollectionUtils.isEmpty(invalidAccountList)) {
            return;
        }
        //作废客户id
        List<String> invalidAccountIds = invalidAccountList.stream()
                .map(o -> o.getString("_id"))
                .collect(Collectors.toList());

        //查询数据同步下发记录
        List<JSONObject> downstreamAccountBySyncInfo = Lists.newArrayList();
        for (List<String> accountIds : ListUtils.partition(invalidAccountIds, 50)) {
            List<JSONObject> result = findDownstreamAccountBySyncInfo(accountIds);
            if (CollectionUtils.isEmpty(result)) {
                continue;
            }
            downstreamAccountBySyncInfo.addAll(result);
        }

        //按下游企业分组客户,要作废的数据
        Map<String, Set<String>> tenantIdToAccountIds = downstreamAccountBySyncInfo.stream()
                .collect(Collectors.groupingBy(o -> o.getString("destTenantId"),
                        Collectors.mapping(o -> o.getString("destDataId"), Collectors.toSet())));

        //使用作废数据填充同步记录可能不存在的数据
        //fillDataByInvalidData(invalidAccountList, tenantIdToAccountIds);

        log.info("invalidAccount tenantIdToAccountIds {}", JSON.toJSONString(tenantIdToAccountIds));

        //作废下游数据
        if ("y".equals(delete)) {
            invalidData(tenantIdToAccountIds);
        }
    }

    private void fillDataByInvalidData(List<JSONObject> invalidAccountList, Map<String, Set<String>> tenantIdToAccountIds) {
        Set<String> collectAccountIds = Sets.newHashSet();
        //收集作废客户数据的销售商和服务商
        for (JSONObject data : invalidAccountList) {
            if (data.containsKey("linked_service_providers__c") && Objects.nonNull(data.get("linked_service_providers__c"))) {
                collectAccountIds.addAll(data.getJSONArray("linked_service_providers__c").toJavaList(String.class));
            }
            if (data.containsKey("sellers__c") && Objects.nonNull(data.get("sellers__c"))) {
                collectAccountIds.addAll(data.getJSONArray("sellers__c").toJavaList(String.class));
            }
        }

        //查询销售商和服务商的客户企业映射
        Map<String, String> accountIdToTenantId = Maps.newHashMap();
        for (List<String> accountIds : ListUtils.partition(Lists.newArrayList(collectAccountIds), 500)) {
            List<JSONObject> result = findEnterpriseRelationObjByAccountId(777421, accountIds,
                    Lists.newArrayList(CommonApiNames.ID, EnterpriseRelationObjApiNames.ENTERPRISE_ACCOUNT, EnterpriseRelationObjApiNames.MAPPER_ACCOUNT_ID));
            if (CollectionUtils.isEmpty(result)) {
                continue;
            }
            for (JSONObject data : result) {
                accountIdToTenantId.put(data.getString(EnterpriseRelationObjApiNames.MAPPER_ACCOUNT_ID), data.getString(EnterpriseRelationObjApiNames.ENTERPRISE_ACCOUNT));
            }
        }

        //用作废客户数据补充同步记录中可能不存在的数据
        for (JSONObject data : invalidAccountList) {
            String curAccountId = data.getString("_id");
            if (data.containsKey("linked_service_providers__c") && Objects.nonNull(data.get("linked_service_providers__c"))) {
                for (String accountId : data.getJSONArray("linked_service_providers__c").toJavaList(String.class)) {
                    if (!accountIdToTenantId.containsKey(accountId)) {
                        continue;
                    }
                    String tenantId = accountIdToTenantId.get(accountId);
                    if (tenantIdToAccountIds.containsKey(tenantId)) {
                        tenantIdToAccountIds.get(tenantId).add(curAccountId);
                    } else {
                        tenantIdToAccountIds.put(tenantId, Sets.newHashSet(curAccountId));
                    }
                }
            }
            if (data.containsKey("sellers__c") && Objects.nonNull(data.get("sellers__c"))) {
                for (String accountId : data.getJSONArray("sellers__c").toJavaList(String.class)) {
                    if (!accountIdToTenantId.containsKey(accountId)) {
                        continue;
                    }
                    String tenantId = accountIdToTenantId.get(accountId);
                    if (tenantIdToAccountIds.containsKey(tenantId)) {
                        tenantIdToAccountIds.get(tenantId).add(curAccountId);
                    } else {
                        tenantIdToAccountIds.put(tenantId, Sets.newHashSet(curAccountId));
                    }
                }
            }
        }
    }

    private void invalidData(Map<String, Set<String>> tenantIdToAccountIds) {
        for (Map.Entry<String, Set<String>> entry : tenantIdToAccountIds.entrySet()) {
            String tenantId = entry.getKey();
            Set<String> values = entry.getValue();
            for (List<String> accountIds : ListUtils.partition(Lists.newArrayList(values), 100)) {
                try {
                    if (CollectionUtils.isEmpty(accountIds)) {
                        continue;
                    }
                    //查询下游客户互联企业
                    List<JSONObject> relationObjByAccountId = findEnterpriseRelationObjByAccountId(Integer.parseInt(tenantId), accountIds, Lists.newArrayList(CommonApiNames.ID));
                    if (CollectionUtils.isNotEmpty(relationObjByAccountId)) {
                        //停用互联企业
                        for (JSONObject object : relationObjByAccountId) {
                            String objectId = object.getString(CommonApiNames.ID);
                            stopEnterpriseRelation(Integer.parseInt(tenantId), objectId);
                        }
                        try {
                            Thread.sleep(4000);
                        } catch (InterruptedException e) {
                            log.error("stopEnterpriseRelation thread sleep error", e);
                        }
                        //作废互联企业
                        batchInvalidData(Integer.parseInt(tenantId), "EnterpriseRelationObj",
                                relationObjByAccountId.stream().map(o -> o.getString(CommonApiNames.ID)).collect(Collectors.toList()));
                    }
                    //查询联系人
                    List<JSONObject> contactObjByAccountId = findContactObjByAccountId(Integer.parseInt(tenantId), accountIds, Lists.newArrayList(CommonApiNames.ID));
                    //作废联系人
                    if (CollectionUtils.isNotEmpty(contactObjByAccountId)) {
                        batchInvalidData(Integer.parseInt(tenantId), "ContactObj",
                                contactObjByAccountId.stream().map(o -> o.getString(CommonApiNames.ID)).collect(Collectors.toList()));
                    }
                    //查询客户
                    List<JSONObject> accountObjByAccountId = findAccountObjByIds(Integer.parseInt(tenantId), accountIds, Lists.newArrayList(CommonApiNames.ID));
                    if (CollectionUtils.isNotEmpty(accountObjByAccountId)) {
                        //作废客户
                        batchInvalidData(Integer.parseInt(tenantId), "AccountObj",
                                accountObjByAccountId.stream().map(o -> o.getString(CommonApiNames.ID)).collect(Collectors.toList()));
                    }
                } catch (Exception e) {
                    log.error("invalidData cur group error", e);
                    log.info("invalidData cur group tenantId {}, accountIds {}", tenantId, JSON.toJSONString(accountIds));
                }
            }
        }
    }

    private void batchInvalidData(Integer tenantId, String apiName, List<String> ids) {
        for (String id : ids) {
            PaasDataBatchInvalid.Arg arg = new PaasDataBatchInvalid.Arg();
            arg.setIds(Lists.newArrayList(id));
            PaasDataBatchInvalid.Result result = paasDataProxy.batchInvalid(tenantId, -10000, apiName, arg);
            log.info("batchInvalidData tenantId {}, apiName {}, arg {}, result {}", tenantId, apiName, JSON.toJSONString(arg), JSON.toJSONString(result));
        }
    }

    private void batchDeleteData(Integer tenantId, String apiName, List<String> ids) {
        PaasDataBatchBulkDelete.Arg arg = new PaasDataBatchBulkDelete.Arg();
        arg.setIdList(ids);
        arg.setDescribeApiName(apiName);
        log.info("batchDeleteData tenantId {}, apiName {}, arg {}", tenantId, apiName, JSON.toJSONString(arg));
        PaasDataBatchBulkDelete.Result result = paasDataProxy.batchBulkDelete(tenantId, -10000, apiName, arg);
        log.info("batchDeleteData tenantId {}, apiName {}, result {}", tenantId, apiName, JSON.toJSONString(result));
    }

    private List<JSONObject> findAccountObjByIds(Integer tenantId, List<String> ids, List<String> fields) {
        PaasDataFindByIds.Arg arg = new PaasDataFindByIds.Arg();
        arg.setDataIdList(ids);
        arg.setDescribeApiName("AccountObj");
        arg.setSelectFields(fields);
        PaasDataFindByIds.Result result = paasDataProxy.findByIds(tenantId, -10000, arg);
        if (result.getCode() != 0) {
            log.info("findAccountObjByIds is error {}, {}", result.getCode(), result.getMessage());
            return null;
        }
        if (Objects.isNull(result.getData()) || Objects.isNull(result.getData().getDataList())) {
            return null;
        }
        return result.getData().getDataList();
    }

    private List<JSONObject> findContactObjByAccountId(Integer tenantId, List<String> accountIds, List<String> fields) {
        PaasDataQueryWithFields.Arg arg = new PaasDataQueryWithFields.Arg();
        PaasDataQueryWithFields.QueryDTO query = new PaasDataQueryWithFields.QueryDTO.Builder()
                .appendFilter("account_id", "IN", accountIds)
                .needReturnCountNum(false)
                .needReturnQuote(false)
                .limit(2000).offset(0).build();
        arg.setQueryString(JSON.toJSONString(query));
        arg.setFieldList(fields);
        PaasDataQueryWithFields.Result result = paasDataProxy.queryWithFields(tenantId, -10000, "ContactObj", arg);
        if (result.getErrCode() != 0) {
            log.info("findContactObjByAccountId is error {}, {}", result.getErrCode(), result.getErrMessage());
            return null;
        }
        if (Objects.isNull(result.getResult()) || Objects.isNull(result.getResult().getQueryResult())) {
            return null;
        }
        return result.getResult().getQueryResult().getDataList();
    }

    private void stopEnterpriseRelation(Integer tenantId, String id) {
        JSONObject object = new JSONObject();
        object.put("objectDataId", id);
        JSONObject result = enterpriseRelationProxy.stopEnterpriseRelation(tenantId, -10000, object);
        log.info("stopEnterpriseRelation tenantId : {}, id : {}, result : {}", tenantId, id, JSON.toJSONString(result));
    }

    private List<JSONObject> findEnterpriseRelationObjByAccountId(Integer tenantId, List<String> accountIds, List<String> fields) {
        PaasDataQueryWithFields.Arg arg = new PaasDataQueryWithFields.Arg();
        PaasDataQueryWithFields.QueryDTO query = new PaasDataQueryWithFields.QueryDTO.Builder()
                .appendFilter(EnterpriseRelationObjApiNames.MAPPER_ACCOUNT_ID, "IN", accountIds)
                .needReturnCountNum(false)
                .needReturnQuote(false)
                .limit(2000).offset(0).build();
        arg.setQueryString(JSON.toJSONString(query));
        arg.setFieldList(fields);
        PaasDataQueryWithFields.Result findEnterpriseRelationResult = paasDataProxy.queryWithFields(tenantId, -10000, EnterpriseRelationObjApiNames.OBJECT_API_NAME, arg);
        if (findEnterpriseRelationResult.getErrCode() != 0) {
            log.info("findEnterpriseRelationObjByAccountId is error {}, {}", findEnterpriseRelationResult.getErrCode(), findEnterpriseRelationResult.getErrMessage());
            return null;
        }
        if (Objects.isNull(findEnterpriseRelationResult.getResult()) || Objects.isNull(findEnterpriseRelationResult.getResult().getQueryResult())) {
            return null;
        }
        return findEnterpriseRelationResult.getResult().getQueryResult().getDataList();
    }

    private List<JSONObject> findDownstreamAccountBySyncInfo(List<String> accountIds) {
        QuerySyncDataMapping.Arg arg = new QuerySyncDataMapping.Arg();
        QuerySyncDataMapping.ArgBody argBody = new QuerySyncDataMapping.ArgBody();
        argBody.setSourceDataIds(Lists.newArrayList(accountIds));
        argBody.setSourceTenantId("777421");
        argBody.setSourceObjectApiName("AccountObj");
        argBody.setDestObjectApiName("AccountObj");
        argBody.setDestDataIds(Lists.newArrayList(accountIds));
        arg.setArg(argBody);

        log.info("invalidAccount findDownstreamAccountBySyncInfo arg : {}", JSON.toJSONString(arg));
        QuerySyncDataMapping.Result result = syncDataAllProxy.querySyncDataMapping(777421, -10000, arg);
        log.info("invalidAccount findDownstreamAccountBySyncInfo result : {}", JSON.toJSONString(result));
        if (result.getErrCode() != 0) {
            return null;
        }
        return result.getData();
    }

    private List<JSONObject> findInvalidAccount(Integer limit, Integer offset, List<String> ids) {
        PaasDataFindByQuery.Arg arg = new PaasDataFindByQuery.Arg();
        arg.setDescribeApiName("AccountObj");
        PaasDataFindByQuery.QueryDTO queryDTO = new PaasDataFindByQuery.QueryDTO();
        if (CollectionUtils.isEmpty(ids)) {
            queryDTO.setLimit(limit);
            queryDTO.setOffset(offset);
        }
        PaasDataFindByQuery.FilterDTO filter = new PaasDataFindByQuery.FilterDTO("life_status", "EQ", "invalid");
        queryDTO.setFilters(Lists.newArrayList(filter));
        if (CollectionUtils.isNotEmpty(ids)) {
            PaasDataFindByQuery.FilterDTO idFilter = new PaasDataFindByQuery.FilterDTO("_id", "IN", ids);
            queryDTO.getFilters().add(idFilter);
        }
        arg.setSearchQueryInfo(JSON.toJSONString(queryDTO));
        arg.setSelectFields(Lists.newArrayList("_id", "linked_service_providers__c", "sellers__c"));
        arg.setNeedCount(true);
        arg.setIncludeInvalid(true);
        log.info("invalidAccount findByQuery arg : {}", JSON.toJSONString(arg));
        PaasDataFindByQuery.Result result = paasDataProxy.findByQuery(777421, -10000, arg);
        log.info("invalidAccount findByQuery result : {}", JSON.toJSONString(result));
        if (result.getCode() != 0) {
            return null;
        }
        if (Objects.isNull(result.getData()) || Objects.isNull(result.getData().getQueryResult())) {
            return null;
        }
        return result.getData().getQueryResult().getDataList();
    }


    @ServiceMethod("fix_tpm_activity_data")
    public String fixTpmActivityData(JSONObject arg, ServiceContext serviceContext) {
        List<String> tenantIds = arg.getJSONArray("tenantIds").toJavaList(String.class);
        boolean update = arg.getBoolean("update");
        TraceContext traceContext = TraceContext.get();
        Map<String, String> mdcMap = MDC.getCopyOfContextMap();
        new Thread(() -> {
            MDC.setContextMap(mdcMap);
            TraceContext._set(traceContext);
            for (String tenantId : tenantIds) {
                log.info("start: {}", tenantId);
                List<IFilter> filters = Lists.newArrayList();
                IFilter filter = new Filter();
                filter.setFieldName("life_status");
                filter.setOperator(Operator.EQ);
                filter.setFieldValues(Lists.newArrayList("normal"));
                List<String> fields = Lists.newArrayList("_id", "field_v22z9__c", "field_7P4gl__c", "field_2e4eA__c", "field_Sn6l1__c", "field_mz7e1__c");
                List<IObjectData> iObjectDataList = findObjectsByFilter(tenantId, "object_Cg1df__c", filters, fields);
                log.info("iObjectDataList size : {}", iObjectDataList.size());
                if (CollectionUtils.isEmpty(iObjectDataList)) {
                    continue;
                }
                for (IObjectData iObjectData : iObjectDataList) {
                    String dataId = iObjectData.getId();
                    String project_id = iObjectData.get("field_v22z9__c", String.class);
                    String year = iObjectData.get("field_7P4gl__c", String.class);
                    String month = iObjectData.get("field_2e4eA__c", String.class);
                    String store_type = iObjectData.get("field_Sn6l1__c", String.class);
                    String bugdet_id = iObjectData.get("field_mz7e1__c", String.class);
                    List<IObjectData> tpmData = queryTPMData(tenantId, project_id, year, month, store_type, bugdet_id);
                    if (CollectionUtils.isEmpty(tpmData)) {
                        log.info("tpmData is empty");
                        continue;
                    }
                    log.info("tpmData size : {}", tpmData.size());
                    for (IObjectData tpmDatum : tpmData) {
                        String activity_id = tpmDatum.getId();
                        String agreementRule__c = tpmDatum.get("agreementRule__c", String.class);
                        if (StringUtils.isEmpty(activity_id) || StringUtils.isNotEmpty(agreementRule__c)) {
                            log.info("agreementRule__c is not empty: {}", agreementRule__c);
                            log.info("activity_id is empty");
                            continue;
                        }
                        PaasDataIncrementUpdate.Arg updateArg = new PaasDataIncrementUpdate.Arg();
                        Map<String, Object> params = Maps.newHashMap();
                        params.put("agreementRule__c", dataId);
                        params.put("field_HV815__c", bugdet_id);
                        params.put("_id", activity_id);
                        updateArg.setData(params);
                        if (update) {
                            PaasDataIncrementUpdate.Result result = paasDataProxy.incrementUpdate(Integer.valueOf(tenantId), -10000, "TPMActivityObj", updateArg);
                            log.info("tenantId : {}, updateArg : {}, result : {}", tenantId, JSON.toJSONString(updateArg), JSON.toJSONString(result));
                        } else {
                            log.info("tenantId : {}, updateArg : {}", tenantId, JSON.toJSONString(updateArg));
                        }
                    }
                }
            }
        }).start();

        return "success";
    }

    private List<IObjectData> queryTPMData(String s, String project_id, String year, String month, String store_type, String bugdet_id) {
        IFilter filter = new Filter();
        filter.setFieldName("field_mpi1n__c");
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(project_id));

        IFilter filter1 = new Filter();
        filter1.setFieldName("year__c");
        filter1.setOperator(Operator.EQ);
        filter1.setFieldValues(Lists.newArrayList(year));

        IFilter filter2 = new Filter();
        filter2.setFieldName("month__c");
        filter2.setOperator(Operator.EQ);
        filter2.setFieldValues(Lists.newArrayList("1"));

        IFilter filter3 = new Filter();
        filter3.setFieldName("field_4t3sj__c");
        filter3.setOperator(Operator.EQ);
        filter3.setFieldValues(Lists.newArrayList(store_type));

        List<String> fields = Lists.newArrayList("_id", "agreementRule__c", "field_HV815__c");
        List<IFilter> filterList = Lists.newArrayList(filter, filter1, filter2, filter3);
        log.info("filterList : {}", JSON.toJSONString(filterList));
        List<IObjectData> tpmDatalist = findObjectsByFilter(s, "TPMActivityObj", filterList, fields);

        log.info("tpmDatalist size : {}", tpmDatalist.size());

        return tpmDatalist;
    }

    private IObjectData queryEnterpriseAccountByAccountId(String tenantId, String accountId) {
        IFilter accountFilter = new Filter();
        accountFilter.setFieldName("mapper_account_id");
        accountFilter.setOperator(Operator.EQ);
        accountFilter.setFieldValues(Lists.newArrayList(accountId));
        List<IObjectData> initPersonnelInfo = findObjectsByFilter(tenantId, "EnterpriseRelationObj", Lists.newArrayList(accountFilter), Lists.newArrayList());
        if (CollectionUtils.isEmpty(initPersonnelInfo)) {
            log.info("enterprise relation  not found  {}", tenantId);
            return null;
        }
        return initPersonnelInfo.get(0);
    }

    private void invalidAndRemovePublicEmployee(int tenantId, String objectDataId, String apiName) {
        PaasDataBatchInvalid.Arg arg = new PaasDataBatchInvalid.Arg();
        arg.setIds(Lists.newArrayList(objectDataId));
        PaasDataBatchInvalid.Result result = paasDataProxy.batchInvalid(tenantId, -10000, apiName, arg);
        log.info("invalid apiName :{} result : {}", apiName, JSON.toJSONString(result));

        PaasDataBatchBulkDelete.Arg deleteArg = new PaasDataBatchBulkDelete.Arg();
        deleteArg.setIdList(Lists.newArrayList(objectDataId));
        deleteArg.setDescribeApiName(apiName);
        PaasDataBatchBulkDelete.Result deleteResult = paasDataProxy.batchBulkDelete(tenantId, -10000, apiName, deleteArg);
        log.info("delete apiName :{} result : {}", apiName, JSON.toJSONString(deleteResult));
    }

    private void stopPublicEmployee(String tenantId, IObjectData publicEmployee) {
        String id = publicEmployee.getId();
        JSONObject object = new JSONObject();
        object.put("objectDataId", id);
        JSONObject result = enterpriseRelationProxy.stopPublicEmployee(Integer.valueOf(tenantId), -10000, object);
        log.info("stopPublicEmployee result : {}", JSON.toJSONString(result));
    }

    private void stopEnterpriseRelation(String tenantId, IObjectData enterpriseRelation) {
        String id = enterpriseRelation.getId();
        JSONObject object = new JSONObject();
        object.put("objectDataId", id);
        JSONObject result = enterpriseRelationProxy.stopEnterpriseRelation(Integer.valueOf(tenantId), -10000, object);
        log.info("stopEnterpriseRelation result : {}", JSON.toJSONString(result));
    }

    private IObjectData handleInvalidEmployeeId(List<IObjectData> invalidEmployeeList) {
        IObjectData result = null;
        if (invalidEmployeeList.size() <= 1) {
            return null;
        }
        Map<String, List<IObjectData>> map = Maps.newHashMap();
        for (IObjectData objectData : invalidEmployeeList) {
            String outTenantId = objectData.get("outer_tenant_id", String.class);
            if (map.containsKey(outTenantId)) {
                map.get(outTenantId).add(objectData);
            } else {
                List<IObjectData> list = Lists.newArrayList();
                list.add(objectData);
                map.put(outTenantId, list);
            }
        }

        log.info("invalidEmployeeMap : {}", JSON.toJSONString(map));
        for (Map.Entry<String, List<IObjectData>> stringListEntry : map.entrySet()) {
            List<IObjectData> dataList = stringListEntry.getValue();
            if (dataList.size() == 1) {
                continue;
            }

            for (IObjectData objectData : dataList) {
                Boolean relationOwner = objectData.get("relation_owner", Boolean.class);
                if (BooleanUtils.isNotTrue(relationOwner)) {
                    result = objectData;
                    return result;
                }
            }
        }

        if (result == null) {
            result = invalidEmployeeList.get(0);
        }

        return result;
    }

    private List<IObjectData> queryNeedInvalidEmployeeInfo(String tenantId, String phone) {
        IFilter filter = new Filter();
        filter.setFieldName("mobile");
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(phone));

        List<IObjectData> employeeList = findObjectsByFilter(tenantId, "PublicEmployeeObj", Lists.newArrayList(filter), Lists.newArrayList());
        if (CollectionUtils.isEmpty(employeeList)) {
            log.info("employee not found : {}", tenantId);
            return Lists.newArrayList();
        }
        log.info("public employee phone : {} , employeeList : {}", phone, JSON.toJSONString(employeeList));
        return employeeList;
    }

    @ServiceMethod("add_department")
    public String addDepartment(JSONObject arg, ServiceContext serviceContext) {
        List<String> tenantIds = arg.getJSONArray("tenantIds").toJavaList(String.class);
        String upTenantId = arg.getString("upTenantId");
        new Thread(MonitorTaskWrapper.wrap(() -> {
            for (String tenantId : tenantIds) {
                addDepartment(tenantId, upTenantId);
            }
        })).start();
        return "success";
    }

    public void addDepartment(String tenantId, String upTenantId) {
        //获取互联企业信息
        log.info("start add department : {}", tenantId);
        IObjectData companyObjectData = getCompanyInfo(upTenantId, tenantId);
        String accountId = companyObjectData.get("mapper_account_id").toString();
        log.info("mapper_account_id : {}", accountId);
        if (StringUtils.isEmpty(accountId)) {
            return;
        }
        //查询人员初始化对象关联的手机号
        List<String> phoneNumberList = getPhoneNumberList(upTenantId, accountId);
        if (CollectionUtils.isEmpty(phoneNumberList)) {
            return;
        }
        //根据手机号查询下游人员数据
        List<IObjectData> employees = getDownEmployeeList(tenantId, phoneNumberList);
        if (CollectionUtils.isEmpty(employees)) {
            return;
        }
        //查询下游所有的部门信息
        List<IObjectData> departments = getDownDepartmentList(tenantId);
        PaasDataBatchIncrementUpdate.Arg updateArg = new PaasDataBatchIncrementUpdate.Arg();
        Map<String, String> departNameToId = Maps.newHashMap();
        for (IObjectData department : departments) {
            String name = department.getName();
            String id = department.getId();
            departNameToId.put(name, id);
        }

        log.info("department name to id : {}", JSON.toJSONString(departNameToId));
        for (IObjectData employee : employees) {
            JSONObject updateObject = new JSONObject();
            updateObject.put("_id", employee.getId());
            if (departNameToId.containsKey("销售部")) {//ingoreI18n
                updateObject.put("main_department", Lists.newArrayList(departNameToId.get("销售部")));//ingoreI18n
            } else {
                updateObject.put("main_department", Lists.newArrayList(departNameToId.get("全公司")));//ingoreI18n
            }
            updateArg.add(updateObject);
        }

        PaasDataBatchIncrementUpdate.Result result = paasDataProxy.batchIncrementUpdate(Integer.valueOf(tenantId), -10000, "PersonnelObj", updateArg);
        if (0 != result.getCode()) {
            log.info("update department error : {}", result.getMessage());
        }
        log.info("end add department : {}", tenantId);
    }

    @ServiceMethod("fix_self_enterprise_flag")
    public String fixSelfEnterpriseFlag(JSONObject arg, ServiceContext serviceContext) {
        List<String> tenantIds = arg.getJSONArray("tenantIds").toJavaList(String.class);
        String upTenantId = arg.getString("upTenantId");
        new Thread(MonitorTaskWrapper.wrap(() -> {
            List<String> activeOurEnterpriseList = new ArrayList<>();
            List<String> needFixOurEnterpriseList = new ArrayList<>();
            List<String> fixSuccessFixOurEnterpriseList = new ArrayList<>();
            List<String> fixFailOurEnterpriseList = new ArrayList<>();
            Map<String, String> errorMessageMap = Maps.newHashMap();
            for (String tenantId : tenantIds) {
                //获取互联企业信息
                log.info("start fixSelfEnterpriseFlag : {}", tenantId);
                IObjectData companyObjectData = getCompanyInfo(upTenantId, tenantId);
                if (Objects.isNull(companyObjectData)) {
                    errorMessageMap.put(tenantId, "companyObjectData is null");
                    continue;
                }
                //获取客户信息
                String accountId = companyObjectData.get("mapper_account_id", String.class, "");
                log.info("mapper_account_id : {}", accountId);
                if (StringUtils.isEmpty(accountId)) {
                    errorMessageMap.put(tenantId, "mapper_account_id is null");
                    continue;
                }
                IObjectData accountInfo = getAccountInfo(tenantId, accountId);
                if (accountInfo == null) {
                    errorMessageMap.put(tenantId, "SelfEnterpriseAccountInfo is null");
                    log.info("accountInfo is null");
                    continue;
                }
                String isOurEnterprise = accountInfo.get("isOurEnterprise__c", String.class);
                if ("YES".equals(isOurEnterprise)) {
                    log.info("isOurEnterprise is YES");
                    activeOurEnterpriseList.add(tenantId);
                    continue;
                }
                needFixOurEnterpriseList.add(tenantId);
                PaasDataIncrementUpdate.Arg updateArg = new PaasDataIncrementUpdate.Arg();
                JSONObject updateData = new JSONObject();
                updateData.put("isOurEnterprise__c", "YES");
                updateData.put("_id", accountId);
                updateArg.setData(updateData);
                PaasDataIncrementUpdate.Result result = paasDataProxy.incrementUpdate(Integer.parseInt(tenantId), -10000, "AccountObj", updateArg);
                if (0 != result.getErrCode()) {
                    log.info("update accountInfo error : {}", result.getErrMessage());
                    errorMessageMap.put(tenantId, result.getErrMessage());
                    fixFailOurEnterpriseList.add(tenantId);
                } else {
                    log.info("update accountInfo success : {}", result.getErrMessage());
                    fixSuccessFixOurEnterpriseList.add(tenantId);
                }
                log.info("update accountInfo end : {}", tenantId);
            }
            JSONObject collect = new JSONObject();
            collect.put("activeOurEnterpriseListSize", activeOurEnterpriseList.size());
            collect.put("activeOurEnterpriseList", activeOurEnterpriseList);
            collect.put("needFixOurEnterpriseListSize", needFixOurEnterpriseList.size());
            collect.put("needFixOurEnterpriseList", needFixOurEnterpriseList);
            collect.put("fixSuccessFixOurEnterpriseListSize", fixSuccessFixOurEnterpriseList.size());
            collect.put("fixSuccessFixOurEnterpriseList", fixSuccessFixOurEnterpriseList);
            collect.put("fixFailOurEnterpriseListSize", fixFailOurEnterpriseList.size());
            collect.put("fixFailOurEnterpriseList", fixFailOurEnterpriseList);
            log.info("fixSelfEnterpriseFlag errorMessageMap : {}", JSONObject.toJSONString(errorMessageMap));
            log.info("fixSelfEnterpriseFlag collect : {}", collect.toJSONString());
        })).start();
        return "success";
    }

    @ServiceMethod("update_personnel")
    public UpdatePersonnel.Result updatePersonnel(UpdatePersonnel.Arg arg, ServiceContext serviceContext) {
        new Thread(MonitorTaskWrapper.wrap(() -> {
            try {
                updatePersonnel(serviceContext.getTenantId(), arg.getArg());
            } catch (Exception e) {
                log.error("updatePersonnel exception", e);
            }
        })).start();
        UpdatePersonnel.Result result = new UpdatePersonnel.Result();
        result.setErrorCode(0);
        result.setErrorMsg("success");
        return result;
    }

    private void updatePersonnel(String tenantId, JSONObject argBody) {
        List<String> enterpriseRelationIds = argBody.getJSONArray("enterpriseRelationIds").toJavaList(String.class);
        for (String enterpriseRelationId : enterpriseRelationIds) {
            try {
                IObjectData enterpriseRelationData = serviceFacade.findObjectData(User.systemUser(tenantId), enterpriseRelationId, EnterpriseRelationObjApiNames.OBJECT_API_NAME);
                if (Objects.isNull(enterpriseRelationData)) {
                    continue;
                }
                String downstreamEa = enterpriseRelationData.get(EnterpriseRelationObjApiNames.ENTERPRISE_ACCOUNT, String.class);
                int downstreamEi = eieaConverter.enterpriseAccountToId(downstreamEa);
                String accountId = enterpriseRelationData.get(EnterpriseRelationObjApiNames.MAPPER_ACCOUNT_ID, String.class);
                updatePersonnel(tenantId, String.valueOf(downstreamEi), accountId);
            } catch (Exception e) {
                log.error("updatePersonnel exception", e);
                log.info("updatePersonnel error {}, {}", tenantId, enterpriseRelationId);
            }
        }
    }

    private void updatePersonnel(String tenantId, String downstreamEi, String accountId) {
        //查询该下游员工 人员初始化对象
        IFilter accountFilter = new Filter();
        accountFilter.setFieldName("field_Mbmxk__c");
        accountFilter.setOperator(Operator.EQ);
        accountFilter.setFieldValues(Lists.newArrayList(accountId));
        List<IObjectData> initPersonnelInfo = findObjectsByFilter(tenantId, "object_7Nhkj__c", Lists.newArrayList(accountFilter), Lists.newArrayList());
        if (CollectionUtils.isEmpty(initPersonnelInfo)) {
            log.info("initPersonnel not found for account {}", accountId);
            return;
        }

        updateEmployeeStatus(initPersonnelInfo, downstreamEi);
    }

    private void updateEmployeeStatus(List<IObjectData> initPersonnelInfo, String downstreamEi) {
        List<IObjectData> filterData = initPersonnelInfo.stream()
                .filter(o -> ("0".equals(o.get("employee_status__c", String.class)) || "1".equals(o.get("employee_status__c", String.class))))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterData)) {
            return;
        }
        //用手机号查询人员
        List<String> phoneNumbers = filterData.stream()
                .map(o -> o.get("phone__c", String.class))
                .collect(Collectors.toList());
        log.info("downstreamEi : {}, phoneNumbers : {}", downstreamEi, JSON.toJSONString(phoneNumbers));

        IFilter phoneFilter = new Filter();
        phoneFilter.setFieldName("phone");
        phoneFilter.setOperator(Operator.IN);
        phoneFilter.setFieldValues(phoneNumbers);
        List<IObjectData> downstreamPersonnelList = findObjectsByFilter(downstreamEi, "PersonnelObj", Lists.newArrayList(phoneFilter), Lists.newArrayList("_id", "phone"));
        log.info("downstreamEi : {}, downstreamPersonnelList : {}", downstreamEi, JSON.toJSONString(downstreamPersonnelList));
        Map<String, String> phoneToIdMap = downstreamPersonnelList.stream()
                .collect(Collectors.toMap(o -> o.get("phone", String.class), DBRecord::getId));
        log.info("downstreamEi : {}, phoneToIdMap : {}", downstreamEi, JSON.toJSONString(phoneToIdMap));

        List<String> resumeIds = Lists.newArrayList();
        List<String> stopIds = Lists.newArrayList();

        for (IObjectData data : filterData) {
            String s = data.get("employee_status__c", String.class);
            String phone = data.get("phone__c", String.class);
            if (!phoneToIdMap.containsKey(phone)) {
                continue;
            }
            String id = phoneToIdMap.get(phone);
            if ("0".equals(s)) {
                resumeIds.add(id);
            } else if ("1".equals(s)) {
                stopIds.add(id);
            }
        }

        log.info("downstreamEi : {}, resumeIds : {}, stopIds : {}", downstreamEi, JSON.toJSONString(resumeIds), JSON.toJSONString(stopIds));
        if (CollectionUtils.isNotEmpty(resumeIds)) {
            for (List<String> ids : Lists.partition(resumeIds, 10)) {
                AsyncBulkResume.Arg arg = new AsyncBulkResume.Arg();
                arg.setDataIds(ids);
                log.info("downstreamEi : {}, resume arg : {}", downstreamEi, JSON.toJSONString(arg));
                AsyncBulkResume.Result result = personnelProxy.asyncBulkResume(Integer.parseInt(downstreamEi), -10000, arg);
                log.info("downstreamEi : {}, resume result : {}", downstreamEi, JSON.toJSONString(result));
            }
        }
        if (CollectionUtils.isNotEmpty(stopIds)) {
            for (List<String> ids : Lists.partition(stopIds, 10)) {
                AsyncBulkStop.Arg arg = new AsyncBulkStop.Arg();
                arg.setDataIds(ids);
                log.info("downstreamEi : {}, stop arg : {}", downstreamEi, JSON.toJSONString(arg));
                AsyncBulkStop.Result result = personnelProxy.asyncBulkStop(Integer.parseInt(downstreamEi), -10000, arg);
                log.info("downstreamEi : {}, stop result : {}", downstreamEi, JSON.toJSONString(result));
            }
        }
    }

    @Override
    @ServiceMethod("init_personnel")
    public InitPersonnel.Result initPersonnel(InitPersonnel.Arg arg, ServiceContext serviceContext) {
        InitPersonnel.Result result = new InitPersonnel.Result();
        List<String> enterpriseRelationIds = arg.getEnterpriseRelationIds();
        String addDepartment = arg.getAddDepartment();
        if (CollectionUtils.isEmpty(enterpriseRelationIds)) {
            result.setErrorCode(101);
            result.setErrorMsg("Parameter error");
            return result;
        }
        TraceContext traceContext = TraceContext.get();
        Map<String, String> mdcMap = MDC.getCopyOfContextMap();
        new Thread(() -> {
            MDC.setContextMap(mdcMap);
            TraceContext._set(traceContext);
            String tenantId = serviceContext.getTenantId();
            for (String enterpriseRelationId : enterpriseRelationIds) {
                log.info("start add personnel : {}", enterpriseRelationId);
                try {
                    IObjectData enterpriseRelationData = serviceFacade.findObjectData(User.systemUser(tenantId), enterpriseRelationId, EnterpriseRelationObjApiNames.OBJECT_API_NAME);
                    if (Objects.isNull(enterpriseRelationData)) {
                        continue;
                    }
                    String downstreamEa = enterpriseRelationData.get(EnterpriseRelationObjApiNames.ENTERPRISE_ACCOUNT, String.class);
                    int downstreamEi = eieaConverter.enterpriseAccountToId(downstreamEa);
                    String accountId = enterpriseRelationData.get(EnterpriseRelationObjApiNames.MAPPER_ACCOUNT_ID, String.class);

                    initPersonnel(tenantId, String.valueOf(downstreamEi), accountId);
                    if ("true".equals(addDepartment)) {
                        addDepartment(String.valueOf(downstreamEi), tenantId);
                    }
                } catch (Exception e) {
                    log.error("init personnel exception", e);
                    log.info("init personnel error {}, {}", tenantId, enterpriseRelationId);
                }
            }
        }).start();
        result.setErrorCode(0);
        result.setErrorMsg("success");
        return result;
    }

    private Map<String, String> generateRoleNameToRoleCode(List<PaasRoleDetail> roleList) {
        Map<String, String> roleNameToRoleCode = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(roleList)) {
            for (PaasRoleDetail paasRoleDetail : roleList) {
                roleNameToRoleCode.put(paasRoleDetail.getRoleName(), paasRoleDetail.getRoleCode());
            }
        }
        return roleNameToRoleCode;
    }

    @ServiceMethod("batchAddOutAppRoleAssigns")
    public String batchAddOutAppRoleAssigns(JSONObject arg, ServiceContext serviceContext) {
        JSONArray tenantIdsArray = arg.getJSONArray("tenantIds");
        String roleId = arg.getString("roleId");
        if (CollectionUtils.isEmpty(tenantIdsArray)) {
            return "tenantIds is empty";
        }
        if (StringUtils.isBlank(roleId)) {
            return "roleId is empty";
        }
        if (StringUtils.isBlank(TraceContext.get().getTraceId())) {
            TraceContext.get().setTraceId("tractId-batchAddOutAppRoleAssigns" + UUID.randomUUID());
        }
        List<IFilter> sourceErFilters, sourcePeFilters;

        try {
            sourceErFilters = convertFilter(arg.getJSONArray("erFilters"));
            sourcePeFilters = convertFilter(arg.getJSONArray("peFilters"));
        } catch (Exception e) {
            log.error("convertFilter exception", e);
            return "convertFilter error";
        }
        new Thread(MonitorTaskWrapper.wrap(() -> {
            try {
                List<Integer> tenantIds = tenantIdsArray.toJavaList(Integer.class);
                for (Integer tenantId : tenantIds) {
                    log.info("{}, handle start", tenantId);
                    List<IObjectData> erList = findAllObjectsByFilter(String.valueOf(tenantId), "EnterpriseRelationObj", sourceErFilters, Lists.newArrayList("_id"));
                    if (CollectionUtils.isEmpty(erList)) {
                        continue;
                    }
                    List<String> erIds = erList.stream().map(DBRecord::getId).collect(Collectors.toList());
                    List<IFilter> peFilters = new ArrayList<>(sourcePeFilters);
                    Filter peFilter = new Filter();
                    peFilter.setFieldName("outer_tenant_id");
                    peFilter.setOperator(Operator.IN);
                    peFilter.setFieldValues(erIds);
                    peFilters.add(peFilter);
                    List<IObjectData> peList = findAllObjectsByFilter(String.valueOf(tenantId), "PublicEmployeeObj", peFilters, Lists.newArrayList("_id", "outer_uid"));
                    if (CollectionUtils.isEmpty(peList)) {
                        continue;
                    }
                    log.info("{}, {}, peList={}", tenantId, peList.size(), JSONObject.toJSONString(peList));
                    List<Long> outerUid = peList.stream().map(i -> i.get("outer_uid", Long.class)).collect(Collectors.toList());
                    HeaderObj headerObj = HeaderObj.newInstance(tenantId);
                    AddOutAppRoleAssignsArg add = new AddOutAppRoleAssignsArg();
                    add.setRoleId(roleId);
                    add.setLinkAppId(String.valueOf(arg.getOrDefault("linkAppId", "FSAID_114910f9")));
                    add.setHasMainRole(arg.getBooleanValue("hasMainRole"));
                    add.setUpstreamEa(eieaConverter.enterpriseIdToAccount(tenantId));
                    int l = 0, r = Math.min(100, outerUid.size());
                    while (l < r) {
                        add.setDownstreamOuterUids(outerUid.subList(l, r));
                        log.info("{}, batchAddOutAppRoleAssigns arg={}", tenantId, JSONObject.toJSONString(add));
                        linkAppService.batchAddOutAppRoleAssigns(headerObj, add);
                        if (r == outerUid.size()) {
                            break;
                        }
                        l = r;
                        r = Math.min(r + 100, outerUid.size());
                    }
                    log.info("{}, handle end", tenantId);
                }
            } catch (Exception e) {
                log.error("batchAddOutAppRoleAssigns exception", e);
            }
        })).start();
        return TraceContext.get().getTraceId();
    }

    public List<IFilter> convertFilter(JSONArray argFilters) {
        List<IFilter> filters = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(argFilters)) {
            for (int i = 0; i < argFilters.size(); i++) {
                JSONObject tmp = argFilters.getJSONObject(i);
                Filter filter = new Filter();
                filter.setFieldName(tmp.getString("fieldName"));
                filter.setOperator(Operator.valueOf(tmp.getString("operator")));
                filter.setFieldValues(tmp.getJSONArray("fieldValues").toJavaList(String.class));
                filters.add(filter);
            }
        }
        return filters;
    }

    @ServiceMethod("fixMNTpmActivityAgreementData")
    public String fixTpmActivityAgreementData(JSONObject arg, ServiceContext serviceContext) {
        JSONArray tenantIdsArray = arg.getJSONArray("tenantIds");
        if (CollectionUtils.isEmpty(tenantIdsArray)) {
            return "tenantIds is empty";
        }
        String upTenantId = arg.getString("upTenantId");
        if (StringUtils.isEmpty(upTenantId)) {
            return "upTenantId must not empty";
        }
        if (arg.getString("beginDate") == null || arg.getString("endDate") == null) {
            return "Start date - End date cannot be empty";
        }
        String objectApiName = String.valueOf(arg.getOrDefault("objectApiName", "object_Jk6CW__c"));
        List<String> tenantIds = tenantIdsArray.toJavaList(String.class);
        new Thread(MonitorTaskWrapper.wrap(() -> {
            try {
                processFixTpmActivityAgreementData(upTenantId, tenantIds, objectApiName, arg);
            } catch (Exception e) {
                log.error("processFixTpmActivityAgreementData exception", e);
            }
        })).start();
        return "success";
    }

    public void processFixTpmActivityAgreementData(String upTenantId, List<String> tenantIds, String objectApiName, JSONObject param) {
        Map<String, String> errorMessageMap = Maps.newHashMap();
        for (String tenantId : tenantIds) {
            try {
                log.info("fixTpmActivityAgreementData handle {}", tenantId);
                IObjectData companyObjectData = getCompanyInfo(upTenantId, tenantId);
                if (Objects.isNull(companyObjectData)) {
                    errorMessageMap.put(tenantId, "companyObjectData is null");
                    continue;
                }
                //获取客户信息
                String accountId = companyObjectData.get("mapper_account_id", String.class, "");
                log.info("mapper_account_id : {}", accountId);
                if (StringUtils.isEmpty(accountId)) {
                    errorMessageMap.put(tenantId, "mapper_account_id is null");
                    continue;
                }
                //查一端数据
                List<IObjectData> dataList;
                //查N端数据
                List<IObjectData> datasListN;
                if ("object_Jk6CW__c".equals(objectApiName)) {
                    dataList = queryAllTpmActivityAgreementData(upTenantId, objectApiName, accountId, param.getString("beginDate"), param.getString("endDate"));
                    datasListN = queryAllTpmActivityAgreementData(tenantId, "TPMActivityAgreementObj", accountId, param.getString("beginDate"), param.getString("endDate"));
                } else if ("CostApprove__c".equals(objectApiName)) {
                    dataList = queryAllTPMActivityData(upTenantId, objectApiName, accountId);
                    datasListN = queryAllTPMActivityData(tenantId, "TPMActivityObj", accountId);
                } else {
                    errorMessageMap.put(tenantId, String.format("objectApiName %s un support", objectApiName));
                    return;
                }
                Set<String> idsNSet = datasListN.stream().map(DBRecord::getId).collect(Collectors.toSet());
                //取1端有 N端没有的数据
                List<IObjectData> targetDataList = new ArrayList<>();
                for (IObjectData data : dataList) {
                    if (!idsNSet.contains(data.getId())) {
                        data.setDescribeApiName("object_Jk6CW__c");
                        data.setTenantId(upTenantId);
                        targetDataList.add(data);
                    }
                }
                //标记
                if (CollectionUtils.isNotEmpty(targetDataList)) {
                    //修改数据进行标记
                    log.info("fixTpmActivityAgreementData targetIds.0={}", JSONObject.toJSONString(targetDataList.get(0)));
                    log.info("fixTpmActivityAgreementData targetIds.size={}", targetDataList.size());
                    int l = 0, r = Math.min(2000, targetDataList.size());
                    int count = 1;
                    while (l < r) {
                        PaasDataBatchIncrementUpdate.Arg arg = new PaasDataBatchIncrementUpdate.Arg();
                        for (IObjectData objectData : targetDataList.subList(l, r)) {
                            JSONObject d = new JSONObject();
                            d.put("_id", objectData.getId());
                            d.put("nActiveDataFlag__c", false);
                            arg.add(d);
                        }
                        PaasDataBatchIncrementUpdate.Result result = paasDataProxy.batchIncrementUpdate(Integer.parseInt(upTenantId), -10000, objectApiName, arg);
                        log.info("fixTpmActivityAgreementData batchUpdateByFields No.{} {}", count++, JSONObject.toJSON(result));
                        if (r == targetDataList.size()) {
                            break;
                        }
                        l = r;
                        r = Math.min(r + 2000, targetDataList.size());
                    }
                }
                log.info("fixTpmActivityAgreementData {} batchIncrementUpdate success, data.size={}", tenantId, targetDataList.size());
            } catch (Exception e) {
                errorMessageMap.put(tenantId, e.getMessage());
            }
        }
        log.info("fixTpmActivityAgreementData errorMessageMap={}", JSONObject.toJSONString(errorMessageMap));
    }

    /**
     * 修复蒙牛一端CostApprove__c（费用申请审批）数据
     */
    @ServiceMethod("fixTpmTPMActivityData")
    public String fixTpmTPMActivityData(JSONObject arg, ServiceContext serviceContext) {
        String upTenantId = arg.getString("upTenantId");
        if (StringUtils.isEmpty(upTenantId)) {
            return "upTenantId must not empty";
        }
        if (arg.getString("beginDate") == null || arg.getString("endDate") == null) {
            return "Activity Request Details Start Date - End date cannot be empty";
        }
        new Thread(MonitorTaskWrapper.wrap(() -> {
            try {
                processFixTpmTPMActivityData(upTenantId, arg);
            } catch (Exception e) {
                log.error("processFixTpmTPMActivityData exception", e);
            }
        })).start();
        return "success";
    }

    private void processFixTpmTPMActivityData(String upTenantId, JSONObject param) {
        //市场名称：field_n90J1__c 门店类型：field_kunCh__c 批发堆头申请金额：field_oRdfx__c  申请总金额：field_Jhq3g__c 协议门店数：field_vS24i__c 活动方案名称: field_sxqX1__c
        List<String> fields = Lists.newArrayList("_id", "name", "field_vS24i__c", "field_Jhq3g__c", "field_oRdfx__c", "field_kunCh__c", "field_n90J1__c", "field_sxqX1__c", "field_3uwnv__c");
        List<IFilter> filters = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(param.getJSONArray("filters"))) {
            List<JSONObject> argFilters = param.getJSONArray("filters").toJavaList(JSONObject.class);
            for (JSONObject argFilter : argFilters) {
                Filter filter = new Filter();
                filter.setFieldName(argFilter.getString("fieldName"));
                filter.setOperator(Operator.valueOf(argFilter.getString("operator")));
                filter.setFieldValues(argFilter.getJSONArray("fieldValues").toJavaList(String.class));
                filters.add(filter);
            }
        }
        List<IObjectData> allCostApproveDataList = findAllObjectsByFilter(upTenantId, "CostApprove__c", filters, fields);
        log.info("allCostApproveDataList.size={}", allCostApproveDataList.size());
        Map<String, Map<String, List<IObjectData>>> groupData = allCostApproveDataList.stream()
                .collect(Collectors.groupingBy(i -> i.get("field_n90J1__c", String.class),
                        Collectors.groupingBy(i -> i.get("field_kunCh__c", String.class))));
        PaasDataBatchIncrementUpdate.Arg arg = new PaasDataBatchIncrementUpdate.Arg();
        groupData.forEach((accountId, storeTypeMap) -> {
            storeTypeMap.forEach((storeType, dataList) -> {
                //查询活动申请明细数据
                //活动申请明细.filter(开始日期., 经销商=x.市场名称, 门店类型=x.门店类型,  有效状态=有效，活动名称=?引用,)
                List<IObjectData> allTpmActivityAgreementDataList = queryAllTpmActivityAgreementData2(upTenantId, accountId, storeType, param.getString("beginDate"), param.getString("endDate"));
                log.info("accountId={}, storeType={}, allTpmActivityAgreementDataList.size={}", accountId, storeType, allTpmActivityAgreementDataList.size());
                //活动方案id : Set<陈列协议Id>
                Map<String, Set<String>> activityIdToActivityAgreementIdSetMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(allTpmActivityAgreementDataList)) {
                    Set<String> activityAgreementIds = allTpmActivityAgreementDataList.stream().map(i -> i.get("field_K6Y5h__c", String.class)).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
                    log.info("activityAgreementIds.size={}", activityAgreementIds.size());
                    if (!activityAgreementIds.isEmpty()) {
                        Filter filter1 = new Filter();
                        filter1.setFieldName("_id");
                        filter1.setOperator(Operator.IN);
                        filter1.setFieldValues(new ArrayList<>(activityAgreementIds));
                        //陈列协议数据
                        List<IObjectData> activityAgreementDataList = findAllObjectsByFilter(upTenantId, "ActivityAgreement__c", Lists.newArrayList(filter1), Lists.newArrayList("_id", "field_Yeg1n__c"));
                        log.info("activityAgreementDataList.size={}", activityAgreementDataList.size());
                        for (IObjectData objectData : activityAgreementDataList) {
                            String activityId = objectData.get("field_Yeg1n__c", String.class);
                            if (!activityIdToActivityAgreementIdSetMap.containsKey(activityId)) {
                                activityIdToActivityAgreementIdSetMap.put(activityId, new HashSet<>());
                            }
                            activityIdToActivityAgreementIdSetMap.get(activityId).add(objectData.getId());
                        }
                        log.info("activityIdToActivityAgreementIdSetMap={}", JSONObject.toJSONString(activityIdToActivityAgreementIdSetMap));
                    }
                }
                for (IObjectData objectData : dataList) {
                    log.info("handle {} start", objectData.getName());
                    String activityId = objectData.get("field_sxqX1__c", String.class);
                    log.info("activityId={}", activityId);
                    //陈列协议Id
                    Set<String> activityAgreementIdSet = activityIdToActivityAgreementIdSetMap.getOrDefault(activityId, Collections.emptySet());
                    List<IObjectData> targetDataList = allTpmActivityAgreementDataList.stream()
                            .filter(i -> activityAgreementIdSet.contains(i.get("field_K6Y5h__c", String.class)))
                            .collect(Collectors.toList());
                    //总门店数
                    int realTotalStore = targetDataList.size();
                    //申请总金额
                    BigDecimal realTotalAppliedAmount = BigDecimal.ZERO;
                    //总批发堆头申请金额
                    BigDecimal realTotalPFDTAmount = BigDecimal.ZERO;

                    for (IObjectData targetData : targetDataList) {
                        BigDecimal appliedAmount = targetData.get("field_3eC2a__c", BigDecimal.class, BigDecimal.ZERO);
                        BigDecimal pFDTAmount = targetData.get("pfdt1_sqje__c", BigDecimal.class, BigDecimal.ZERO);
                        realTotalAppliedAmount = realTotalAppliedAmount.add(appliedAmount);
                        realTotalPFDTAmount = realTotalPFDTAmount.add(pFDTAmount);
                    }
                    //对比
                    Integer totalStore = objectData.get("field_vS24i__c", Integer.class, 0);
                    BigDecimal totalAppliedAmount = objectData.get("field_Jhq3g__c", BigDecimal.class, BigDecimal.ZERO);
                    BigDecimal totalPFDTAmount = objectData.get("field_oRdfx__c", BigDecimal.class, BigDecimal.ZERO);
                    boolean isDiff;
                    if ("64a0e28aca15e600011ff4d9".equals(objectData.get("field_kunCh__c", String.class))) {
                        isDiff = realTotalPFDTAmount.compareTo(totalPFDTAmount) != 0;
                    } else {
                        isDiff = realTotalStore != totalStore || realTotalAppliedAmount.compareTo(totalAppliedAmount) != 0 || realTotalPFDTAmount.compareTo(totalPFDTAmount) != 0;
                    }
                    log.info("{}, realData: {}-{}-{}, data: {}-{}-{}, isDiff={}", objectData.getName(), realTotalStore, realTotalAppliedAmount, realTotalPFDTAmount, totalStore, totalAppliedAmount, totalPFDTAmount, isDiff);
                    if (isDiff) {
                        JSONObject updateData = new JSONObject();
                        updateData.put("_id", objectData.getId());
                        updateData.put("field_2bh3m__c", realTotalStore);
                        updateData.put("field_ZwqRJ__c", realTotalAppliedAmount);
                        updateData.put("field_1G5oc__c", realTotalPFDTAmount);
                        updateData.put("field_3uwnv__c", true);
                        arg.add(updateData);
                    } else {
                        log.info("{}, field_3uwnv__c={}", objectData.getName(), objectData.get("field_3uwnv__c"));
                        JSONObject updateData = new JSONObject();
                        updateData.put("_id", objectData.getId());
                        updateData.put("field_2bh3m__c", realTotalStore);
                        updateData.put("field_ZwqRJ__c", realTotalAppliedAmount);
                        updateData.put("field_1G5oc__c", realTotalPFDTAmount);
                        updateData.put("field_3uwnv__c", false);
                        arg.add(updateData);
                    }
                    log.info("handle {} end", objectData.getName());
                }
            });
        });
        log.info("arg.size={}", arg.size());
        if (!arg.isEmpty()) {
            PaasDataBatchIncrementUpdate.Result result = paasDataProxy.batchIncrementUpdate(Integer.parseInt(upTenantId), -10000, "CostApprove__c", arg);
            if (result.getCode() != 0) {
                log.error("paasDataProxy.batchIncrementUpdate CostApprove__c error {}", result.getMessage());
            }
        }
    }

    private List<IObjectData> queryAllTpmActivityAgreementData2(String tenantId, String accountId, String storeType, String beginDate, String endData) {
        List<IFilter> filters = new ArrayList<>();
        //查询1端一月份数据
        Filter filter1 = new Filter();
        filter1.setFieldName("begin_date__c");
        filter1.setOperator(Operator.GTE);
        filter1.setFieldValues(Lists.newArrayList(beginDate));
        filters.add(filter1);
        IFilter filter2 = new Filter();
        filter2.setFieldName("end_date__c");
        filter2.setOperator(Operator.LTE);
        filter2.setFieldValues(Lists.newArrayList(endData));
        filters.add(filter2);
        IFilter filter3 = new Filter();
        filter3.setFieldName("dealer_id__c");
        filter3.setOperator(Operator.EQ);
        filter3.setFieldValues(Lists.newArrayList(accountId));
        filters.add(filter3);
        IFilter filter4 = new Filter();
        filter4.setFieldName("field_38uda__c");
        filter4.setOperator(Operator.EQ);
        filter4.setFieldValues(Lists.newArrayList(storeType));
        filters.add(filter4);
        IFilter filter5 = new Filter();
        filter5.setFieldName("is_submint__c");
        filter5.setOperator(Operator.EQ);
        filter5.setFieldValues(Lists.newArrayList("2"));
        filters.add(filter5);
        List<IObjectData> allData = findAllObjectsByFilter(tenantId, "object_Jk6CW__c", filters, Lists.newArrayList("_id", "field_3eC2a__c", "pfdt1_sqje__c", "field_K6Y5h__c"));
        if (CollectionUtils.isEmpty(allData)) {
            return Collections.emptyList();
        }
        return allData;
    }

    private List<IObjectData> queryAllTpmActivityAgreementData(String tenantId, String objectApiName, String accountId, String beginDate, String endDate) {
        List<IFilter> filters = new ArrayList<>();
        //查询1端一月份数据
        Filter filter1 = new Filter();
        filter1.setFieldName("begin_date__c");
        filter1.setOperator(Operator.GTE);
        filter1.setFieldValues(Lists.newArrayList(beginDate));
        filters.add(filter1);
        IFilter filter2 = new Filter();
        filter2.setFieldName("end_date__c");
        filter2.setOperator(Operator.LTE);
        filter2.setFieldValues(Lists.newArrayList(endDate));
        filters.add(filter2);
        if ("object_Jk6CW__c".equals(objectApiName)) {//一端企业
            IFilter filter3 = new Filter();
            filter3.setFieldName("dealer_id__c");
            filter3.setOperator(Operator.EQ);
            filter3.setFieldValues(Lists.newArrayList(accountId));
            filters.add(filter3);
        }
        List<IObjectData> allData = findAllObjectsByFilter(tenantId, objectApiName, filters, Lists.newArrayList("_id"));
        if (CollectionUtils.isEmpty(allData)) {
            return Collections.emptyList();
        }
        return allData;
    }

    private List<IObjectData> queryAllTPMActivityData(String tenantId, String objectApiName, String accountId) {
        List<IFilter> filters = new ArrayList<>();
        if ("CostApprove__c".equals(objectApiName)) {//一端企业
            IFilter filter3 = new Filter();
            filter3.setFieldName("field_n90J1__c");
            filter3.setOperator(Operator.EQ);
            filter3.setFieldValues(Lists.newArrayList(accountId));
            filters.add(filter3);
        }
        List<IObjectData> allData = findAllObjectsByFilter(tenantId, objectApiName, filters, Lists.newArrayList("_id"));
        if (CollectionUtils.isEmpty(allData)) {
            return Collections.emptyList();
        }
        return allData;
    }


    private List<IObjectData> getDownDepartmentList(String tenantId) {
        IFilter filter = new Filter();
        filter.setFieldName("life_status");
        filter.setOperator(Operator.IN);
        filter.setFieldValues(Lists.newArrayList("normal"));

        List<IObjectData> departmentList = findObjectsByFilter(tenantId, "DepartmentObj", Lists.newArrayList(filter), Lists.newArrayList());
        if (CollectionUtils.isEmpty(departmentList)) {
            log.info("department not found : {}", tenantId);
            return Lists.newArrayList();
        }

        return departmentList;
    }

    private List<IObjectData> getDownEmployeeList(String tenantId, List<String> phoneNumberList) {
        IFilter filter = new Filter();
        filter.setFieldName("phone");
        filter.setOperator(Operator.IN);
        filter.setFieldValues(phoneNumberList);


        IFilter departmentFilter = new Filter();
        departmentFilter.setFieldName("main_department");
        departmentFilter.setOperator(Operator.IN);
        departmentFilter.setFieldValues(Lists.newArrayList("999998"));

        List<IObjectData> employeeList = findObjectsByFilter(tenantId, "PersonnelObj", Lists.newArrayList(filter, departmentFilter), Lists.newArrayList());
        if (CollectionUtils.isEmpty(employeeList)) {
            log.info("person not found for account {}", tenantId);
            return Lists.newArrayList();
        }

        log.info("employee info : {}", JSON.toJSONString(employeeList));
        return employeeList;
    }

    private List<String> getPhoneNumberList(String tenantId, String accountId) {
        List<String> result = Lists.newArrayList();
        //查询该下游员工 人员初始化对象
        IFilter accountFilter = new Filter();
        accountFilter.setFieldName("field_Mbmxk__c");
        accountFilter.setOperator(Operator.EQ);
        accountFilter.setFieldValues(Lists.newArrayList(accountId));
        List<IObjectData> initPersonnelInfo = findObjectsByFilter(tenantId, "object_7Nhkj__c", Lists.newArrayList(accountFilter), Lists.newArrayList());
        if (CollectionUtils.isEmpty(initPersonnelInfo)) {
            log.info("initPersonnel not found for account {}", accountId);
            return Lists.newArrayList();
        }
        for (IObjectData objectData : initPersonnelInfo) {
            result.add(objectData.get("phone__c", String.class));
        }
        log.info("phone list : {}", JSON.toJSONString(result));
        IObjectData enterpriseRelationData = serviceFacade.findObjectData(User.systemUser(tenantId), accountId, AccountObjApiNames.OBJECT_API_NAME);
        result.add(enterpriseRelationData.get("tel", String.class));
        log.info("phone numbers : {}", JSON.toJSONString(result));

        return result;
    }

    private IObjectData getCompanyInfo(String upTenantId, String tenantId) {
        IFilter accountFilter = new Filter();
        accountFilter.setFieldName("enterprise_account");
        accountFilter.setOperator(Operator.EQ);
        accountFilter.setFieldValues(Lists.newArrayList(tenantId));
        List<IObjectData> initPersonnelInfo = findObjectsByFilter(upTenantId, "EnterpriseRelationObj", Lists.newArrayList(accountFilter), Lists.newArrayList());
        if (CollectionUtils.isEmpty(initPersonnelInfo)) {
            log.info("enterprise relation  not found  {}", tenantId);
            return null;
        }
        return initPersonnelInfo.get(0);
    }

    private IObjectData getAccountInfo(String tenantId, String accountId) {
        IFilter accountFilter = new Filter();
        accountFilter.setFieldName("_id");
        accountFilter.setOperator(Operator.EQ);
        accountFilter.setFieldValues(Lists.newArrayList(accountId));
        List<IObjectData> accountInfo = findObjectsByFilter(tenantId, "AccountObj", Lists.newArrayList(accountFilter), Lists.newArrayList());
        if (CollectionUtils.isEmpty(accountInfo)) {
            log.info("account not found tenantId={}, accountId={}", tenantId, tenantId);
            return null;
        }
        return accountInfo.get(0);
    }

    public List<PaasRoleDetail> getRoleList(Integer downstreamEi, String appId) {
        RoleListDto.Argument roleArg = PaasArgumentUtil.buildPaaSPermissionArgument(RoleListDto.Argument.class, downstreamEi, -10000, appId);
        PaaSResult<RoleListDto.Result> resultPaaSResult = paaSPermissionService.roleList(roleArg);
        if (resultPaaSResult.getErrCode() != 0) {
            log.info("get roleList error {}, {}", resultPaaSResult.getErrCode(), resultPaaSResult.getErrMessage());
            return Lists.newArrayList();
        }
        if (Objects.isNull(resultPaaSResult.getResult()) || CollectionUtils.isEmpty(resultPaaSResult.getResult().getRoles())) {
            log.info("get roleList is empty");
            return Lists.newArrayList();
        }
        return resultPaaSResult.getResult().getRoles();
    }

    public void initPersonnel(String tenantId, String downstreamEi, String accountId) {
        //查询该下游员工 人员初始化对象
        IFilter accountFilter = new Filter();
        accountFilter.setFieldName("field_Mbmxk__c");
        accountFilter.setOperator(Operator.EQ);
        accountFilter.setFieldValues(Lists.newArrayList(accountId));
        //排除创建成功的
        IFilter createStateFilter = new Filter();
        createStateFilter.setFieldName("create_state__c");
        createStateFilter.setOperator(Operator.N);
        createStateFilter.setFieldValues(Lists.newArrayList("1"));
        List<IObjectData> initPersonnelInfo = findObjectsByFilter(tenantId, "object_7Nhkj__c", Lists.newArrayList(accountFilter, createStateFilter), Lists.newArrayList());
        if (CollectionUtils.isEmpty(initPersonnelInfo)) {
            log.info("initPersonnel not found for account {}", accountId);
            return;
        }

        initPersonnelInfo.sort((s1, s2) -> {
            Integer s1Priority = s1.get("create_priority__c", Integer.class);
            Integer s2Priority = s2.get("create_priority__c", Integer.class);
            if (Objects.isNull(s1Priority)) {
                return 1;
            }
            if (Objects.isNull(s2Priority)) {
                return -1;
            }
            return s1Priority - s2Priority;
        });

        //给下游创建人员对象
        for (IObjectData personnelInfo : initPersonnelInfo) {
            log.info("personnelInfo : {}", JSON.toJSONString(personnelInfo));
            String personnelId = null;
            try {
                //创建人员
                if (personnelInfo.containsField("create_way__c") && Objects.nonNull(personnelInfo.get("create_way__c"))) {
                    String createWay = personnelInfo.get("create_way__c", String.class);
                    if (createWay.equals("InsertImportData")) {
                        personnelId = insertImportDataAction(personnelInfo, downstreamEi, tenantId);
                    }
                } else {
                    personnelId = addAction(personnelInfo, downstreamEi, tenantId);
                }
            } catch (Exception e) {
                log.error("create personnel by action exception", e);
            }

            if (Strings.isNullOrEmpty(personnelId)) {
                log.info("after action personnelId is nullOrEmpty");
                continue;
            }

            //业务功能权限
            Map<String, String> roleNameToRoleCode = generateRoleNameToRoleCode(
                    getRoleList(Integer.parseInt(downstreamEi), "CRM"));
            //管理功能权限
            Map<String, String> systemRoleNameToRoleCode = generateRoleNameToRoleCode(
                    getRoleList(Integer.parseInt(downstreamEi), "facishare-system"));

            //处理CRM角色字段映射
            IObjectDescribe initPersonnelDescribe = serviceFacade.findObject(tenantId, "object_7Nhkj__c");
            Map<String, String> roleFieldValueToName = generateFieldValueToLabel(initPersonnelDescribe, "field_fm5Md__c");
            Map<String, String> systemRoleFieldValueToName = generateFieldValueToLabel(initPersonnelDescribe, "system_role__c");

            try {
                addRole(personnelInfo, personnelId, "field_fm5Md__c", roleFieldValueToName, roleNameToRoleCode, Integer.parseInt(downstreamEi), "CRM");
            } catch (Exception e) {
                log.error("add CRM role exception", e);
            }
            try {
                addRole(personnelInfo, personnelId, "system_role__c", systemRoleFieldValueToName, systemRoleNameToRoleCode, Integer.parseInt(downstreamEi), "facishare-system");
            } catch (Exception e) {
                log.error("add facishare-system role exception", e);
            }
            try {
                initPersonnelPassWord(personnelInfo, personnelId, Integer.parseInt(downstreamEi));
            } catch (Exception e) {
                log.error("initPersonnelPassWord exception", e);
            }
            try {
                //设置主角色
                String defaultRoleV = personnelInfo.get("field_Pv3FO__c", String.class);
                if (!roleFieldValueToName.containsKey(defaultRoleV)) {
                    log.info("default role not found {}, {}", downstreamEi, defaultRoleV);
                    continue;
                }
                setDefaultRole(Integer.parseInt(downstreamEi), roleNameToRoleCode, Lists.newArrayList(personnelId), roleFieldValueToName.get(defaultRoleV));
            } catch (Exception e) {
                log.error("setDefaultRole exception", e);
            }
        }
    }

    private String insertImportDataAction(IObjectData personnelInfo, String downstreamEi, String tenantId) {
        ActionContext context = new ActionContext(RequestContext.builder()
                .tenantId(downstreamEi)
                .user(User.builder().userId("-10000").tenantId(downstreamEi).build())
                .build(), "PersonnelObj", "InsertImportData");
        BaseImportAction.Arg arg = new BaseImportAction.Arg();
        arg.setImportType(1);
        arg.setIsApprovalFlowEnabled(false);
        arg.setCheckOutOwner(false);
        arg.setIsEmptyValueToUpdate(false);
        arg.setFinalBatch(true);
        arg.setImportPreProcessing(false);
        arg.setRemoveOutTeamMember(false);
        arg.setIsUnionDuplicateChecking(false);
        arg.setUpdateOwner(false);
        arg.setIsWorkFlowEnabled(false);
        arg.setMatchingType(2);
        arg.setApiName("PersonnelObj");
        arg.setObjectCode("PersonnelObj");
        arg.setSupportFieldMapping(false);
        arg.setTenantId(downstreamEi);

        List<ObjectDataDocument> rows = Lists.newArrayList();
        ObjectDataDocument row = new ObjectDataDocument();
        row.put("系统名（昵称）", personnelInfo.getName());//ingoreI18n
        row.put("姓名", personnelInfo.get("full_name__c"));//ingoreI18n
        row.put("员工编号", personnelInfo.get("employee_number__c"));//ingoreI18n
        if (personnelInfo.containsField("sex__c") && Objects.nonNull(personnelInfo.get("sex__c"))) {
            String sex = personnelInfo.get("sex__c", String.class);
            if ("M".equals(sex)) {
                row.put("性别", "男");//ingoreI18n
            } else if ("F".equals(sex)) {
                row.put("性别", "女");//ingoreI18n
            }
        }
        row.put("手机", personnelInfo.get("phone__c"));//ingoreI18n
        row.put("登录账号", personnelInfo.get("user_name__c"));//ingoreI18n
        row.put("职位", personnelInfo.get("positions__c"));//ingoreI18n
        row.put("汇报对象", personnelInfo.get("report__c"));//ingoreI18n
        row.put("主属部门", personnelInfo.get("main_department__c"));//ingoreI18n
        rows.add(row);
        arg.setRows(rows);
        log.info("insertImportDataAction rows {}", JSON.toJSONString(rows));
        BaseImportAction.Result result = serviceFacade.triggerRemoteAction(context, arg, StandardInsertImportDataAction.Result.class);
        log.info("insertImportDataAction result {}", JSON.toJSONString(result));
        if (!result.isSuccess()) {
            log.info("insertImportDataAction isSuccess false");
            updateErrorInfo(Integer.parseInt(tenantId), personnelInfo.getId(), String.format("ErrorMessage : %s, rows : %s", "insertImportDataAction isSuccess false", JSON.toJSONString(rows)));
            return null;
        }
        if (result.getValue().getImportSucceedCount() == 0) {
            if (CollectionUtils.isNotEmpty(result.getValue().getRowErrorList())) {
                updateErrorInfo(Integer.parseInt(tenantId), personnelInfo.getId(), String.format("ErrorMessage : %s, rows : %s", result.getValue().getRowErrorList().get(0).getErrorMessage(), JSON.toJSONString(rows)));
            }
            log.info("insertImportDataAction ErrorMessage");
            return null;
        }

        updateSuccessInfo(Integer.parseInt(tenantId), personnelInfo.getId());

        PaasDataFindOne.Arg findOneArg = new PaasDataFindOne.Arg();
        findOneArg.setDescribeApiName("PersonnelObj");
        findOneArg.setSelectFields(Lists.newArrayList("_id"));
        PaasDataFindOne.FilterDTO filterDTO = new PaasDataFindOne.FilterDTO("phone", "EQ", personnelInfo.get("phone__c", String.class));
        PaasDataFindOne.QueryDTO queryDTO = new PaasDataFindOne.QueryDTO();
        queryDTO.setFilters(Lists.newArrayList(filterDTO));
        findOneArg.setSearchQueryInfo(JSONObject.toJSONString(queryDTO));
        log.info("insertImportDataAction findOneArg {}", JSONObject.toJSONString(findOneArg));
        PaasDataFindOne.Result findOneResult = paasDataProxy.findOne(Integer.parseInt(downstreamEi), -10000, findOneArg);
        log.info("insertImportDataAction findOneResult {}", JSONObject.toJSONString(findOneResult));
        if (findOneResult.getCode() != 0) {
            log.info("insertImportDataAction findOneResult code != 0");
            return null;
        }
        if (Objects.isNull(findOneResult.getData().getObjectData())) {
            log.info("insertImportDataAction findOneResult data is null");
            return null;
        }
        return findOneResult.getData().getObjectData().getString("_id");
    }

    private String addAction(IObjectData personnelInfo, String downstreamEi, String tenantId) {
        JSONObject createData = new JSONObject();
        createData.put("name", personnelInfo.getName());
        createData.put("full_name", personnelInfo.get("full_name__c"));
        createData.put("employee_number", personnelInfo.get("employee_number__c"));
        createData.put("phone", personnelInfo.get("phone__c"));
        createData.put("user_name", personnelInfo.get("user_name__c"));
        createData.put("sex", personnelInfo.get("sex__c"));
        createData.put("employee_type__c", personnelInfo.get("employee_type__c"));
        createData.put("idcard__c", personnelInfo.get("idcard__c"));
        createData.put("wechat_open_id__c", personnelInfo.get("wechat_open_id__c"));
        createData.put("wechat_app_id__c", personnelInfo.get("wechat_app_id__c"));
        createData.put("unique_id__c", personnelInfo.get("unique_id__c"));

        PaasDataCreate.Arg createArg = new PaasDataCreate.Arg();
        createArg.setObjectData(createData);
        log.info("addAction createArg {}", JSON.toJSONString(createArg));
        PaasDataCreate.Result result = paasDataProxy.create(Integer.parseInt(downstreamEi), -10000, "PersonnelObj", createArg);
        log.info("addAction createResult {}", JSON.toJSONString(result));
        if (result.getErrCode() != 0) {
            updateErrorInfo(Integer.parseInt(tenantId), personnelInfo.getId(), String.format("errorCode : %s, errorMsg : %s, createArg : %s", result.getErrCode(), result.getErrMessage(), JSON.toJSONString(createArg)));
            log.info("create personnel failed");
            return null;
        }

        if (Objects.isNull(result.getResult().getObjectData())) {
            log.info("create personnel result is null");
            return null;
        }
        return result.getResult().getObjectData().getString("_id");
    }

    private void initPersonnelPassWord(IObjectData personnelInfo, String personnelId, Integer tenantId) {
        if (!personnelInfo.containsField("init_password__c")) {
            return;
        }
        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            log.error("initPersonnelPassWord sleep exception", e);
        }
        String initPassword = personnelInfo.get("init_password__c", String.class);
        if (Strings.isNullOrEmpty(initPassword)) {
            return;
        }
        ResetEmployeePasswordArg arg = new ResetEmployeePasswordArg();
        arg.setEnterpriseId(tenantId);
        arg.setEmployeeId(Integer.parseInt(personnelId));
        arg.setNewPassword(initPassword);
        arg.setPasswordEncodeType(0);
        arg.setInitialPassword(false);
        arg.setCheckPasswordHistory(false);
        ResetEmployeePasswordResult resetEmployeePasswordResult = employeeEditionService.resetEmployeePassword(arg);
        System.out.println(JSON.toJSONString(resetEmployeePasswordResult));
    }

    private void addRole(IObjectData personnelInfo, String personnelId, String fieldName, Map<String, String> roleFieldValueToName, Map<String, String> roleNameToRoleCode, Integer tenantId, String appId) {
        if (!personnelInfo.containsField(fieldName) || Objects.isNull(personnelInfo.get(fieldName))) {
            return;
        }
        List crmRoles = personnelInfo.get(fieldName, List.class);
        List<String> roleNames = Lists.newArrayList();
        for (Object crmRole : crmRoles) {
            String v = String.valueOf(crmRole);
            if (roleFieldValueToName.containsKey(v)) {
                roleNames.add(roleFieldValueToName.get(v));
            }
        }
        if (CollectionUtils.isEmpty(roleNames)) {
            log.info("personnel {} role is null {}", appId, JSON.toJSONString(personnelInfo));
            return;
        }
        addRole(tenantId, Lists.newArrayList(personnelId), roleNameToRoleCode, roleNames, appId);
    }

    private Map<String, String> generateFieldValueToLabel(IObjectDescribe objectDescribe, String fieldName) {
        Map<String, String> map = Maps.newHashMap();
        if (Objects.nonNull(objectDescribe) && objectDescribe.containsField(fieldName)) {
            Map fields = objectDescribe.get("fields", Map.class);
            Object crmRole = fields.get(fieldName);
            Map crmRoleMap = JSONObject.parseObject(JSONObject.toJSONString(crmRole), Map.class);
            Object options = crmRoleMap.get("options");
            JSONArray optionsArray = JSON.parseArray(JSON.toJSONString(options));
            for (Object o : optionsArray) {
                JSONObject object = JSONObject.parseObject(JSON.toJSONString(o));
                map.put(object.getString("value"), object.getString("label"));
            }
        }
        return map;
    }

    private void updateSuccessInfo(Integer tenantId, String dataId) {
        try {
            PaasDataIncrementUpdate.Arg updateArg = new PaasDataIncrementUpdate.Arg();
            Map<String, Object> params = Maps.newHashMap();
            params.put("create_state__c", "1");
            params.put("_id", dataId);
            updateArg.setData(params);
            PaasDataIncrementUpdate.Result result = paasDataProxy.incrementUpdate(tenantId, -10000, "object_7Nhkj__c", updateArg);
            if (result.getErrCode() != 0) {
                log.info("updateSuccessInfo error {}, {}", result.getErrCode(), result.getErrMessage());
            }
        } catch (Exception e) {
            log.error("updateSuccessInfo error ", e);
        }
    }

    public void updateErrorInfo(Integer tenantId, String dataId, String errorMsg) {
        try {
            PaasDataIncrementUpdate.Arg updateArg = new PaasDataIncrementUpdate.Arg();
            Map<String, Object> params = Maps.newHashMap();
            params.put("field_Gs46E__c", errorMsg);
            params.put("create_state__c", "2");
            params.put("_id", dataId);
            updateArg.setData(params);
            PaasDataIncrementUpdate.Result result = paasDataProxy.incrementUpdate(tenantId, -10000, "object_7Nhkj__c", updateArg);
            if (result.getErrCode() != 0) {
                log.info("updateErrorInfo error {}, {}", result.getErrCode(), result.getErrMessage());
            }
        } catch (Exception e) {
            log.error("updateErrorInfo error ", e);
        }
    }

    public void setDefaultRole(Integer tenantId, Map<String, String> roleNameToRoleCode, List<String> users, String roleName) {
        if (!roleNameToRoleCode.containsKey(roleName)) {
            log.info("setting default role not found {}, {}", tenantId, JSON.toJSONString(roleNameToRoleCode));
            return;
        }
        UpdateUserDefaultRole.Argument argument = PaasArgumentUtil.buildPaaSPermissionArgument(UpdateUserDefaultRole.Argument.class, tenantId, -10000, "CRM");
        argument.setUsers(users);
        argument.setRoleCode(roleNameToRoleCode.get(roleName));
        PaaSResult<String> stringPaaSResult = paaSPermissionService.updateUserDefaultRole(argument);
        log.info("setDefaultRole result {}", JSON.toJSONString(stringPaaSResult));
    }

    public void addRole(Integer downstreamEi, List<String> users, Map<String, String> roleNameToRoleCode, List<String> roleNames, String appId) {
        if (CollectionUtils.isEmpty(users) || CollectionUtils.isEmpty(roleNames)) {
            log.info("addRole arg is empty {}, {}, {}", downstreamEi, JSON.toJSONString(users), JSON.toJSONString(roleNames));
            return;
        }
        List<String> roleCodes = Lists.newArrayList();
        for (String roleName : roleNames) {
            if (roleNameToRoleCode.containsKey(roleName)) {
                roleCodes.add(roleNameToRoleCode.get(roleName));
            }
        }
        if (CollectionUtils.isEmpty(roleCodes)) {
            log.info("not found roleNames {}, {}, {} ,{}", downstreamEi, JSON.toJSONString(users), JSON.toJSONString(roleNameToRoleCode), JSON.toJSONString(roleNames));
            return;
        }
        addRole(downstreamEi, appId, users, roleCodes);
    }

    public void addRole(Integer tenantId, String appId, List<String> users, List<String> roles) {
        BatchAddUserRoleDto.Argument argument = PaasArgumentUtil.buildPaaSPermissionArgument(BatchAddUserRoleDto.Argument.class, tenantId, -10000, appId);
        argument.setUsers(users);
        argument.setRoles(roles);
        PaaSResult<BatchAddUserRoleDto.Result> result = paaSPermissionService.batchAddUserRole(argument);
        log.info("addRole result {}", JSON.toJSONString(result));
    }

    private List<IObjectData> findObjectsByFilter(String tenantId, String apiName, List<IFilter> filters, List<String> fields) {
        SearchTemplateQuery queryTemplate = new SearchTemplateQuery();
        queryTemplate.setLimit(2000);
        queryTemplate.setFilters(filters);

        IActionContext actionContext = ActionContextUtil.getNewContext(tenantId);
        actionContext.setUserId(User.systemUser(tenantId).getUserId());
        actionContext.setPrivilegeCheck(false);

        return serviceFacade.findBySearchTemplateQueryWithFields(
                actionContext,
                apiName,
                queryTemplate,
                fields).getData();
    }

    private List<IObjectData> findAllObjectsByFilter(String tenantId, String apiName, List<IFilter> filters, List<String> fields) {
        int offset = 0, limit = 2000;
        List<IObjectData> result = new ArrayList<>();
        IActionContext actionContext = ActionContextUtil.getNewContext(tenantId);
        actionContext.setUserId(User.systemUser(tenantId).getUserId());
        actionContext.setPrivilegeCheck(false);
        SearchTemplateQuery queryTemplate = new SearchTemplateQuery();
        queryTemplate.setFilters(filters);
        List<IObjectData> currentData;
        do {
            queryTemplate.setLimit(limit);
            queryTemplate.setOffset(offset);
            currentData = serviceFacade.findBySearchTemplateQueryWithFields(
                    actionContext,
                    apiName,
                    queryTemplate,
                    fields).getData();
            result.addAll(currentData);
            offset += limit;
        } while (currentData.size() == limit);
        return result;
    }

    public void createDownstream(String tenantId, IObjectData accountData, BatchOpenDownstream.Arg arg) {
        String accountId = accountData.getId();
        String recordType = accountData.getRecordType();
        //获取模版企业
        String templateEa = null;
        if (Objects.isNull(arg.getArg().getDesignated()) || Strings.isNullOrEmpty(arg.getArg().getDesignated())) {
            templateEa = getTemplateEa(recordType, arg.getArg().getN(), arg.getArg().getM());
        } else {
            templateEa = arg.getArg().getDesignated();
        }
        log.info("cur templateEa is {}, {}", accountId, templateEa);
        if (Strings.isNullOrEmpty(templateEa)) {
            log.info("No template for record type, accountId : {}", accountId);
            return;
        }

        //创建联系人
        IObjectData contactObj = createContactObj(tenantId, accountData.get("corpn__c", String.class), accountData.get("tel", String.class), accountId);
        String contactId = contactObj.getId();

        //新建互联企业
        CreateDownstreamPublicEmployeeByMapperObjectIdOutArg createDownstreamArg = new CreateDownstreamPublicEmployeeByMapperObjectIdOutArg();
        createDownstreamArg.setUpstreamEi(Integer.valueOf(tenantId));
        createDownstreamArg.setMapperApiName(AccountObjApiNames.OBJECT_API_NAME);
        createDownstreamArg.setMapperId(accountId);
        createDownstreamArg.setContractId(contactId);
        createDownstreamArg.setIdentityType(1);
        createDownstreamArg.setIsInitFsAccount(true);
        createDownstreamArg.setTemplateEa(templateEa);
        createDownstreamArg.setDownstreamEnterpriseName(null);
        createDownstreamArg.setOuterRoleIds(arg.getArg().getOuterRoleIds());
        createDownstreamArg.setLinkAppIds(null);

        HeaderObj headerObj = HeaderObj.newInstance(Integer.valueOf(tenantId));
        RestResult<OuterAccountVo> createDownstreamResult = publicEmployeeService.createDownstreamPublicEmployeeByMapperObjectId(headerObj, createDownstreamArg);
        if (createDownstreamResult.getErrCode() != 0) {
            log.info("createDownstream is error {}", createDownstreamResult.getErrMsg());
        }
        log.info("createDownstream info {}", JSON.toJSONString(createDownstreamResult));
    }

    public String getTemplateEa(String recordType, String nTemplateEa, String mTemplateEa) {
        String templateEa = null;
        if ("dealer__c".equals(recordType) || "mollercular_company__c".equals(recordType)) {
            templateEa = nTemplateEa;
        } else if ("secondary_dealer__c".equals(recordType)) {
            templateEa = mTemplateEa;
        }
        return templateEa;
    }

    public IObjectData createContactObj(String tenantId, String name, String mobile, String accountId) {
        IObjectData objectData = new ObjectData();
        objectData.setOwner(Lists.newArrayList("-10000"));
        objectData.setDescribeApiName(ContactObjApiNames.OBJECT_API_NAME);
        objectData.setTenantId(tenantId);
        objectData.setRecordType("default__c");
        objectData.setName(name);
        objectData.set("mobile1", mobile);
        objectData.set("account_id", accountId);
        return serviceFacade.saveObjectData(User.systemUser(tenantId), objectData);
    }
}
