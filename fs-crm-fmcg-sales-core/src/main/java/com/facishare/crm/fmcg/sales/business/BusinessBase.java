package com.facishare.crm.fmcg.sales.business;

import com.facishare.crm.fmcg.sales.apiname.AccountObjApiNames;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> liuhaoyu
 * @create : 2025-04-10 17:13
 **/
@Slf4j
public abstract class BusinessBase {
    @Resource
    protected ServiceFacade serviceFacade;

    public List<IObjectData> findByIds(String tenantId, List<String> ids, String objectApiName) {
        return serviceFacade.findObjectDataByIds(tenantId, ids, objectApiName);
    }

    public List<IObjectData> queryWithFields(String tenantId, String objectApiName, List<IFilter> filters, List<String> fields, Integer limit, Integer offset) {
        SearchTemplateQuery queryTemplate = new SearchTemplateQuery();
        queryTemplate.setFilters(filters);
        if (Objects.nonNull(limit)) {
            queryTemplate.setLimit(limit);
        }
        if (Objects.nonNull(offset)) {
            queryTemplate.setOffset(offset);
        }
        return queryWithFields(User.systemUser(tenantId), objectApiName, queryTemplate, fields);
    }

    public List<IObjectData> queryWithFields(String tenantId, String objectApiName, List<IFilter> filters, List<String> fields) {
        SearchTemplateQuery queryTemplate = new SearchTemplateQuery();
        queryTemplate.setFilters(filters);

        return queryWithFields(User.systemUser(tenantId), objectApiName, queryTemplate, fields);
    }

    public List<IObjectData> queryWithFields(User user, String objectApiName, SearchTemplateQuery searchTemplateQuery, List<String> fields) {
        return serviceFacade.findBySearchTemplateQueryWithFields(
                ActionContextExt.of(user).getContext(), objectApiName,
                searchTemplateQuery,
                fields).getData();
    }
}
