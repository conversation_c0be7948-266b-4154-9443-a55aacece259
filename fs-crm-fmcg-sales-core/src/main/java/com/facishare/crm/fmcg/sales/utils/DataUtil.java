package com.facishare.crm.fmcg.sales.utils;

import com.facishare.crm.fmcg.sales.apiname.CommonApiNames;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.IObjectData;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> l<PERSON><PERSON><PERSON>
 * @create : 2025-04-10 11:33
 * <p>
 * com.facishare.paas.metadata.util.DataUtils#processAndValidateCreatePublicObjectData
 **/
public class DataUtil {
    /**
     * 更改公共对象数据可见范围
     */
    public static void editDataVisibleRange(IObjectData objectData, List<String> range) {
        objectData.setIsPublic(false);
        objectData.set(CommonApiNames.PUBLIC_DATA_TYPE, IObjectData.PUBLIC_DATA);
        objectData.set(CommonApiNames.D_TENANT_ID, range);
    }

    public static void putAll(IObjectData target, IObjectData source) {
        ObjectDataDocument targetDocument = ObjectDataDocument.of(target);
        targetDocument.putAll(ObjectDataDocument.of(source));
    }

    public static void putAll(IObjectData target, Map<String, Object> source) {
        ObjectDataDocument targetDocument = ObjectDataDocument.of(target);
        targetDocument.putAll(source);
    }

    public static <T> List<T> collectSpecifiedField(Collection<IObjectData> objectDataList, String fieldApiName, Class<T> type) {
        if (CollectionUtils.isEmpty(objectDataList)) {
            return null;
        }
        return objectDataList.stream()
                .filter(o -> o.containsField(fieldApiName) && Objects.nonNull(o.get(fieldApiName)))
                .map(o -> o.get(fieldApiName, type))
                .distinct()
                .collect(Collectors.toList());
    }
}
