package com.facishare.crm.fmcg.sales.message.crm;

import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.sales.apiname.EnterpriseRelationObjApiNames;
import com.facishare.crm.fmcg.sales.apiname.InspectionRecordObjApiNames;
import com.facishare.crm.fmcg.sales.business.InspectionBusiness;
import com.facishare.crm.fmcg.sales.enums.MagicEnum;
import com.facishare.crm.fmcg.sales.model.inspection.InspectionVerify;
import com.facishare.crm.fmcg.sales.utils.DataUtil;
import com.facishare.crm.fmcg.sales.utils.I18nUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.fxiaoke.model.RemindRecordItem;
import com.fxiaoke.model.crmNotify.AddRemindRecordArg;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> liuhaoyu
 * @create : 2024-08-01 15:06
 **/
@Slf4j
@Component
public class CRMMessageService {
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private CRMMessageProducer crmMessageProducer;
    @Resource
    private InspectionBusiness inspectionBusiness;

    public void sendMessageToIllegallyGoodsAccount(InspectionVerify.Context context, List<IObjectData> dataList) {
        List<String> illegallyGoodsAccountIds = DataUtil.collectSpecifiedField(dataList, InspectionRecordObjApiNames.ILLEGALLY_GOODS_ACCOUNT_ID, String.class);

        if (CollectionUtils.isEmpty(illegallyGoodsAccountIds)) {
            return;
        }

        IFilter accountIdFilter = new Filter();
        accountIdFilter.setFieldValues(illegallyGoodsAccountIds);
        accountIdFilter.setOperator(Operator.IN);
        accountIdFilter.setFieldName(EnterpriseRelationObjApiNames.MAPPER_ACCOUNT_ID);

        List<IObjectData> enterprises = inspectionBusiness.queryWithFields(context.getUpstreamTenantId(), EnterpriseRelationObjApiNames.OBJECT_API_NAME, Lists.newArrayList(accountIdFilter),
                Lists.newArrayList(EnterpriseRelationObjApiNames.ENTERPRISE_ACCOUNT, EnterpriseRelationObjApiNames.MAPPER_ACCOUNT_ID), illegallyGoodsAccountIds.size(), 0);

        Map<String, String> collect = enterprises.stream()
                .collect(Collectors.toMap(o -> o.get(EnterpriseRelationObjApiNames.MAPPER_ACCOUNT_ID, String.class), o -> o.get(EnterpriseRelationObjApiNames.ENTERPRISE_ACCOUNT, String.class)));

        for (IObjectData objectData : dataList) {
            if (!"1".equals(objectData.get(InspectionRecordObjApiNames.INSPECTION_RESULT, String.class))) {
                continue;
            }
            String illegallyGoodsAccountId = objectData.get(InspectionRecordObjApiNames.ILLEGALLY_GOODS_ACCOUNT_ID, String.class);
            if (!collect.containsKey(illegallyGoodsAccountId)) {
                log.info("sendMessageToIllegallyGoodsAccount not found illegallyGoodsAccount : {}, upstreamTenantId : {}", illegallyGoodsAccountId, context.getUpstreamTenantId());
                continue;
            }

            int ei = eieaConverter.enterpriseAccountToId(collect.get(illegallyGoodsAccountId));

            sendObjDetailUrlCRMMessage(ei, InspectionRecordObjApiNames.OBJECT_API_NAME, objectData.getId(), I18nUtil.get(MagicEnum.abnormalGoodsFlow),
                    I18nUtil.get(MagicEnum.abnormalGoodsFlowNotice), -10000, Lists.newArrayList(1000));
        }
    }

    public void sendObjDetailUrlCRMMessage(Integer tenantId, String objectApiName, String objectId, String title, String fullContent, Integer senderId, List<Integer> receiverIds) {
        Map<String, String> urlParameter = Maps.newHashMap();
        urlParameter.put("objectApiName", objectApiName);
        urlParameter.put("objectId", objectId);

        RemindRecordItem remindRecordItem = new RemindRecordItem();
        remindRecordItem.setTitle(title);
        remindRecordItem.setFullContent(fullContent);
        remindRecordItem.setSenderId(senderId);
        remindRecordItem.setSourceId(TraceContext.get().getTraceId());
        remindRecordItem.setReceiverIDs(Lists.newArrayList(1000));
        remindRecordItem.setType(92);
        remindRecordItem.setUrlType(1);
        remindRecordItem.setUrlParameter(urlParameter);
        remindRecordItem.setRemindSender(true);

        AddRemindRecordArg arg = new AddRemindRecordArg();
        arg.setEi(tenantId);
        arg.setRemindRecordItem(remindRecordItem);

        crmMessageProducer.sendCRMMessage(arg);
    }
}
