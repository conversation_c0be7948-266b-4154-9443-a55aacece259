package com.facishare.crm.fmcg.sales.pool;

import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.util.concurrent.ThreadFactoryBuilder;

import java.util.concurrent.*;

public class ThreadPoolUtil {
    public static final int DEFAULT_CORE_POOL_SIZE = 4;
    public static final int DEFAULT_MAXIMUM_POOL_SIZE = 4;
    public static final int DEFAULT_KEEP_ALIVE_TIME = 1000;

    private static final ExecutorService executorService;

    static {
        ThreadFactory workerFactory = (new ThreadFactoryBuilder()).setNameFormat("orion-%d").setDaemon(true).build();
        executorService = new ThreadPoolExecutor(DEFAULT_CORE_POOL_SIZE, DEFAULT_MAXIMUM_POOL_SIZE, DEFAULT_KEEP_ALIVE_TIME, TimeUnit.SECONDS, new ArrayBlockingQueue<>(3000), workerFactory, new ThreadPoolExecutor.CallerRunsPolicy());
    }

    public static void execute(Runnable runnable) {
        executorService.submit(MonitorTaskWrapper.wrap(runnable));
    }
}
