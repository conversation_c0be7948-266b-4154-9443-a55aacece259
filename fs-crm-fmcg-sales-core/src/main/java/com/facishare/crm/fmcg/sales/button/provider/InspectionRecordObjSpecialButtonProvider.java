package com.facishare.crm.fmcg.sales.button.provider;

import com.facishare.crm.fmcg.sales.apiname.InspectionRecordObjApiNames;
import com.facishare.crm.fmcg.sales.button.AbstractSalesSpecialButtonProvider;
import com.facishare.crm.fmcg.sales.utils.ButtonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.metadata.ui.layout.IButton;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> liu<PERSON><PERSON>
 * @create : 2024-07-12 11:25
 **/
@Component
public class InspectionRecordObjSpecialButtonProvider extends AbstractSalesSpecialButtonProvider {
    @Override
    public String getApiName() {
        return InspectionRecordObjApiNames.OBJECT_API_NAME;
    }

    @Override
    public List<IButton> getSpecialButtons() {
        List<IButton> buttons = super.getSpecialButtons();
        buttons.add(ButtonUtils.buildButton(ObjectAction.INSPECTION_RECORD_OK));
        buttons.add(ButtonUtils.buildButton(ObjectAction.INSPECTION_RECORD_APPEAL));
        return buttons;
    }
}
