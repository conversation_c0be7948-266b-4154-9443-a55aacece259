package com.facishare.crm.fmcg.sales.model.statement;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.sales.apiname.ApiNames;
import com.facishare.crm.fmcg.sales.utils.I18NSimpleUtils;
import com.fxiaoke.api.model.BaseResult;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
//IgnoreI18nFile
public interface SalesStatementsDetail {

    @Data
    @ToString
    class Arg {
        private String dataId;
    }

    @Data
    @ToString
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    class Result extends BaseResult {
        private String tag;

        private List<HeadOption> heads;

        private List<Shop> dataDetailList = Lists.newArrayList();

        public Result(String lang) {
            tag = I18NSimpleUtils.get("SalesStatementsObj.AccountDetail.tag", lang, "今日门店结算明细");
            heads = Lists.newArrayList(
                    new HeadOption("accountName", I18NSimpleUtils.get("SalesStatementsObj.AccountDetail.accountName", lang, "门店名称")).color("blue").className("underline"),
                    new HeadOption("cashPayable", I18NSimpleUtils.get("SalesStatementsObj.AccountDetail.cashPayable", lang, "现金应交")).dataType(1).isMoney(true),
                    new HeadOption("salesAmount", I18NSimpleUtils.get("SalesStatementsObj.AccountDetail.salesAmount", lang, "销售金额")).dataType(1).isMoney(true),
                    new HeadOption("salesReceivedAmount", I18NSimpleUtils.get("SalesStatementsObj.AccountDetail.salesReceivedAmount", lang, "销售实收")).dataType(1).isMoney(true),
                    new HeadOption("useReceive", I18NSimpleUtils.get("SalesStatementsObj.AccountDetail.useReceive", lang, "使用预收款")).dataType(1).isMoney(true),
                    new HeadOption("salesReturnAmount", I18NSimpleUtils.get("SalesStatementsObj.AccountDetail.salesReturnAmount", lang, "退货金额")).dataType(1).isMoney(true),
                    new HeadOption("owedAmount", I18NSimpleUtils.get("SalesStatementsObj.AccountDetail.owedAmount", lang, "新增欠款")).dataType(1).isMoney(true).color("owed_red"),
                    new HeadOption("receiveDebt", I18NSimpleUtils.get("SalesStatementsObj.AccountDetail.receiveDebt", lang, "收欠款")).dataType(1).isMoney(true),
                    new HeadOption("receivePrepaidDeposit", I18NSimpleUtils.get("SalesStatementsObj.AccountDetail.receivePrepaidDeposit", lang, "收预收款")).dataType(1).isMoney(true),
                    new HeadOption("returnGoodsOldDeductionAmount", I18NSimpleUtils.get("SalesStatementsObj.AccountDetail.returnGoodsOldDeductionAmount", lang, "退货冲抵")).dataType(1).isMoney(true),
                    new HeadOption("refundAmount", I18NSimpleUtils.get("SalesStatementsObj.AccountDetail.refundAmount", lang, "退款金额")).dataType(1).isMoney(true),
                    new HeadOption("returnGoodsDeductionAmount", I18NSimpleUtils.get("SalesStatementsObj.AccountDetail.returnGoodsDeductionAmount", lang, "车销抵扣金额")).dataType(1).isMoney(true),
                    new HeadOption("deliveryAmount", I18NSimpleUtils.get("SalesStatementsObj.AccountDetail.deliveryAmount", lang, "配送金额")).dataType(1).isMoney(true),
                    new HeadOption("deliveryCount", I18NSimpleUtils.get("SalesStatementsObj.AccountDetail.deliveryCount", lang, "配送单数"))
            );
        }

        /**
         * 添加多语言相关
         */
        public Result initLangData(String lang) {
            this.setTag(I18NSimpleUtils.get("SalesStatementsObj.AccountDetail.tag", lang, this.tag));

            // 统一处理所有表头
            handleHeadsLocalization(this.heads, lang);

            // 处理店铺数据
            for (Shop shop : dataDetailList) {
                shop.setSalesOrderListTag(I18NSimpleUtils.get("SalesOrderObj.attribute.self.display_name", lang, shop.getSalesOrderListTag()));
                shop.setReturnedOrderListTag(I18NSimpleUtils.get("ReturnedGoodsInvoiceObj.attribute.self.display_name", lang, shop.getReturnedOrderListTag()));
                // 统一处理订单和退货单
                handleOrderLists(shop.getSalesOrderList(), shop.getReturnedOrderList(), lang);
            }
            return this;
        }

        private void handleHeadsLocalization(List<HeadOption> heads, String lang) {
            for (HeadOption head : heads) {
                head.setTitle(I18NSimpleUtils.get(String.format("SalesStatementsObj.AccountDetail.%s", head.getData()), lang, head.getTitle()));
            }
        }

        private void handleOrderLists(List<SalesOrder> salesOrders, List<ReturnedOrder> returnedOrders, String lang) {
            if (salesOrders != null) {
                salesOrders.forEach(order -> handleOrderTable(order.getOrderProductTable(), lang));
            }
            if (returnedOrders != null) {
                returnedOrders.forEach(order -> handleOrderTable(order.getOrderProductTable(), lang));
            }
        }

        private static void handleOrderTable(OrderProductTable orderProductTable, String lang) {
            if (orderProductTable != null && orderProductTable.getHeads() != null) {
                orderProductTable.getHeads().forEach(head ->
                        head.setTitle(I18NSimpleUtils.get(String.format("SalesStatementsObj.AccountDetail.TableHead.%s", head.getData()), lang, head.getTitle()))
                );
            }
        }
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @ToString
    @Builder
    class HeadOption implements Serializable {
        private String data;
        private String title;
        //3 金额类型--右对齐
        private Integer dataType;
        //添加¥
        private Boolean isMoney = false;
        //owed_red - (--color-danger06: #ff522a;) black1 - (--color-neutrals19: #181c25;) black2 - (--color-neutrals15: #545861;) black3 - (--color-neutrals11: #91959e;)
        private String color = "";
        //"font-weight: bold;font-size:14px;",
        private String style = "";

        private String className = "";

        public HeadOption(String data, String title) {
            this.data = data;
            this.title = title;
        }

        public HeadOption dataType(Integer dataType) {
            this.dataType = dataType;
            return this;
        }

        public HeadOption isMoney(Boolean isMoney) {
            this.isMoney = isMoney;
            return this;
        }

        public HeadOption color(String color) {
            this.color = color;
            return this;
        }

        public HeadOption style(String style) {
            this.style = style;
            return this;
        }

        public HeadOption className(String className) {
            this.style = className;
            return this;
        }
    }


    @Data
    @ToString
    class Shop implements Serializable {
        //门店Id
        private String accountId;
        //门店名字
        private String accountName;
        //门头照
        private Map<String, Object> doorPhoto;
        //销售净额 receivable_amount
        private BigDecimal receivableAmount;

        //销 sales_amount
        private BigDecimal salesAmount = BigDecimal.ZERO;
        //shipment_amount
        private BigDecimal shipmentAmount = BigDecimal.ZERO;
        //应退 sales_return_amount
        private BigDecimal salesReturnAmount = BigDecimal.ZERO;
        //应退 sales_return_amount，去掉换出大于换入的换货
        private BigDecimal salesReturnAmountV2 = BigDecimal.ZERO;
        //换
        private BigDecimal salesExchangeAmount;
        //优惠 total_discount
        private BigDecimal totalDiscount = BigDecimal.ZERO;
        //抹零dynamic_amount
        private BigDecimal dynamicAmount = BigDecimal.ZERO;
        //实收 received_amount
        private BigDecimal receivedAmount = BigDecimal.ZERO;
        //欠款 owed_amount
        private BigDecimal owedAmount = BigDecimal.ZERO;
        //订单待回款金额 order_wait_payment_amount
        private BigDecimal orderWaitPaymentAmount = BigDecimal.ZERO;
        //优惠券优惠 coupon_discount
        private BigDecimal couponDiscount = BigDecimal.ZERO;
        //促销优惠 promotion_activities_discount_amount
        private BigDecimal promotionActivitiesDiscountAmount = BigDecimal.ZERO;
        //返利优惠 rebate_amount
        private BigDecimal rebateAmount = BigDecimal.ZERO;
        //订单金额 order_amount
        private BigDecimal orderAmount = BigDecimal.ZERO;

        //现金应交   回款单 收现金 金额  receive_cash
        private BigDecimal cashPayable = BigDecimal.ZERO;
        //收现金 receive_cash
        private BigDecimal receiveCash = BigDecimal.ZERO;
        //在线支付 online_payment
        private BigDecimal onlinePayment = BigDecimal.ZERO;
        //销售实收 门店当天所有回款用途=销售收款的回款单金额
        private BigDecimal salesReceivedAmount = BigDecimal.ZERO;
        //使用预存款 门店当天所有订单的非返利账户使用金额 use_receive
        private BigDecimal useReceive = BigDecimal.ZERO;
        //收欠款 门店当天所有回款用途=收欠款的回款单金额  receive_debt
        private BigDecimal receiveDebt = BigDecimal.ZERO;
        //receive_debt_cash
        private BigDecimal receiveDebtCash = BigDecimal.ZERO;
        //receive_debt_online_payment
        private BigDecimal receiveDebtOnlinePayment = BigDecimal.ZERO;
        //收预存款 门店当天所有回款用途=收预收款的回款单金额 receive_prepaid_deposit
        private BigDecimal receivePrepaidDeposit = BigDecimal.ZERO;
        //receive_prepaid_deposit_cash
        private BigDecimal receivePrepaidDepositCash = BigDecimal.ZERO;
        //receive_prepaid_deposit_online_payment
        private BigDecimal receivePrepaidDepositOnlinePayment = BigDecimal.ZERO;
        //退款金额refund_amount
        private BigDecimal refundAmount = BigDecimal.ZERO;
        //退货抵扣金额 return_goods_deduction_amount
        private BigDecimal returnGoodsDeductionAmount = BigDecimal.ZERO;
        //退货冲抵金额 return_goods_old_deduction_amount
        private BigDecimal returnGoodsOldDeductionAmount = BigDecimal.ZERO;
        //退货单.累计结算金额 total_settled_amount
        private BigDecimal totalSettledAmount = BigDecimal.ZERO;
        //退货单.累计结算金额 total_settled_amount，去掉换出大于换入的换货
        private BigDecimal totalSettledAmountV2 = BigDecimal.ZERO;
        //实退金额refunded_amount
        private BigDecimal refundedAmount = BigDecimal.ZERO;
        //delivery_amount
        private BigDecimal deliveryAmount = BigDecimal.ZERO;
        //退货入库金额 取退货单下入库单的合计金额
        private BigDecimal returnReceivedStorageAmount = BigDecimal.ZERO;
        //delivery_count
        private Integer deliveryCount = 0;

        private String planString;
        //销售订单
        private List<SalesOrder> salesOrderList = Lists.newArrayList();
        private String salesOrderListTag;
        //退货单
        private List<ReturnedOrder> returnedOrderList = Lists.newArrayList();
        private String returnedOrderListTag;

        private List<ExchangeOrder> exchangeOrderList = Lists.newArrayList();
        private String exchangeOrderListTag;


    }

    @Data
    @ToString
    class SalesOrder implements Serializable {

        private String objectApiName = ApiNames.SALES_ORDER_OBJ;

        private String orderId;
        //单号
        private String orderNum;
        //订货金额
        private BigDecimal salesAmount;
        //实收
        private BigDecimal receivedAmount;
        //欠款
        private BigDecimal owedAmount;
        //订单待回款金额
        private BigDecimal orderWaitPaymentAmount = BigDecimal.ZERO;
        //退货冲抵金额
        private BigDecimal returnGoodsOldDeductionAmount = BigDecimal.ZERO;
        //优惠金额
        private BigDecimal totalDiscount;
        //抹零金额
        private BigDecimal dynamicAmount;
        //使用预收款
        private BigDecimal useReceive;

        private String planString;
        //产品
        private OrderProductTable orderProductTable;
    }

    @Data
    @ToString
    class ReturnedOrder implements Serializable {

        private String objectApiName = ApiNames.RETURNED_GOODS_INVOICE_OBJ;

        private String orderId;
        //单号
        private String orderNum;

        private Boolean isRefunded = false;
        //应退
        private BigDecimal salesReturnAmount = BigDecimal.ZERO;
        //实退金额
        private BigDecimal refundedAmount = BigDecimal.ZERO;
        //退款金额
        private BigDecimal refundAmount = BigDecimal.ZERO;
        //退货单.累计结算金额
        private BigDecimal totalSettledAmount = BigDecimal.ZERO;
        //退货抵扣金额
        private BigDecimal returnGoodsDeductionAmount = BigDecimal.ZERO;
        private String planString;
        //产品
        private OrderProductTable orderProductTable;
    }

    @Data
    @ToString
    class ExchangeOrder implements Serializable {

        private String orderId;
        //单号
        private String orderNum;
        private BigDecimal salesExchangeAmount;
        private BigDecimal exchangeInAmount;
        private BigDecimal exchangeOutAmount;
        private String planString;
        //产品
        private ExchangeOrderProductTable orderProductTable = new ExchangeOrderProductTable();
    }

    @Data
    @ToString
    @NoArgsConstructor
    class OrderProductTable implements Serializable {
        private List<HeadOption> heads;
        //产品
        private List<OrderProduct> orderProductList = Lists.newArrayList();

        public OrderProductTable(String lang) {
            heads = Lists.newArrayList(
                    new HeadOption("productName", I18NSimpleUtils.get("SalesStatementsObj.AccountDetail.TableHead.productName", lang, "产品名称")).dataType(3),
                    new HeadOption("quantity", I18NSimpleUtils.get("SalesStatementsObj.AccountDetail.TableHead.quantity", lang, "数量")).dataType(3),
                    new HeadOption("unit", I18NSimpleUtils.get("SalesStatementsObj.AccountDetail.TableHead.unit", lang, "单位")),
                    new HeadOption("unitPrice", I18NSimpleUtils.get("SalesStatementsObj.AccountDetail.TableHead.unitPrice", lang, "单价")).isMoney(true),
                    new HeadOption("subtotal", I18NSimpleUtils.get("SalesStatementsObj.AccountDetail.TableHead.subtotal", lang, "小计")).isMoney(true));
        }
    }

    @Data
    @ToString
    class ExchangeOrderProductTable implements Serializable {
        private List<HeadOption> heads;
        //产品
        private List<ExchangeOrderProduct> orderProductList = Lists.newArrayList();

        public ExchangeOrderProductTable() {
            heads = Lists.newArrayList(
                    new HeadOption("exchangeType", "类型"),
                    new HeadOption("productName", "产品名称"),
                    new HeadOption("quantity", "数量"),
                    new HeadOption("unit", "单位"),
                    new HeadOption("unitPrice", "单价"),
                    new HeadOption("subtotal", "小计"));
        }
    }


    @Data
    @ToString
    class OrderProduct implements Serializable {
        //产品名称
        private String productId;
        //产品name
        private String productName;
        //是否赠品  "0":否  "1":是
        private String isGiveaway;
        //数量
        private BigDecimal quantity;
        //单位
        private String unit;
        //单价
        private String unitPrice;
        //小计
        private BigDecimal subtotal;
    }

    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    class ExchangeOrderProduct extends OrderProduct {
        private String type;
    }

}
