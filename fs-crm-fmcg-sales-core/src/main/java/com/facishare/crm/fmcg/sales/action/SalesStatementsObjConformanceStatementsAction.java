package com.facishare.crm.fmcg.sales.action;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.sales.model.statement.SalesStatementsConformance;
import com.facishare.crm.fmcg.sales.service.SalesStatementsService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
public class SalesStatementsObjConformanceStatementsAction extends PreDefineAction<SalesStatementsObjConformanceStatementsAction.Arg, SalesStatementsObjConformanceStatementsAction.Result> {

    private IObjectData objectData;
    //结清
    public static final String CLOSE_OFF = "2";
    public static final String OPEN = "1";
    public static final String CLOSE = "0";


    private final SalesStatementsService salesStatementsService = SpringUtil.getContext().getBean(SalesStatementsService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.Conformance_Statements.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getObjectDataId());
    }

    @Override
    protected void init() {
        super.init();
        if (CollectionUtils.notEmpty(dataList)) {
            objectData = dataList.get(0);
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        log.info("actionContext={},objectData{}", actionContext, objectData);
        //修改对账单状态
        Map<String, Object> updateMap = new HashMap<>(4);
        updateMap.put("statement_status", OPEN);
        updateMap.put("payer", Lists.newArrayList(actionContext.getUser().getUserId()));
        if (Objects.nonNull(arg.getArgs())) {
            if (arg.getArgs().containsKey("form_actual_payment")) {
                updateMap.put("form_actual_payment", arg.getArgs().get("form_actual_payment"));
            }
        }
        ServiceContext context = new ServiceContext(actionContext.getRequestContext(), "", "");
        IObjectData data = salesStatementsService.updateStatementsStatus(objectData, updateMap, OPEN, objectDescribe, context);
        return Result.of(data);
    }

    @Data
    public static class Arg {
        private String objectDataId;

        private JSONObject args;
    }

    @Data
    public static class Result {
        private ObjectDataDocument objectData;

        public static Result of(IObjectData objectData) {
            Result result = new Result();
            result.setObjectData(ObjectDataDocument.of(objectData));
            return result;
        }
    }
}
