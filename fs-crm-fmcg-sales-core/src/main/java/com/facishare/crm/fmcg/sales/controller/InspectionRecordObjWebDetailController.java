package com.facishare.crm.fmcg.sales.controller;

import com.facishare.crm.fmcg.sales.utils.ButtonUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.google.common.collect.Sets;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> l<PERSON><PERSON><PERSON>
 * @create : 2025-03-04 14:24
 **/
public class InspectionRecordObjWebDetailController extends StandardWebDetailController {
    @Override
    protected Result after(Arg arg, Result result) {
        buttonFilter(arg, result);
        return super.after(arg, result);
    }

    public void buttonFilter(Arg arg, Result result) {
        for (Object component : (ArrayList) result.getLayout().get("components")) {
            Map com = (Map) component;
            if ("head_info".equals(com.get("api_name"))) {
                ButtonUtils.deleteOtherThanTheSpecifiedButtonActions((ArrayList) com.get("buttons"),
                        Sets.newHashSet("Edit", "InspectionRecordAppeal", "InspectionRecordOk"), true);
                return;
            }
        }
    }
}