package com.facishare.crm.fmcg.sales.utils;


import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;

public class MengniuEncryptedMarkCode {
    public static String getEncryptedMarkCode(String markCode) {
        String deCodeStr = sha256(sha256(markCode)).toUpperCase();
        return deCodeStr;
    }

    private static String sha256(String markCode) {
        MessageDigest md = null;
        String strDes = null;
        byte[] bt = markCode.getBytes();
        try {
            md = MessageDigest.getInstance("SHA-256");
            md.update(bt);
            strDes = bytes2Hex(md.digest());
        } catch (NoSuchAlgorithmException e) {
            return null;
        }
        return strDes;
    }

    private static String bytes2Hex(byte[] bts) {
        String des = "";
        String tmp = null;
        for (int i = 0; i < bts.length; i++) {
            tmp = Integer.toHexString(bts[i] & 0xFF);
            if (tmp.length() == 1)
                des = des + "0";
            des = des + tmp;
        }
        return des;
    }

    public static void main(String[] arg0) {
        List<String> codes = new ArrayList();
        codes.add("1330000111122220016");
        codes.add("134111111111111111");
        for (String code : codes) {
            String encryptedMarkCode = getEncryptedMarkCode(code);
            System.out.println(code + " : " + encryptedMarkCode);
        }

    }
}

