package com.facishare.crm.fmcg.sales.model.inspection;

import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @create: 2025-04-15 11:25
 **/
public interface FillExtraField {
    @Data
    @ToString
    class Context {
        private InspectionVerify.Context verifyContext;
        private List<IObjectData> objectDataList;
        private Map<String, IObjectData> productIdMap = Maps.newHashMap();
        private Map<String, IObjectData> productCategoryIdMap = Maps.newHashMap();
        private Map<String, IObjectData> belongAccountIdMap = Maps.newHashMap();
        private Map<String, Object> inspectionPersonnelInfo = Maps.newHashMap();

        public Context(InspectionVerify.Context verifyContext, List<IObjectData> objectDataList) {
            this.verifyContext = verifyContext;
            this.objectDataList = objectDataList;
        }
    }
}
