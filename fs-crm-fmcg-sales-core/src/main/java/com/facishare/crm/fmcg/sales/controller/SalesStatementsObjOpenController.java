package com.facishare.crm.fmcg.sales.controller;

import com.facishare.crm.fmcg.sales.service.SalesStatementsService;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.api.model.BaseResult;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class SalesStatementsObjOpenController extends PreDefineController<SalesStatementsObjOpenController.Arg, BaseResult> {

    private static final SalesStatementsService salesStatementsService = SpringUtil.getContext().getBean(SalesStatementsService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }

    @Override
    protected BaseResult doService(Arg arg) {
        salesStatementsService.initSalesStatementsModule(controllerContext.getTenantId());
        BaseResult baseResult = new BaseResult();
        baseResult.setErrorCode(0);
        baseResult.setErrorMsg("success");
        return baseResult;
    }

    @Data
    @ToString
    static class Arg implements Serializable {
    }
}
