package com.facishare.crm.fmcg.sales.model.inspection.designer;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.api.model.BaseResult;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;
import java.util.Map;

public interface InspectionDesignerAddVO {
    @Data
    @ToString
    class Arg {
        private List<Integer> effectiveUserIds;
        private List<Integer> unEffectiveUserIds;
        private Map<String, JSONObject> data;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @ToString
    class Result extends BaseResult {


        public static Result success() {
            Result result = new Result();
            result.setErrorMsg("success");
            result.setErrorCode(0);
            return result;
        }

        public static Result error(String msg) {
            Result result = new Result();
            result.setErrorMsg(msg);
            result.setErrorCode(400);
            return result;
        }

    }
}
