package com.facishare.crm.fmcg.sales.model.inspection;

import com.alibaba.fastjson.JSONObject;
import com.facishare.common.FsStopWatch;
import com.facishare.crm.fmcg.sales.apiname.InspectionRecordObjApiNames;
import com.facishare.crm.fmcg.sales.business.SnBusiness;
import com.facishare.crm.fmcg.sales.exception.InspectionVerifyErrorInfo;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.fxiaoke.api.model.BaseResult;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * @author: liuhaoyu
 * @create: 2024-07-12 16:29
 **/
public interface InspectionVerify {
    @Data
    @ToString
    class Arg {
        /**
         * 伪码
         */
        private String snName;
        /**
         * 真码
         */
        private String sourceCode;
        /**
         * 原始代码
         */
        private String originalCode;
        private ObjectDataDocument objectData;
        private Boolean recheckFlag;
        private String ruleId;
        private List<ObjectDataDocument> batchSaveData;
        private Map<String, ObjectDataDocument> extraSaveData;
    }

    @Data
    @ToString
    class Result extends BaseResult {
        private ObjectDataDocument objectData;
        private InspectionVerifyErrorInfo errorInfo;
        private String message;
        private Boolean existInspection = false;
        private Integer inspectionQuantity;

        public static Result success(Context context) {
            Result result = new Result();
            result.setObjectData(ObjectDataDocument.of(context.getObjectData()));
            result.setInspectionQuantity(1);
            if (CollectionUtils.isNotEmpty(context.getObjectData().get(InspectionRecordObjApiNames.BOX_CODE_IDS_TEMP, List.class))) {
                result.setInspectionQuantity(context.getObjectData().get(InspectionRecordObjApiNames.BOX_CODE_IDS_TEMP, List.class).size());
            }
            result.setExistInspection(context.getExistInspection());
            result.setErrorCode(0);
            return result;
        }

        public static Result error(Integer errorCode, InspectionVerifyErrorInfo errorInfo) {
            Result result = new Result();
            result.setErrorCode(errorCode);
            result.setErrorInfo(errorInfo);
            result.setMessage(errorInfo.getTitle());
            if (StringUtils.isNotEmpty(errorInfo.getSubTitle())) {
                result.setMessage(errorInfo.getTitle() + "," + errorInfo.getSubTitle());
            }
            return result;
        }
    }

    @Data
    @ToString
    @NoArgsConstructor
    class Context {
        private String snName;
        private String sourceCode;
        private String originalCode;
        /**
         * 码来源 local/external
         */
        private String codeSource = "local";
        private IObjectData objectData;
        private String tenantId;
        private User user;
        private User systemUser;
        private String upstreamTenantId;
        private Boolean recheckFlag;
        private Boolean existInspection = false;
        private IObjectData inspectionLogData;
        private String inspectionDesignerId;
        private Map<String, JSONObject> inspectionDesignerData;
        private IObjectData curPersonnelInfo;
        private IObjectData curAccountInfoInUpstream;
        private List<IObjectData> batchSaveData;
        private Map<String, IObjectData> extraSaveData;
        private List<IObjectData> boxRecordDataList;
        private FsStopWatch fsStopWatch = new FsStopWatch("inspectionVerifyStopWatch");

        public Context(InspectionVerify.Arg arg, ServiceContext serviceContext) {
            this.snName = arg.getSnName();
            this.sourceCode = arg.getSourceCode();
            this.originalCode = arg.getOriginalCode();
            if (Objects.nonNull(arg.getObjectData())) {
                this.objectData = arg.getObjectData().toObjectData();
            }
            this.tenantId = serviceContext.getTenantId();
            this.user = serviceContext.getUser();
            this.systemUser = User.systemUser(serviceContext.getTenantId());
            this.recheckFlag = arg.getRecheckFlag();
            this.inspectionLogData = new ObjectData();
            if (CollectionUtils.isNotEmpty(arg.getBatchSaveData())) {
                this.batchSaveData = arg.getBatchSaveData().stream()
                        .map(ObjectDataDocument::toObjectData)
                        .collect(Collectors.toList());
            }
            if (MapUtils.isNotEmpty(arg.getExtraSaveData())) {
                this.extraSaveData = arg.getExtraSaveData().entrySet().stream()
                        .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().toObjectData()));
            }
            this.getObjectData().set(InspectionRecordObjApiNames.CODE_TYPE_TEMP, SnBusiness.getCodeType(serviceContext.getTenantId(), arg.getSourceCode()));
        }
    }
}