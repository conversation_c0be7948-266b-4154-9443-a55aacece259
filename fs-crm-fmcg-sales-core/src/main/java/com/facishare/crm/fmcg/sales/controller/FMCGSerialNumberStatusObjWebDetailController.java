package com.facishare.crm.fmcg.sales.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> l<PERSON><PERSON><PERSON>
 * @create : 2023-08-16 11:51
 **/
public class FMCGSerialNumberStatusObjWebDetailController extends StandardWebDetailController {
    @Override
    protected Result after(Arg arg, Result result) {
        buttonFilter(arg, result);
        return super.after(arg, result);
    }

    public void buttonFilter(Arg arg, Result result) {
        List components = (ArrayList) result.getLayout().get("components");

        for (Object component : components) {
            Map com = (Map) component;
            if ("head_info".equals(com.get("api_name"))) {
                com.put("buttons", Lists.newArrayList());
                return;
            }
        }
    }
}
