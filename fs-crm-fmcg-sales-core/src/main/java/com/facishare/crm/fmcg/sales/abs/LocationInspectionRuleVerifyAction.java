package com.facishare.crm.fmcg.sales.abs;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.sales.apiname.AccountObjApiNames;
import com.facishare.crm.fmcg.sales.apiname.InspectionRecordObjApiNames;
import com.facishare.crm.fmcg.sales.business.CheckinBusiness;
import com.facishare.crm.fmcg.sales.enums.MagicEnum;
import com.facishare.crm.fmcg.sales.exception.InspectionVerifyErrorInfo;
import com.facishare.crm.fmcg.sales.exception.InspectionVerifyFailureInterruptException;
import com.facishare.crm.fmcg.sales.model.inspection.InspectionVerify;
import com.facishare.crm.fmcg.sales.utils.I18nUtil;
import com.facishare.crm.fmcg.sales.utils.LocationUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> liuhaoyu
 * @create : 2024-07-15 17:11
 **/
@Slf4j
@Component("locationInspectionRuleVerifyAction")
public class LocationInspectionRuleVerifyAction extends AbstractionInspectionRuleVerifyAction {
    @Resource
    private CheckinBusiness checkinBusiness;

    @Override
    protected void contextVerify(InspectionVerify.Context context) {
        super.contextVerify(context);
        if (Objects.isNull(context.getObjectData().get(InspectionRecordObjApiNames.CURRENT_AREA_LOCATION))) {
            throw new InspectionVerifyFailureInterruptException(100002,
                new InspectionVerifyErrorInfo(I18nUtil.get(MagicEnum.currentLocationNotFound), null));
        }
    }

    @Override
    public void verify(InspectionVerify.Context context) {
        super.handleSnBelongData(context);
        //获取码归属客户的销售区域
        List<String> belongAccountSalesAreaIds = getSalesAreaIdsByAccountId(User.systemUser(context.getUpstreamTenantId()),
                context.getObjectData().get(InspectionRecordObjApiNames.BELONG_ACCOUNT_ID, String.class), context);
        context.getFsStopWatch().lap("getSalesAreaIdsByAccountId");
        if (CollectionUtils.isEmpty(belongAccountSalesAreaIds)) {
            throw new InspectionVerifyFailureInterruptException(100001,
                new InspectionVerifyErrorInfo(
                    I18nUtil.get(MagicEnum.salesAreaNotSet),
                    I18nUtil.get(MagicEnum.contactSalesManager)
                )
            );
        }

        String currentAreaLocation = context.getObjectData().get(InspectionRecordObjApiNames.CURRENT_AREA_LOCATION, String.class);
        String[] locations = LocationUtils.analyticLocation(currentAreaLocation);

        List<String> salesAreaByPoint = checkinBusiness.getSalesAreaByPoint(Integer.parseInt(context.getUpstreamTenantId()), String.format("%s,%s", locations[0], locations[1]));
        context.getFsStopWatch().lap("getSalesAreaByPoint");
        if (CollectionUtils.isEmpty(belongAccountSalesAreaIds)) {
            throw new InspectionVerifyFailureInterruptException(100006,
                new InspectionVerifyErrorInfo(I18nUtil.get(MagicEnum.salesAreaFetchFailed), null));
        }

        boolean belongTo = !Collections.disjoint(belongAccountSalesAreaIds, salesAreaByPoint);
        if (belongTo) {
            context.getObjectData().set(InspectionRecordObjApiNames.INSPECTION_RESULT, "0");
        } else {
            context.getObjectData().set(InspectionRecordObjApiNames.INSPECTION_RESULT, "1");
            context.getObjectData().set(InspectionRecordObjApiNames.ILLEGALLY_GOODS_ACCOUNT_ID, context.getObjectData().get(InspectionRecordObjApiNames.BELONG_ACCOUNT_ID, String.class));
        }
    }

    private List<String> getSalesAreaIdsByAccountId(User user, String accountId, InspectionVerify.Context context) {
        IObjectData accountObj = serviceFacade.findObjectData(user, accountId, AccountObjApiNames.OBJECT_API_NAME);
        if (Objects.isNull(accountObj.get(AccountObjApiNames.COVERING_SALES_AREAS)) || CollectionUtils.isEmpty(accountObj.get(AccountObjApiNames.COVERING_SALES_AREAS, List.class))) {
            return Lists.newArrayList();
        }
        String jsonString = JSON.toJSONString(accountObj.get(AccountObjApiNames.COVERING_SALES_AREAS));
        return JSONObject.parseArray(jsonString, String.class);
    }
}
