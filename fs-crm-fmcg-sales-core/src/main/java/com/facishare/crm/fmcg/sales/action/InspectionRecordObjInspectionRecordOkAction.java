package com.facishare.crm.fmcg.sales.action;

import com.facishare.crm.fmcg.sales.apiname.InspectionRecordObjApiNames;
import com.facishare.crm.fmcg.sales.business.InspectionBusiness;
import com.facishare.crm.fmcg.sales.enums.MagicEnum;
import com.facishare.crm.fmcg.sales.pool.InspectionBusinessPool;
import com.facishare.crm.fmcg.sales.task.gnomon.GnomonNomonService;
import com.facishare.crm.fmcg.sales.utils.I18nUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> liuhaoyu
 * @create : 2024-07-18 18:05
 **/
public class InspectionRecordObjInspectionRecordOkAction extends PreDefineAction<InspectionRecordObjInspectionRecordOkAction.Arg, InspectionRecordObjInspectionRecordOkAction.Result> {
    private IObjectData objectData;

    private final InspectionBusiness inspectionBusiness = SpringUtil.getContext().getBean(InspectionBusiness.class);
    private final GnomonNomonService gnomonNomonService = SpringUtil.getContext().getBean(GnomonNomonService.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        Boolean illegallyGoodsAccountOwner = inspectionBusiness.isIllegallyGoodsAccountOwner(actionContext.getUser(),
                objectData.get(InspectionRecordObjApiNames.ILLEGALLY_GOODS_ACCOUNT_ID, String.class));
        if (!illegallyGoodsAccountOwner) {
            throw new ValidateException(I18nUtil.get(MagicEnum.noOperationPermission));
        }
        if (!"0".equals(objectData.get(InspectionRecordObjApiNames.PROCESSING_STATE, String.class))) {
            throw new ValidateException(I18nUtil.get(MagicEnum.currentDataHasBeenProcessed));
        }
        if (!"1".equals(objectData.get(InspectionRecordObjApiNames.INSPECTION_RESULT, String.class))) {
            throw new ValidateException(I18nUtil.get(MagicEnum.currentDataIsNotAbnormal));
        }
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.INSPECTION_RECORD_OK.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getObjectDataId());
    }

    @Override
    protected void init() {
        super.init();
        if (CollectionUtils.notEmpty(dataList)) {
            objectData = dataList.get(0);
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        Map<String, Object> updateMap = Maps.newHashMap();
        updateMap.put(InspectionRecordObjApiNames.PROCESSING_STATE, "2");
        IObjectData data = serviceFacade.updateWithMap(actionContext.getUser(), objectData, updateMap);
        //删除窜货自动确认任务
        InspectionBusinessPool.execute(() -> {
            try {
                gnomonNomonService.sendDeleteIllegallyGoodsAutomaticConfirmationTask(actionContext.getTenantId(), objectData.getId());
            } catch (Exception e) {
                log.error("okAction sendDeleteIllegallyGoodsAutomaticConfirmationTask Error", e);
            }
        });
        return Result.of(data);
    }

    @Data
    public static class Arg {
        private String objectDataId;
    }

    @Data
    public static class Result {
        private ObjectDataDocument objectData;

        public static Result of(IObjectData objectData) {
            Result result = new Result();
            result.setObjectData(ObjectDataDocument.of(objectData));
            return result;
        }
    }
}
