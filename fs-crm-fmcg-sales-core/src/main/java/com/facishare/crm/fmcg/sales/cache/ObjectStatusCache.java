package com.facishare.crm.fmcg.sales.cache;

import com.facishare.crm.fmcg.sales.exception.ApiException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.notifier.support.NotifierClient;
import com.google.common.base.Splitter;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Iterator;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> liuhaoyu
 **/
@Component
@Slf4j
public class ObjectStatusCache {
    public static final String DESCRIBE_CHANGE_EVENT = "describe-change-event";

    @Resource
    private ServiceFacade serviceFacade;

    private LoadingCache<String, Status> objectStatusCache;

    @PostConstruct
    public void init() {
        objectStatusCache = CacheBuilder.newBuilder()
                .maximumSize(5000)
                .expireAfterWrite(2, TimeUnit.HOURS)
                .build(new CacheLoader<String, Status>() {
                    @Override
                    public Status load(String key) {
                        return handleCache(key);
                    }
                });
        listenDescribeChangeEventUseFastNotify();
    }

    private Status handleCache(String key) {
        String[] keys = splitKey(key);
        if (keys.length != 2) {
            throw new ApiException(20001, "ObjectIsPublicCache key is error");
        }
        // ObjectDefNotFoundError
        IObjectDescribe objectDescribe = serviceFacade.findObject(keys[0], keys[1]);
        Status status = new Status();
        status.setPublicObject(objectDescribe.isPublicObject());
        status.setUpstreamTenantId(objectDescribe.isPublicObject() ? objectDescribe.getUpstreamTenantId() : null);
        return status;
    }

    public Status getNoException(String tenantId, String describeApiName) {
        Status status;
        try {
            status = objectStatusCache.get(buildKey(tenantId, describeApiName));
        } catch (Exception e) {
            log.error("get objectStatusCache is error", e);
            throw new ApiException(300001, "get objectStatusCache is error");
        }
        return status;
    }

    private void listenDescribeChangeEventUseFastNotify() {
        NotifierClient.register(DESCRIBE_CHANGE_EVENT, (message) -> {
            dealSubMessage(message.getContent());
        });
    }

    void dealSubMessage(String message) {
        Iterator<String> it = Splitter.on('\u0004').split(message).iterator();
        String tenantId = it.next();
        String describeApiName = it.next();
        clear(buildKey(tenantId, describeApiName));
    }

    public String buildKey(String tenantId, String describeApiName) {
        return String.format("%s#$%s", tenantId, describeApiName);
    }

    public String[] splitKey(String key) {
        return key.split("#\\$");
    }

    public void clear(String key) {
        if (containsKey(key)) {
            objectStatusCache.invalidate(key);
            log.info("clear invalidate cache {}", key);
        }
    }

    public Boolean containsKey(String key) {
        return objectStatusCache.asMap().containsKey(key);
    }

    @Data
    @ToString
    public static class Status {
        private Boolean publicObject;
        private String upstreamTenantId;
    }
}
