package com.facishare.crm.fmcg.sales.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.sales.apiname.MultiUnitRelatedApiNames;
import com.facishare.crm.fmcg.sales.apiname.SalesOrderApiNames;
import com.facishare.crm.fmcg.sales.business.MultipleUnitBusiness;
import com.facishare.crm.fmcg.sales.cache.FMCGSNOpenStatusCache;
import com.facishare.crm.fmcg.sales.constants.RoleCode;
import com.facishare.crm.fmcg.sales.enums.BizConfKeyEnums;
import com.facishare.crm.fmcg.sales.enums.ScanEncryptionTypeEnum;
import com.facishare.crm.fmcg.sales.model.TaskManager;
import com.facishare.crm.fmcg.sales.model.sn.*;
import com.facishare.crm.fmcg.sales.service.abstraction.IFMCGSerialNumberService;
import com.facishare.crm.fmcg.sales.utils.AppLinkUtils;
import com.facishare.crm.fmcg.sales.utils.FMCGConfigUtil;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.common.release.GrayRelease;
import com.fxiaoke.notifier.support.NotifierClient;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> liuhaoyu
 * @create : 2023-07-10 18:22
 **/
@Service
@Slf4j
@ServiceModule("fmcg_serial_number_service")
public class FMCGSerialNumberService implements IFMCGSerialNumberService {

    private static final String OPEN_BIZ_CONF_PRE = "metadata_sys_is_open_";
    private static final String OPEN_FMCG_SERIAL_NUMBER_CONFIG_KEY = "metadata_sys_is_open_FMCGSerialNumberObj";
    private static final String OPEN_FMCG_SERIAL_NUMBER_STATUS_CONFIG_KEY = "metadata_sys_is_open_FMCGSerialNumberStatusObj";
    private static final String OPEN_CROUP_PILE_RATIO_CONFIG_KEY = "metadata_sys_is_open_GroupPileRatioObj";

    private static final String ENABLE_FMCG_SERIAL_NUMBER_ROOM = "ENABLE_FMCG_SERIAL_NUMBER_ROOM";
    private static final String ENABLE_FMCG_SERIAL_NUMBER_VALUE_PREFIX = "FMCG_SERIAL_NUMBER_";

    @Resource
    private ConfigService configService;

    @Resource
    private DescribeManagerService describeManagerService;

    @Resource
    private ButtonManagerService buttonManagerService;
    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private MultipleUnitBusiness multipleUnitBusiness;
    @Resource
    private FMCGSNOpenStatusCache fmcgsnOpenStatusCache;

    @Override
    @ServiceMethod("init_sn_scene_v2")
    public FMCGSerialNumberInItScene.Result initFMCGSerialNumberSceneV2(FMCGSerialNumberInItScene.Arg arg, ServiceContext serviceContext) {
        FMCGSerialNumberInItScene.Result result = new FMCGSerialNumberInItScene.Result();
        User superUser = User.systemUser(serviceContext.getTenantId());

        TaskManager taskManager = initTaskRegistry(serviceContext, superUser);
        List<String> taskKeysToExecute = CollectionUtils.isNotEmpty(arg.getTaskKeys()) ? arg.getTaskKeys() : Lists.newArrayList(taskManager.getAllTaskKeys());

        try {
            Map<String, TaskManager.TaskResult> taskResults = taskManager.execute(taskKeysToExecute);

            List<String> failedTasks = taskResults.values().stream()
                    .filter(r -> !r.isSuccess())
                    .map(TaskManager.TaskResult::getTaskKey)
                    .collect(Collectors.toList());

            if (!failedTasks.isEmpty()) {
                log.info("Some tasks failed: {}", String.join(", ", failedTasks));
                result.setErrorCode(1);
                result.setErrorMsg("Some tasks failed: " + String.join(", ", failedTasks));
                return result;
            }

            result.setErrorCode(0);
            result.setErrorMsg("success");
        } catch (Exception e) {
            log.error("Task execution exception", e);
            result.setErrorCode(1);
            result.setErrorMsg("Task execution exception: " + e.getMessage());
        }
        return result;
    }

    private TaskManager initTaskRegistry(ServiceContext serviceContext, User superUser) {
        TaskManager taskManager = new TaskManager();

        taskManager.register("add_field_DeliveryNoteObj_signing_receive_address", () -> describeManagerService.addFields(serviceContext.getTenantId(), "DeliveryNoteObj", "signing_receive_address"))
                .register("add_field_SalesOrderProductObj_unique_product_code_combination", () -> describeManagerService.addFields(serviceContext.getTenantId(), "SalesOrderProductObj", "unique_product_code_combination"))
                .register("add_field_ProductObj_turnover_box_of_numbers", () -> describeManagerService.addFields(serviceContext.getTenantId(), "ProductObj", "turnover_box_of_numbers"));

        taskManager.register("open_FMCGSerialNumberObj", () -> configService.upsertTenantConfig(superUser, OPEN_FMCG_SERIAL_NUMBER_CONFIG_KEY, "true", ConfigValueType.STRING))
                .register("open_FMCGSerialNumberStatusObj", () -> configService.upsertTenantConfig(superUser, OPEN_FMCG_SERIAL_NUMBER_STATUS_CONFIG_KEY, "true", ConfigValueType.STRING))
                .register("open_GroupPileRatioObj", () -> configService.upsertTenantConfig(superUser, OPEN_CROUP_PILE_RATIO_CONFIG_KEY, "true", ConfigValueType.STRING));

        List<String> roles = Lists.newArrayList(RoleCode.SYSTEM_ADMINISTRATOR);

        taskManager.register("create_btn_PurchaseOrderObj_scan_receive_order", () -> {
                    String receiveButtonStr = "{\"describe_api_name\":\"PurchaseOrderObj\",\"api_name\":\"scan_receive_order__c\",\"label\":\"扫码签收\",\"define_type\":\"custom\",\"button_type\":\"common\",\"redirect_type\":\"\",\"description\":\"\",\"use_pages\":[\"list\",\"detail\"],\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"field_name\":\"goods_received_status\",\"field_values\":[\"1\",\"2\"],\"operator\":\"HASANYOF\",\"value_type\":0}]}],\"param_form\":[],\"jump_url\":\"\",\"is_active\":true,\"is_deleted\":false,\"lock_data_show_button\":false}";//ingoreI18n
                    buttonManagerService.createButtonByRole(serviceContext, receiveButtonStr, roles);
                })
                .register("create_btn_SalesOrderObj_scan_delivery", () -> {
                    String deliveryButtonStr = "{\"describe_api_name\":\"SalesOrderObj\",\"api_name\":\"scan_delivery__c\",\"label\":\"扫码出库\",\"define_type\":\"custom\",\"button_type\":\"common\",\"redirect_type\":\"\",\"description\":\"\",\"use_pages\":[\"detail\",\"list\"],\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"field_name\":\"logistics_status\",\"field_values\":[\"2\",\"1\"],\"operator\":\"HASANYOF\",\"value_type\":0}]}],\"param_form\":[],\"jump_url\":\"\",\"is_active\":true,\"is_deleted\":false,\"lock_data_show_button\":false}";//ingoreI18n
                    buttonManagerService.createButtonByRole(serviceContext, deliveryButtonStr, roles);
                })
                .register("create_btn_DeliveryNoteObj_scan_receipt_store", () -> {
                    String deliveryScanButtonStr = "{\"describe_api_name\":\"DeliveryNoteObj\",\"api_name\":\"scan_receipt_store__c\",\"label\":\"扫码签收\",\"define_type\":\"custom\",\"button_type\":\"common\",\"redirect_type\":\"\",\"description\":\"生成签收码\",\"use_pages\":[\"detail\",\"list\",\"list_batch\",\"related_list\"],\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"field_name\":\"status\",\"field_values\":[\"has_delivered\"],\"operator\":\"EQ\",\"value_type\":0}]}],\"param_form\":[],\"jump_url\":\"\",\"is_active\":true,\"is_deleted\":false,\"lock_data_show_button\":false}";//ingoreI18n
                    buttonManagerService.createButtonByRole(serviceContext, deliveryScanButtonStr, roles);
                })
                .register("create_btn_GoodsReceivedNoteObj_scan_received_from_purchase", () -> {
                    String receiveToReturnButtonStr = "{\"describe_api_name\":\"GoodsReceivedNoteObj\",\"api_name\":\"scan_received_from_purchase__c\",\"label\":\"扫码退货\",\"define_type\":\"custom\",\"button_type\":\"common\",\"redirect_type\":\"\",\"description\":\"\",\"use_pages\":[\"detail\",\"list\"],\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"field_name\":\"purchase_return_status\",\"field_values\":[\"1\",\"2\"],\"operator\":\"HASANYOF\",\"value_type\":0}]}],\"param_form\":[],\"jump_url\":\"\",\"is_active\":true,\"is_deleted\":false,\"lock_data_show_button\":false}";//ingoreI18n
                    buttonManagerService.createButtonByRole(serviceContext, receiveToReturnButtonStr, roles);
                })
                .register("create_btn_SalesOrderObj_offline_payment", () -> addCreatePaymentButton(new AddCreatePaymentButton.Arg(), serviceContext));

        taskManager.register("send_notify_sn_room", () -> NotifierClient.send(ENABLE_FMCG_SERIAL_NUMBER_ROOM, ENABLE_FMCG_SERIAL_NUMBER_VALUE_PREFIX + serviceContext.getTenantId()));
        taskManager.register("save_app_link_scan_serial", () -> AppLinkUtils.saveAppLink(serviceContext.getEa(), "SCAN_SERIAL", "SCAN_SERIAL"));
        return taskManager;
    }

    @Override
    @ServiceMethod("init_sn_scene")
    public FMCGSerialNumberInItScene.Result initFMCGSerialNumberScene(FMCGSerialNumberInItScene.Arg arg, ServiceContext serviceContext) {
        FMCGSerialNumberInItScene.Result result = new FMCGSerialNumberInItScene.Result();
        User superUser = User.systemUser(serviceContext.getTenantId());
        try {
            describeManagerService.addFields(serviceContext.getTenantId(), "DeliveryNoteObj", "signing_receive_address");
            describeManagerService.addFields(serviceContext.getTenantId(), "SalesOrderProductObj", "unique_product_code_combination");
            describeManagerService.addFields(serviceContext.getTenantId(), "ProductObj", "turnover_box_of_numbers");
            // addIsScanCode(new AddIsScanCode.Arg(), serviceContext);

            configService.upsertTenantConfig(superUser, OPEN_FMCG_SERIAL_NUMBER_CONFIG_KEY, "true", ConfigValueType.STRING);
            configService.upsertTenantConfig(superUser, OPEN_FMCG_SERIAL_NUMBER_STATUS_CONFIG_KEY, "true", ConfigValueType.STRING);
            configService.upsertTenantConfig(superUser, OPEN_CROUP_PILE_RATIO_CONFIG_KEY, "true", ConfigValueType.STRING);

            List<String> roles = Lists.newArrayList(RoleCode.SYSTEM_ADMINISTRATOR);
            String receiveButtonStr = "{\"describe_api_name\":\"PurchaseOrderObj\",\"api_name\":\"scan_receive_order__c\",\"label\":\"扫码签收\",\"define_type\":\"custom\",\"button_type\":\"common\",\"redirect_type\":\"\",\"description\":\"\",\"use_pages\":[\"list\",\"detail\"],\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"field_name\":\"goods_received_status\",\"field_values\":[\"1\",\"2\"],\"operator\":\"HASANYOF\",\"value_type\":0}]}],\"param_form\":[],\"jump_url\":\"\",\"is_active\":true,\"is_deleted\":false,\"lock_data_show_button\":false}";//ingoreI18n
            buttonManagerService.createButtonByRole(serviceContext, receiveButtonStr, roles);
            String deliveryButtonStr = "{\"describe_api_name\":\"SalesOrderObj\",\"api_name\":\"scan_delivery__c\",\"label\":\"扫码出库\",\"define_type\":\"custom\",\"button_type\":\"common\",\"redirect_type\":\"\",\"description\":\"\",\"use_pages\":[\"detail\",\"list\"],\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"field_name\":\"logistics_status\",\"field_values\":[\"2\",\"1\"],\"operator\":\"HASANYOF\",\"value_type\":0}]}],\"param_form\":[],\"jump_url\":\"\",\"is_active\":true,\"is_deleted\":false,\"lock_data_show_button\":false}";//ingoreI18n
            buttonManagerService.createButtonByRole(serviceContext, deliveryButtonStr, roles);
            String deliveryScanButtonStr = "{\"describe_api_name\":\"DeliveryNoteObj\",\"api_name\":\"scan_receipt_store__c\",\"label\":\"扫码签收\",\"define_type\":\"custom\",\"button_type\":\"common\",\"redirect_type\":\"\",\"description\":\"生成签收码\",\"use_pages\":[\"detail\",\"list\",\"list_batch\",\"related_list\"],\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"field_name\":\"status\",\"field_values\":[\"has_delivered\"],\"operator\":\"EQ\",\"value_type\":0}]}],\"param_form\":[],\"jump_url\":\"\",\"is_active\":true,\"is_deleted\":false,\"lock_data_show_button\":false}";//ingoreI18n
            buttonManagerService.createButtonByRole(serviceContext, deliveryScanButtonStr, roles);
            String receiveToReturnButtonStr = "{\"describe_api_name\":\"GoodsReceivedNoteObj\",\"api_name\":\"scan_received_from_purchase__c\",\"label\":\"扫码退货\",\"define_type\":\"custom\",\"button_type\":\"common\",\"redirect_type\":\"\",\"description\":\"\",\"use_pages\":[\"detail\",\"list\"],\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"field_name\":\"purchase_return_status\",\"field_values\":[\"1\",\"2\"],\"operator\":\"HASANYOF\",\"value_type\":0}]}],\"param_form\":[],\"jump_url\":\"\",\"is_active\":true,\"is_deleted\":false,\"lock_data_show_button\":false}";//ingoreI18n
            buttonManagerService.createButtonByRole(serviceContext, receiveToReturnButtonStr, roles);
            addCreatePaymentButton(new AddCreatePaymentButton.Arg(), serviceContext);

            NotifierClient.send(ENABLE_FMCG_SERIAL_NUMBER_ROOM, ENABLE_FMCG_SERIAL_NUMBER_VALUE_PREFIX + serviceContext.getTenantId());
            AppLinkUtils.saveAppLink(serviceContext.getEa(), "SCAN_SERIAL", "SCAN_SERIAL");
        } catch (Exception e) {
            log.error("init_sn_scene error", e);
            result.setErrorCode(1);
            result.setErrorMsg("system error");
            return result;
        }
        result.setErrorCode(0);
        result.setErrorMsg("success");
        return result;
    }

    @Override
    @ServiceMethod("query_open_status")
    public FMCGSerialNumberQueryOpenStatus.Result queryFMCGSerialNumberModuleOpenStatus(FMCGSerialNumberQueryOpenStatus.Arg arg, ServiceContext serviceContext) {
        FMCGSerialNumberQueryOpenStatus.Result result = new FMCGSerialNumberQueryOpenStatus.Result();
        result.setValue(0);

        User superUser = User.systemUser(serviceContext.getTenantId());
        String isOpen = configService.findTenantConfig(superUser, OPEN_FMCG_SERIAL_NUMBER_CONFIG_KEY);
        if (StringUtils.isNotBlank(isOpen) && Boolean.TRUE.toString().equals(isOpen)) {
            result.setValue(1);
        }

        if (Objects.equals(1, result.getValue())) {
            IObjectDescribe describe = serviceFacade.findObject(serviceContext.getTenantId(), "FMCGSerialNumberObj");
            result.setIsShare(describe.isPublicObject() ? 1 : 0);
            result.setUpstreamTenantId(describe.getUpstreamTenantId());
        }

        result.setErrorCode(0);
        result.setErrorMsg("success");
        return result;
    }

    @Override
    @ServiceMethod("set_biz_conf")
    public FMCGSerialNumberSetBizConf.Result setBizConf(FMCGSerialNumberSetBizConf.Arg arg, ServiceContext serviceContext) {
        FMCGSerialNumberSetBizConf.Result result = new FMCGSerialNumberSetBizConf.Result();
        User superUser = User.systemUser(serviceContext.getTenantId());
        if (CollectionUtils.isNotEmpty(arg.getKvs())) {
            for (FMCGSerialNumberSetBizConf.KV kv : arg.getKvs()) {
                if (!BizConfKeyEnums.exist(kv.getKey()) || (!"true".equals(kv.getValue()) && !"false".equals(kv.getValue()))) {
                    result.setErrorCode(1);
                    result.setErrorMsg("key or val illegality");
                    return result;
                }
            }
            for (FMCGSerialNumberSetBizConf.KV kv : arg.getKvs()) {
                try {
                    configService.upsertTenantConfig(superUser, OPEN_BIZ_CONF_PRE + kv.getKey(), kv.getValue(), ConfigValueType.STRING);
                } catch (Exception e) {
                    log.error("setBizConf error : " + kv.getKey(), e);
                }
            }
        }
        result.setErrorCode(0);
        result.setErrorMsg("success");
        return result;
    }

    @ServiceMethod("clear_open_status_cache")
    public String clearOpenStatusCache(@RequestBody JSONObject arg) {
        String tenantId = arg.getString("tenantId");
        if (Strings.isNullOrEmpty(tenantId)) {
            return "tenantId is null";
        }
        try {
            log.info("before clear {}", JSON.toJSONString(fmcgsnOpenStatusCache.getNoException(tenantId)));
            fmcgsnOpenStatusCache.clear(tenantId);
            log.info("after clear {}", JSON.toJSONString(fmcgsnOpenStatusCache.getNoException(tenantId)));
        } catch (Exception e) {
            log.error("clear is error : ", e);
            return "failed";
        }
        return "success";
    }

    @Override
    @ServiceMethod("add_is_scan_code")
    public AddIsScanCode.Result addIsScanCode(AddIsScanCode.Arg arg, ServiceContext serviceContext) {
        AddIsScanCode.Result result = new AddIsScanCode.Result();
        if (!multipleUnitBusiness.enableMultipleUnit(Integer.parseInt(serviceContext.getTenantId()))) {
            result.setErrorCode(1);
            result.setErrorMsg("don`t enable multipleUnit");
            return result;
        }

        try {
            // add is_scan_code field describe
            describeManagerService.addFields(serviceContext.getTenantId(), MultiUnitRelatedApiNames.OBJECT_API_NAME, MultiUnitRelatedApiNames.IS_SCAN_CODE);

            // add is_scan_code field for list_layout of MultiUnitRelatedObj
            describeManagerService.addFieldsToListLayoutTableComponent(serviceContext.getTenantId(), MultiUnitRelatedApiNames.OBJECT_API_NAME, "MultiUnitRelatedObj_table_component", MultiUnitRelatedApiNames.IS_SCAN_CODE);

            result.setErrorCode(0);
            result.setErrorMsg("success");
        } catch (Exception e) {
            log.error("addIsScanCode error:", e);
            result.setErrorCode(1);
            result.setErrorMsg("handle error:" + e.getMessage());
        }
        return result;
    }

    @Override
    @ServiceMethod("add_create_payment_button")
    public AddCreatePaymentButton.Result addCreatePaymentButton(AddCreatePaymentButton.Arg arg, ServiceContext serviceContext) {
        AddCreatePaymentButton.Result result = new AddCreatePaymentButton.Result();
        try {
            buttonManagerService.addCustomerButton(serviceContext, SalesOrderApiNames.OBJECT_API_NAME, "offline_payment__c");
            result.setErrorCode(0);
            result.setErrorMsg("success");
        } catch (Exception e) {
            log.error("addCreatePaymentButton error:", e);
            result.setErrorCode(1);
            result.setErrorMsg("handle error:" + e.getMessage());
        }
        return result;
    }

    @ServiceMethod("config")
    public FmcgSerialNumberConfig.Result config(FmcgSerialNumberConfig.Arg arg, ServiceContext serviceContext) {
        FmcgSerialNumberConfig.Result result = new FmcgSerialNumberConfig.Result();
        result.setScanEncryptionType(ScanEncryptionTypeEnum.NONE.getType());
        String profile = System.getProperty("process.profile", System.getProperty("spring.profiles.active", ""));
        if ("mengniu-public-prod".equals(profile) || GrayRelease.isAllow("fmcg", "MENGNIU", serviceContext.getTenantId())) {
            result.setScanEncryptionType(ScanEncryptionTypeEnum.DOUBLE_MD5_UPPERCASE.getType());
        }
        JSONObject codeScanRule = FMCGConfigUtil.getFMCGSnCodeScanRule(Integer.parseInt(serviceContext.getTenantId()));
        result.setCodeScanRule(codeScanRule);
        return result;
    }
}
