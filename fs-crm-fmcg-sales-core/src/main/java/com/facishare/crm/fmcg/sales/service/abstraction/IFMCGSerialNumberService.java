package com.facishare.crm.fmcg.sales.service.abstraction;

import com.facishare.crm.fmcg.sales.model.sn.*;
import com.facishare.paas.appframework.core.model.ServiceContext;

/**
 * <AUTHOR> liu<PERSON>yu
 * @create : 2023-07-10 18:16
 **/
public interface IFMCGSerialNumberService {
    FMCGSerialNumberInItScene.Result initFMCGSerialNumberSceneV2(FMCGSerialNumberInItScene.Arg arg, ServiceContext serviceContext);

    FMCGSerialNumberInItScene.Result initFMCGSerialNumberScene(FMCGSerialNumberInItScene.Arg arg, ServiceContext serviceContext);

    FMCGSerialNumberQueryOpenStatus.Result queryFMCGSerialNumberModuleOpenStatus(FMCGSerialNumberQueryOpenStatus.Arg arg, ServiceContext serviceContext);

    FMCGSerialNumberSetBizConf.Result setBizConf(FMCGSerialNumberSetBizConf.Arg arg, ServiceContext serviceContext);

    AddIsScanCode.Result addIsScanCode(AddIsScanCode.Arg arg, ServiceContext serviceContext);

    AddCreatePaymentButton.Result addCreatePaymentButton(AddCreatePaymentButton.Arg arg, ServiceContext serviceContext);
}
