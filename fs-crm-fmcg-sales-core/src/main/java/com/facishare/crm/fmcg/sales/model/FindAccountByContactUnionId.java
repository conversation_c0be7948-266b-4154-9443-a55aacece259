package com.facishare.crm.fmcg.sales.model;

import com.alibaba.fastjson.JSONObject;
import com.fmcg.framework.http.contract.paas.describe.PaasDescribeGet;
import com.fxiaoke.api.model.BaseResult;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR> l<PERSON><PERSON><PERSON>
 * @create : 2024-06-05 11:46
 **/
public interface FindAccountByContactUnionId {
    @Data
    @ToString
    class Arg {
        private String unionId;
        private Integer tenantId;
    }

    @Data
    @ToString
    class Result extends BaseResult {
        private ResultData data;

        public static FindAccountByContactUnionId.Result success(FindAccountByContactUnionId.ResultData resultData) {
            FindAccountByContactUnionId.Result result = new FindAccountByContactUnionId.Result();
            result.setErrorCode(0);
            result.setErrorMsg("success");
            result.setData(resultData);
            return result;
        }

        public static FindAccountByContactUnionId.Result error(Integer errorCode, String errorMsg) {
            FindAccountByContactUnionId.Result result = new FindAccountByContactUnionId.Result();
            result.setErrorCode(errorCode);
            result.setErrorMsg(errorMsg);
            return result;
        }
    }

    @Data
    @ToString
    class ResultData {
        private JSONObject accountData;
        private JSONObject describe;
    }
}
