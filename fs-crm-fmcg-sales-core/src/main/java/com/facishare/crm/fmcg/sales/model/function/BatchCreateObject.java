package com.facishare.crm.fmcg.sales.model.function;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

public interface BatchCreateObject {
    @EqualsAndHashCode(callSuper = true)
    @Data
    class Arg extends FunctionBase.Arg {
        private List<JSONObject> dataList;
    }

    class Result extends FunctionBase.Result {

    }
}
