package com.facishare.crm.fmcg.sales.business;

import com.fmcg.framework.http.contract.sfa.MultipleUnitStatus;
import com.fmcg.framework.http.proxyinterface.sfa.MultipleUnitProxy;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Component("multipleUnitBusiness")
@Slf4j
public class MultipleUnitBusiness implements InitializingBean {
    private LoadingCache<Integer, Boolean> multipleUnitActiveCache;
    @Resource
    private MultipleUnitProxy multipleUnitProxy;

    public Boolean enableMultipleUnit(int tenantId) {
        try {
            return multipleUnitActiveCache.get(tenantId);
        } catch (ExecutionException e) {
            log.error("get multiple unit status error", e);
            return false;
        }
    }

    public Boolean enableMultipleUnitAndThrow(int tenantId) throws ExecutionException {
        return multipleUnitActiveCache.get(tenantId);
    }

    private Boolean enableMultipleUnitOriginal(int tenantId) {
        try {
            MultipleUnitStatus.Result result = multipleUnitProxy.getMultipleUnitStatus(tenantId, -10000, new MultipleUnitStatus.Arg());
            if (!result.isSuccess()) {
                return false;
            }
            return "1".equals(result.getValue().getString("openStatus"));
        } catch (Exception ex) {
            return false;
        }
    }

    @Override
    public void afterPropertiesSet() {
        multipleUnitActiveCache = CacheBuilder.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(3, TimeUnit.HOURS)
                .build(new CacheLoader<Integer, Boolean>() {
                    @Override
                    public Boolean load(Integer key) {
                        return enableMultipleUnitOriginal(key);
                    }
                });
    }
}
