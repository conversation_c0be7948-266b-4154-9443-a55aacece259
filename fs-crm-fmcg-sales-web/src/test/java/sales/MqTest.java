package sales;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.sales.mq.action.EnterpriseRelationObjAction;
import com.facishare.crm.fmcg.sales.mq.consumer.model.MessageBodyObj;
import com.facishare.organization.paas.model.PaaSResult;
import com.facishare.organization.paas.model.permission.BatchAddUserRoleDto;
import com.facishare.organization.paas.service.PaaSPermissionService;
import com.facishare.organization.paas.util.PaasArgumentUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> liu<PERSON>yu
 * @create : 2023-11-01 11:13
 **/
public class MqTest extends BaseTest {
    @Resource
    private EnterpriseRelationObjAction enterpriseRelationObjAction;
    @Resource
    private ServiceFacade serviceFacade;

    @Test
    public void testEnterpriseRelation() {
        JSONObject object = new JSONObject();
        object.put("objectId", "*********");
        object.put("triggerType", "u");
        JSONObject after = new JSONObject();
        after.put("enterprise_account", "89816");
        after.put("crm_open_status", "1");
        object.put("afterTriggerData", after);

        MessageBodyObj messageBodyObj = new MessageBodyObj("89150", "1000", "", object);
        enterpriseRelationObjAction.action(messageBodyObj);
    }

    @Resource
    private PaaSPermissionService paaSPermissionService;

    @Test
    public void tt() {
        addRole(89831, "facishare-system", Lists.newArrayList("1000"), Lists.newArrayList("89804-*************"));
        addRole(89831, "CRM", Lists.newArrayList("1000"), Lists.newArrayList("00000000000000000000000000000026", "6540ed2e6abbf40001063d6e"));
    }

    private void addRole(Integer tenantId, String appId, List<String> users, List<String> roles) {
        BatchAddUserRoleDto.Argument argument = PaasArgumentUtil.buildPaaSPermissionArgument(BatchAddUserRoleDto.Argument.class, tenantId, -10000, appId);
        argument.setUsers(users);
        argument.setRoles(roles);
        PaaSResult<BatchAddUserRoleDto.Result> result = paaSPermissionService.batchAddUserRole(argument);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void t() {
        IObjectData userData = new ObjectData();
        userData.setOwner(Lists.newArrayList("-10000"));
        userData.setDescribeApiName("PersonnelObj");
        userData.setTenantId("89833");
        userData.setRecordType("default__c");
        userData.setName("纷享员工");
        userData.set("phone", "18600698275");
        userData.set("sex", "M");
        userData.set("status", "0");
        userData.set("user_id", "1001");
        userData.set("password", "aa123456");
        serviceFacade.saveObjectData(User.systemUser("89833"), userData);
    }

    @Test
    public void t1() {
        IObjectData objectData = new ObjectData();
        objectData.setName("本企业供应商");
        objectData.setOwner(Lists.newArrayList("-10000"));
        objectData.setTenantId("89854");
        objectData.setRecordType("default__c");
        objectData.setDescribeApiName("SupplierObj");
        objectData.set("isOurEnterprise__c", "YES");
        objectData.set("is_enable", "1");
        IObjectData result = serviceFacade.saveObjectData(User.systemUser("89854"), objectData);
        System.out.println(JSON.toJSONString(result));
    }
}
