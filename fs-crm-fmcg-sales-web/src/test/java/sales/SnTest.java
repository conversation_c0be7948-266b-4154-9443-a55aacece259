package sales;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.sales.apiname.FMCGSerialNumberStatusApiNames;
import com.facishare.crm.fmcg.sales.apiname.InspectionRecordObjApiNames;
import com.facishare.crm.fmcg.sales.apiname.ProductApiNames;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fmcg.framework.http.PaasDescribeProxy;
import com.fmcg.framework.http.contract.paas.describe.PaasDescribeLayout;
import com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> liuhaoyu
 * @create : 2023-09-05 17:58
 **/
public class SnTest extends BaseTest {
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private PaasDescribeProxy paasDescribeProxy;

    @Test
    public void t() {
        IObjectDescribe describe = serviceFacade.findObject("89386", "FMCGSerialNumberObj");
        Object visibleScope = describe.get("visible_scope");
        Object upstreamTenantId = describe.get("upstream_tenant_id_a");
        System.out.println(Objects.equals(visibleScope, "public"));
        System.out.println(Objects.equals(upstreamTenantId, "82958"));
        System.out.println(JSON.toJSONString(describe));
    }

    @Test
    public void testAddStore() {
        RequestContext requestContext = RequestContext.builder()
                .tenantId("89150")
                .ea("89150")
                .user(User.systemUser("-10000"))
                .build();
        ServiceContext serviceContext = new ServiceContext(requestContext, "", "");
        JSONObject object = new JSONObject();
        object.put("limit", 100);
        object.put("offset", 0);
        object.put("tenant_id", "78582");
        object.put("query_all", "false");
        object.put("max_size", "1");
        object.put("clientId", "bf68d8a3-4763-40aa-a8d8-a34763e0aace");
        object.put("sk", "D7ABC695-6EB3-4902-ABC6-956EB3790228");
//        String clientId = "bf68d8a3-4763-40aa-a8d8-a34763e0aace";
//        String sk = "D7ABC695-6EB3-4902-ABC6-956EB3790228";

//        batchAddStoreService.batchAddStore(object, serviceContext);
    }

    @Test
    public void batchAdd() {
        List<IObjectData> dataList = Lists.newArrayList();
        IObjectData objectData = new ObjectData();
        objectData.setOwner(Lists.newArrayList("-10000"));
        objectData.setDescribeApiName(ProductApiNames.OBJECT_API_NAME);
        objectData.setTenantId("89386");
        objectData.setRecordType("default__c");

        objectData.set("name", "产品名称0004");
        objectData.set("name__lang", new JSONObject() {{
            put("en", "Product Name 0004");
            put("zh-CN", "产品名称0004");
            put("zh-TW", "產品名稱0004");
        }});

        objectData.set("remark", "备注0004");
        objectData.set("remark__lang", new JSONObject() {{
            put("en", "Note0004");
            put("zh-CN", "备注0004");
            put("zh-TW", "備註0004");
        }});

        objectData.set("product_spec", "规格属性0004");
        objectData.set("product_spec__lang", new JSONObject() {{
            put("en", "Specification Attribute 0004");
            put("zh-CN", "规格属性0004");
            put("zh-TW", "規格屬性0004");
        }});

        objectData.set("product_code", "产品编码0004");
        objectData.set("product_code__lang", new JSONObject() {{
            put("en", "Product Code 0004");
            put("zh-CN", "产品编码0004");
            put("zh-TW", "產品編碼0004");
        }});

        objectData.set("mnemonic_code", "助记码0004");
        objectData.set("mnemonic_code__lang", new JSONObject() {{
            put("en", "Mnemonic Code 0004");
            put("zh-CN", "助记码0004");
            put("zh-TW", "助記碼0004");
        }});

        objectData.set("product_category_id", "64e87fd315c3530001fdf928");
        objectData.set("price", "100");
        objectData.set("product_status", "1");
        objectData.set("batch_sn", "1");
        objectData.set("is_multiple_unit", false);

        dataList.add(objectData);
        List<IObjectData> result = serviceFacade.bulkSaveObjectData(dataList, User.systemUser("89386"));
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void batchAddI18N() {
        String tenantId = "89386";

        List<IObjectData> dataList = Lists.newArrayList();
        IObjectData objectData = new ObjectData();
        objectData.setOwner(Lists.newArrayList("-10000"));
        objectData.setDescribeApiName(FMCGSerialNumberStatusApiNames.OBJECT_API_NAME);
        objectData.setTenantId(tenantId);
        objectData.setRecordType("default__c");
        objectData.setIsPublic(true);

        objectData.set("node_name_id", "销售出库");
        objectData.set("node_name_id__lang", new JSONObject() {{
            put("en", "Sales Outbound");
            put("zh-CN", "销售出库");
            put("zh-TW", "銷售出庫");
        }});

        objectData.set("business_object_name", "销售订单");
        objectData.set("business_object_name__lang", new JSONObject() {{
            put("en", "Sales Order");
            put("zh-CN", "销售订单");
            put("zh-TW", "銷售訂單");
        }});

        dataList.add(objectData);
        List<IObjectData> result = serviceFacade.bulkSaveObjectData(dataList, User.systemUser(tenantId));
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void findObject() {
        IFilter snIdFilter = new Filter();
        snIdFilter.setFieldValues(Lists.newArrayList("6683b3518f2119000123a2fb"));
        snIdFilter.setOperator(Operator.EQ);
        snIdFilter.setFieldName("_id");

        SearchTemplateQuery queryTemplate = new SearchTemplateQuery();
        queryTemplate.setLimit(1);
        queryTemplate.setFilters(Lists.newArrayList(snIdFilter));

        List<IObjectData> result = serviceFacade.findBySearchTemplateQueryWithFields(
                ActionContextExt.of(User.systemUser("82958")).getContext(),
                "FMCGSerialNumberObj",
                queryTemplate,
                null).getData();

        System.out.println(JSON.toJSONString(result.get(0)));
        System.out.println(JSON.toJSONString(ObjectDataDocument.of(result.get(0))));
        System.out.println(JSON.toJSONString(ObjectDataDocument.of(result.get(0)).toObjectData()));
    }

    @Test
    public void findObjectTest() {
        IObjectData objectData = serviceFacade.findObjectDataIgnoreAll(User.systemUser("82958"), "66a70cb3d12c4e00010c3d35", InspectionRecordObjApiNames.OBJECT_API_NAME);
        System.out.println(JSON.toJSONString(objectData));
        IObjectData objectData1 = serviceFacade.findObjectData(User.systemUser("82958"), "66a70cb3d12c4e00010c3d35", InspectionRecordObjApiNames.OBJECT_API_NAME);
        System.out.println(JSON.toJSONString(objectData1));
    }
    @Test
    public void findDescribeLayoutV1() {
        PaasDescribeLayout.Arg layoutArg = new PaasDescribeLayout.Arg();
        layoutArg.setIncludeDetailDescribe(false);
        layoutArg.setIncludeLayout(false);
        layoutArg.setApiName("FMCGSerialNumberStatusObj");
        layoutArg.setLayoutType("add");
        layoutArg.setRecordTypeApiName("default__c");
        PaasDescribeLayout.Result describeLayout = paasDescribeProxy.findDescribeLayoutV1(89386, -10000, "FMCGSerialNumberStatusObj", layoutArg);
        System.out.println(JSON.toJSONString(describeLayout));
    }
}
