package sales;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.sales.apiname.MultiUnitRelatedApiNames;
import com.facishare.paas.metadata.api.service.ILayoutService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.TableColumn;
import com.facishare.paas.metadata.impl.ui.layout.component.TableComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.ui.layout.ITableColumn;
import com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023/09/03 11:43
 */
public class DescribeTest extends BaseTest {

    @Resource
    private ILayoutService layoutService;

    @Test
    public void listLayoutTest() throws MetadataServiceException {
        List<Layout> layoutList = layoutService.findByTypes("85781", Lists.newArrayList(ILayout.LIST_LAYOUT_TYPE), MultiUnitRelatedApiNames.OBJECT_API_NAME);
        System.out.println("------list_layout----->" + JSONObject.toJSONString(layoutList));

        String isScanCode = "{\"is_required\": false,\"api_name\": \"is_scan_code\",\"label\": \"是否开启扫码\",\"type\": \"true_or_false\"}";
        List<ITableColumn> tableColumnList = Lists.newArrayList();
        tableColumnList.add(getTableColumn(isScanCode));

        ITableColumn tableColumn2 = getTableColumn(isScanCode);
        tableColumn2.set("api_name", null);
        tableColumnList.add(tableColumn2);
        for (Layout layout : layoutList) {
            addFieldsToListLayoutTableComponent(layout, tableColumnList);
            System.out.println("------list-layout----->" + JSONObject.toJSONString(layout));
            // layoutService.replace(layout);
        }
    }

    private ITableColumn getTableColumn(String columnDescJson) {
        JSONObject columnInfo = JSON.parseObject(columnDescJson);
        ITableColumn tableColumn = new TableColumn(columnInfo);
        tableColumn.setLabelName(columnInfo.getString("label"));
        tableColumn.setRenderType(columnInfo.getString("type"));
        columnInfo.remove("type");
        return tableColumn;
    }

    private void addFieldsToListLayoutTableComponent(ILayout layout, List<ITableColumn> tableColumnList) throws MetadataServiceException {
        layout.getComponents().forEach(component -> {
            if (Objects.equals(component.getName(), "MultiUnitRelatedObj_table_component")) {
                TableComponent tableComponent = (TableComponent) component;
                Set<String> columnApiNameSet = tableComponent.getIncludeFields().stream().map(tc -> (String) tc.get("api_name")).collect(Collectors.toSet());

                List<ITableColumn> columnToRemove = Lists.newArrayList();
                for (ITableColumn tableColumn : tableColumnList) {
                    Object columnApiName = tableColumn.get("api_name");
                    if (Objects.isNull(columnApiName) || columnApiNameSet.contains(columnApiName.toString())) {
                        columnToRemove.add(tableColumn);
                    }
                }
                tableColumnList.removeAll(columnToRemove);
                tableColumnList.forEach(tableComponent::addField);
            }
        });
    }
}