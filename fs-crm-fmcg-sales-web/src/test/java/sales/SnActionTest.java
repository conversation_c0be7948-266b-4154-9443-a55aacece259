package sales;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.sales.dao.mongo.FMCGSerialNumberActionDao;
import com.facishare.crm.fmcg.sales.model.sn.DeleteMongoAction;
import com.facishare.crm.fmcg.sales.model.sn.QuerySnAction;
import com.facishare.crm.fmcg.sales.mq.consumer.model.MessageBodyObj;
import com.facishare.crm.fmcg.sales.mq.service.FMCGSnActionService;
import com.facishare.crm.fmcg.sales.service.FMCGSerialNumberActionService;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR> liu<PERSON>yu
 * @create : 2023-09-19 11:14
 **/
public class SnActionTest extends BaseTest {
    @Resource
    private FMCGSerialNumberActionService fmcgSerialNumberActionService;
    @Resource
    private FMCGSerialNumberActionDao fmcgSerialNumberActionDao;
    @Resource
    private FMCGSnActionService fmcgSnActionService;

    @Test
    public void findActions() {
        RequestContext requestContext = RequestContext.builder()
                .tenantId("89150")
                .ea("89150")
                .user(User.systemUser("-10000"))
                .build();
        ServiceContext serviceContext = new ServiceContext(requestContext, "", "");
        QuerySnAction.Result result = fmcgSerialNumberActionService.querySnAction(new QuerySnAction.Arg(), serviceContext);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void record() {
        //门店库存盘点
//        JSONObject message = new JSONObject();
//        message.put("objectId", "6736c45dad93b500078d1b20");
//        message.put("entityId", "StoreStockCheck__c");
//        message.put("triggerType", "i");
//        fmcgSnActionService.recordSnStatusByAction(new MessageBodyObj("89386", "1000", "", message));

        //签收入库
//        JSONObject message = new JSONObject();
//        message.put("objectId", "650e8e9846938d0001299067");
//        message.put("entityId", "GoodsReceivedNoteObj");
//        message.put("triggerType", "i");
//        fmcgSnActionService.recordSnStatusByAction(new MessageBodyObj("89273", "1000", message));

//        盘盈入库
        JSONObject message = new JSONObject();
        message.put("objectId", "66cc0360adc402000199ece0");
        message.put("entityId", "GoodsReceivedNoteProductObj");
        message.put("triggerType", "u");
        message.put("afterTriggerData", new JSONObject() {{
            put("unique_product_code_combination", Lists.newArrayList("65fc2173b7c1ec00016d335a"));
        }});
        message.put("beforeTriggerData", new JSONObject() {{
            put("unique_product_code_combination", null);
        }});
        fmcgSnActionService.recordSnStatusByAction(new MessageBodyObj("89386", "1000", "", message));

        //门店签收
//        JSONObject message = new JSONObject();
//        message.put("objectId", "66d996e8ba1eb40001ee8d4b");
//        message.put("entityId", "DeliveryNoteObj");
//        message.put("triggerType", "u");
//        message.put("afterTriggerData", new JSONObject() {{
//            put("status", "received");
//        }});
//        message.put("beforeTriggerData", new JSONObject() {{
//            put("status", null);
//        }});
//        fmcgSnActionService.recordSnStatusByAction(new MessageBodyObj("89386", "1000", "", message));
//
//        //消费者扫码领红包
//        JSONObject message = new JSONObject();
//        message.put("objectId", "650a6fee46938d0001215997");
//        message.put("entityId", "DeliveryNoteObj");
//        message.put("triggerType", "i");
//        fmcgSnActionService.recordSnStatusByAction(new MessageBodyObj("89273", "1000", message));

//        String json = "[{\"AN\":\"签收入库\",\"AS\":1,\"AST\":2,\"AT\":0,\"C\":[{\"FN\":\"record_type\",\"FT\":\"string\",\"FV\":\"default__c\",\"O\":\"EQ\",\"RO\":0}],\"CNT\":[0,1],\"CP\":\"(0)\",\"CT\":[0,2],\"PSF\":\"created_by\",\"PSO\":\"PersonnelObj\",\"SSL\":[{\"DOAN\":\"GoodsReceivedNoteProductObj\",\"MOF\":\"goods_received_note_id\",\"PF\":\"product_id\",\"SF\":\"unique_product_code_combination\",\"ST\":\"array\"}],\"TA\":\"i\",\"TI\":\"781417\",\"TO\":\"GoodsReceivedNoteObj\",\"UI\":\"SIGN_CODE_SIGN\",\"US\":0,\"WF\":\"warehouse_id\"},{\"AF\":\"account_id\",\"AN\":\"销售出库\",\"AS\":1,\"AST\":1,\"AT\":0,\"CNT\":[0,1],\"CP\":\"(0)\",\"CT\":[0,2],\"PSF\":\"created_by\",\"PSO\":\"PersonnelObj\",\"SSL\":[{\"DOAN\":\"DeliveryNoteProductObj\",\"MOF\":\"delivery_note_id\",\"PF\":\"product_id\",\"SF\":\"unique_product_code_combination\",\"ST\":\"array\"}],\"TA\":\"i\",\"TI\":\"781417\",\"TO\":\"DeliveryNoteObj\",\"UI\":\"SALES_OUT_OF_WAREHOUSE\",\"US\":1,\"WF\":\"delivery_warehouse_id\"},{\"AN\":\"退货入库\",\"AS\":1,\"AST\":0,\"AT\":0,\"C\":[{\"FN\":\"record_type\",\"FT\":\"string\",\"FV\":\"return_back__c\",\"O\":\"EQ\",\"RO\":0}],\"CNT\":[0,1],\"CP\":\"(0)\",\"CT\":[0,2],\"PSF\":\"created_by\",\"PSO\":\"PersonnelObj\",\"SSL\":[{\"DOAN\":\"GoodsReceivedNoteProductObj\",\"MOF\":\"goods_received_note_id\",\"PF\":\"product_id\",\"SF\":\"unique_product_code_combination\",\"ST\":\"array\"}],\"TA\":\"i\",\"TI\":\"781417\",\"TO\":\"GoodsReceivedNoteObj\",\"UI\":\"RETURN_BACK\",\"US\":2,\"WF\":\"warehouse_id\"},{\"AN\":\"换货入库\",\"AS\":1,\"AST\":0,\"AT\":0,\"C\":[{\"FN\":\"record_type\",\"FT\":\"string\",\"FV\":\"exchange_return_in__c\",\"O\":\"EQ\",\"RO\":0}],\"CNT\":[0,1],\"CP\":\"(0)\",\"CT\":[0,2],\"PSF\":\"created_by\",\"PSO\":\"PersonnelObj\",\"SSL\":[{\"DOAN\":\"GoodsReceivedNoteProductObj\",\"MOF\":\"goods_received_note_id\",\"PF\":\"product_id\",\"SF\":\"unique_product_code_combination\",\"ST\":\"array\"}],\"TA\":\"i\",\"TI\":\"781417\",\"TO\":\"GoodsReceivedNoteObj\",\"UI\":\"EXCHANGE_RETURN_IN\",\"US\":3,\"WF\":\"warehouse_id\"},{\"AN\":\"换货出库\",\"AS\":1,\"AST\":1,\"AT\":0,\"C\":[{\"FN\":\"record_type\",\"FT\":\"string\",\"FV\":\"exchange_return_out__c\",\"O\":\"EQ\",\"RO\":0}],\"CNT\":[0,1],\"CP\":\"(0)\",\"CT\":[0,2],\"PSF\":\"created_by\",\"PSO\":\"PersonnelObj\",\"SSL\":[{\"DOAN\":\"ReturnedGoodsInvoiceProductObj\",\"MOF\":\"returned_goods_inv_id\",\"PF\":\"product_id\",\"SF\":\"unique_product_code_combination\",\"ST\":\"array\"}],\"TA\":\"i\",\"TI\":\"781417\",\"TO\":\"ReturnedGoodsInvoiceObj\",\"UI\":\"EXCHANGE_RETURN_OUT\",\"US\":4,\"WF\":\"return_warehouse_id\"},{\"AF\":\"account_id\",\"AN\":\"门店签收\",\"AS\":1,\"AST\":2,\"AT\":0,\"C\":[{\"FN\":\"status\",\"FT\":\"string\",\"FV\":\"received\",\"O\":\"EQ\",\"OY\":\"after\",\"RO\":0}],\"CNT\":[2],\"CP\":\"(0)\",\"CT\":[0,2],\"PSF\":\"consignee\",\"PSO\":\"ContactObj\",\"SSL\":[{\"DOAN\":\"DeliveryNoteProductObj\",\"MOF\":\"delivery_note_id\",\"PF\":\"product_id\",\"SF\":\"unique_product_code_combination\",\"ST\":\"array\"}],\"TA\":\"u\",\"TI\":\"781417\",\"TO\":\"DeliveryNoteObj\",\"UI\":\"STORE_SIGN\",\"US\":6},{\"AN\":\"消费者领红包\",\"AS\":1,\"AST\":3,\"AT\":2,\"C\":[{\"FN\":\"payment_status__c\",\"FT\":\"string\",\"FV\":\"2\",\"O\":\"EQ\",\"OY\":\"after\",\"RO\":0},{\"FN\":\"role__c\",\"FT\":\"string\",\"FV\":\"1\",\"O\":\"EQ\",\"RO\":1}],\"CNT\":[3],\"CP\":\"(0 and 1)\",\"CT\":[1],\"PSF\":\"created_by\",\"PSO\":\"PersonnelObj\",\"SSL\":[{\"DOAN\":\"red_packet_record_detail__c\",\"MOF\":\"red_packet_record_id__c\",\"PF\":\"product_id__c\",\"SF\":\"serial_number_id__c\",\"ST\":\"string\"}],\"TA\":\"u\",\"TI\":\"781417\",\"TO\":\"red_packet_record__c\",\"UI\":\"CONSUMER_RED_PACKET\",\"US\":7},{\"AN\":\"其他入库\",\"AS\":1,\"AST\":0,\"AT\":1,\"C\":[{\"FN\":\"record_type\",\"FT\":\"string\",\"FV\":\"purchase_order__c\",\"O\":\"EQ\",\"RO\":0}],\"CNT\":[0,1],\"CP\":\"(0)\",\"CT\":[0,2],\"PSF\":\"created_by\",\"PSO\":\"PersonnelObj\",\"SSL\":[{\"DOAN\":\"GoodsReceivedNoteProductObj\",\"MOF\":\"goods_received_note_id\",\"PF\":\"product_id\",\"SF\":\"unique_product_code_combination\",\"ST\":\"array\"}],\"TA\":\"i\",\"TI\":\"781417\",\"TO\":\"GoodsReceivedNoteObj\",\"UI\":\"OTHER_RETURN_IN\",\"US\":5,\"WF\":\"warehouse_id\"},{\"AN\":\"消费者扫码\",\"AS\":1,\"AST\":3,\"AT\":2,\"C\":[{\"FN\":\"record_type\",\"FT\":\"string\",\"FV\":\"default__c\",\"O\":\"EQ\",\"RO\":0}],\"CNT\":[3],\"CP\":\"(0)\",\"CT\":[1],\"PSF\":\"created_by\",\"PSO\":\"PersonnelObj\",\"SSL\":[{\"PF\":\"product_id\",\"SF\":\"serial_number_id\",\"ST\":\"string\"}],\"TA\":\"i\",\"TI\":\"781417\",\"TO\":\"StorePromotionRecordObj\",\"UI\":\"CONSUMER_SCAN_CODE\",\"US\":8}]";
//
//        InsertMongoAction.Arg arg = new InsertMongoAction.Arg();
//        RequestContext requestContext = RequestContext.builder()
//                .tenantId("89150")
//                .ea("89150")
//                .user(User.systemUser("-10000"))
//                .build();
//        ServiceContext serviceContext = new ServiceContext(requestContext, "", "");
//        fmcgSerialNumberActionService.insertMongoAction(arg, serviceContext);

    }

    @Test
    public void deleteAction() {
        DeleteMongoAction.Arg arg = new DeleteMongoAction.Arg();
        arg.setIds(Lists.newArrayList("6537e50550b16876e793bed0",
                "6537e50550b16876e793bed1",
                "6537e50550b16876e793bed2",
                "6537e50550b16876e793bed3",
                "6537e50550b16876e793bed4",
                "6537e50550b16876e793bed5",
                "6537e50550b16876e793bed6",
                "6537e50550b16876e793bed7",
                "6537e50550b16876e793bed8"));
        RequestContext requestContext = RequestContext.builder()
                .tenantId("82958")
                .ea("82958")
                .user(User.systemUser("-10000"))
                .build();
        ServiceContext serviceContext = new ServiceContext(requestContext, "", "");
        DeleteMongoAction.Result result = fmcgSerialNumberActionService.deleteMongoAction(arg, serviceContext);
        System.out.println(JSON.toJSONString(result));
    }
}
