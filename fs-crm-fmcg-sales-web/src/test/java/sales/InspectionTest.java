package sales;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.sales.abs.MengNiuHandleFmcgSerialNumberAction;
import com.facishare.crm.fmcg.sales.apiname.FMCGSerialNumberApiNames;
import com.facishare.crm.fmcg.sales.model.inspection.InspectionVerify;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR> liu<PERSON>yu
 * @create : 2025-04-16 19:42
 **/
public class InspectionTest extends BaseTest {
    @Resource
    private ServiceFacade serviceFacade;

    @Test
    public void addInspectionPhoto() {
        User user = User.systemUser("82958");
        IObjectData objectData = new ObjectData();
        objectData.setDescribeApiName("InspectionPhoto__c");
        objectData.setTenantId("82958");
        objectData.setOwner(Lists.newArrayList("1000"));

        // 设置第一个地址相关字段
        objectData.set("first_area_city__c", "283");
        objectData.set("first_area_country__c", "248");
        objectData.set("first_area_detail_address__c", "北京市海淀区10号线知春里(地铁站)");
        objectData.set("first_area_district__c", "635");
        objectData.set("first_area_location__c", "116.328858#%$39.976337#%$北京市海淀区10号线知春里(地铁站)");
        objectData.set("first_area_province__c", "249");
        objectData.set("first_area_town__c", "13929");

        // 设置当前地址相关字段
        objectData.set("current_area_city__c", "283");
        objectData.set("current_area_country__c", "248");
        objectData.set("current_area_detail_address__c", "北京市昌平区5号线天通苑南(地铁站)");
        objectData.set("current_area_district__c", "640");
        objectData.set("current_area_location__c", "116.412684#%$40.066612#%$北京市昌平区5号线天通苑南(地铁站)");
        objectData.set("current_area_province__c", "249");
        objectData.set("current_area_town__c", "14036");

        IObjectData result = serviceFacade.saveObjectData(user, objectData);
        System.out.println(JSON.toJSONString(result));

        ActionContext context = new ActionContext(RequestContext.builder()
                .tenantId("82958")
                .user(User.builder().userId("1000").tenantId("82958").build())
                .build(), "InspectionPhoto__c", "Add");

        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
        arg.setObjectData(ObjectDataDocument.of(objectData));

        BaseObjectSaveAction.Result result1 = serviceFacade.triggerRemoteAction(context, arg, BaseObjectSaveAction.Result.class);
        System.out.println(JSON.toJSONString(result1));
    }

    @Test
    public void a() {
        try {
            IObjectData objectData = serviceFacade.findObjectData(User.systemUser("82958"), "67f4e592ce335b000128f53f", "InspectionRecordObj");
            System.out.println(JSON.toJSONString(objectData));
            IObjectData objectData1 = serviceFacade.findObjectData(User.systemUser("89386"), "67f4e592ce335b000128f53f", "InspectionRecordObj");
            System.out.println(JSON.toJSONString(objectData1));
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            IObjectData objectData = serviceFacade.findObjectData(User.systemUser("82958"), "67f4e5319a5f6d00016f6ed6", "InspectionRecordObj");
            System.out.println(JSON.toJSONString(objectData));
            IObjectData objectData1 = serviceFacade.findObjectData(User.systemUser("89386"), "67f4e5319a5f6d00016f6ed6", "InspectionRecordObj");
            System.out.println(JSON.toJSONString(objectData1));
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            IObjectData objectData = serviceFacade.findObjectData(User.systemUser("82958"), "67f4c33e08412ff2b5561872", "InspectionRecordObj");
            System.out.println(JSON.toJSONString(objectData));
            IObjectData objectData1 = serviceFacade.findObjectData(User.systemUser("89386"), "67f4c33e08412ff2b5561872", "InspectionRecordObj");
            System.out.println(JSON.toJSONString(objectData1));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Resource
    private MengNiuHandleFmcgSerialNumberAction mengNiuHandleFmcgSerialNumberAction;

    @Test
    public void mengNiuHandleFmcgSerialNumberActionFillDataTest() {
        IObjectData objectData = new ObjectData();
        objectData.set(FMCGSerialNumberApiNames.DELIVERY_NUMBER, "833899038");
        //objectData.set(FMCGSerialNumberApiNames.DELIVERY_NUMBER, "8027926529");
        //objectData.set(FMCGSerialNumberApiNames.DELIVERY_NUMBER, "833885990");

        InspectionVerify.Context context = new InspectionVerify.Context();
        context.setObjectData(objectData);
        context.setUpstreamTenantId("82958");

        mengNiuHandleFmcgSerialNumberAction.fillData(context);
        System.out.println(JSON.toJSONString(context));
    }
}
