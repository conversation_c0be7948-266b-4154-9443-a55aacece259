package sales;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.sales.dao.mongo.SalesStatementsDao;
import com.facishare.crm.fmcg.sales.enums.LayoutType;
import com.facishare.crm.fmcg.sales.model.statement.SalesStatementsDetail;
import com.facishare.crm.fmcg.sales.service.DescribeManagerService;
import com.facishare.crm.fmcg.sales.service.SalesStatementsService;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.metadata.ui.layout.ILayout;
import org.junit.Test;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @Description:
 * @Author: TianHongzhen
 * @Date: 2022/10/18 15:46
 */
public class SalesStatementsTest extends BaseTest {

    @Resource
    private SalesStatementsService salesStatementsService;

    @Resource
    private DescribeManagerService describeManagerService;

    @Resource
    private SalesStatementsDao salesStatementsDao;

    @Resource
    private ConfigService configService;

    @Resource
    private ServiceFacade serviceFacade;


    @Test
    public void detail() {
        String tenantId = "85023";
        RequestContext requestContext = RequestContext.builder()
                .tenantId(tenantId)
                .ea(tenantId)
                .user(User.systemUser("-10000"))
                .build();
        ServiceContext context = new ServiceContext(requestContext, "", "");

        SalesStatementsDetail.Arg arg = new SalesStatementsDetail.Arg();
        arg.setDataId("6385b735295bca0001f650ef");
        SalesStatementsDetail.Result detail = salesStatementsService.detail(arg, context);
        System.out.println(JSONObject.toJSON(detail));

    }

    @Test
    public void cache() {
        //存储快照
        try {
            SalesStatementsDetail.Result result = new SalesStatementsDetail.Result(null);
            List<SalesStatementsDetail.Shop> shopList = new ArrayList<>();
            result.setDataDetailList(shopList);
            result.setErrorCode(0);
            result.setErrorMsg("success");
            System.out.println();
            //            salesStatementsDao.saveSalesStatementsDetailEntity("1", String.valueOf(78612), JSON.toJSONString(result));


        } catch (Exception e) {
            e.printStackTrace();
            //log.error("对账单快照存储失败 message={}, _id={},salesman={},dateInterval={}, tenantId={}, allSalesOrder={}", e.getMessage(), arg.getId(), arg.getSalesman(), dateInterval, tenantId, allSalesOrder);
        }
    }


    @Test
    public void initSalesStatementModule() {
        String tenantId = "78612";
        RequestContext.RequestContextBuilder requestContextBuilder = RequestContext.builder();
        requestContextBuilder.tenantId(tenantId);
        requestContextBuilder.user(new User(tenantId, "-10000"));
        requestContextBuilder.contentType(RequestContext.ContentType.FULL_JSON);
        requestContextBuilder.postId(System.currentTimeMillis() + "");
        requestContextBuilder.requestSource(RequestContext.RequestSource.CEP);
        RequestContext requestContext = requestContextBuilder.build();
        RequestContextManager.setContext(requestContext);
        salesStatementsService.initSalesStatementsModule(tenantId);
    }

    @Test
    public void a() {
        String tenantId = "78612";
        User superUser = User.systemUser(tenantId);
        try {
            describeManagerService.createLayout(superUser, "SalesStatementsObj", LayoutType.LIST_LAYOUT);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

}