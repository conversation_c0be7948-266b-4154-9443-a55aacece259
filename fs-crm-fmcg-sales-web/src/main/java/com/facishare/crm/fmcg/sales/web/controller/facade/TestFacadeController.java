package com.facishare.crm.fmcg.sales.web.controller.facade;


import com.facishare.crm.fmcg.sales.web.context.ApiContext;
import com.facishare.crm.fmcg.sales.web.context.ApiContextManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/FMCGSales/API/testFacade")
@Slf4j
public class TestFacadeController {

    @GetMapping("/test")
    public String test() {
        ApiContext context = ApiContextManager.getContext();
        log.info("context : {}", context);
        return "test";
    }

    @PostMapping("/test1")
    public String test1() {
        ApiContext context = ApiContextManager.getContext();
        log.info("context : {}", context);
        return "test1";
    }
}
