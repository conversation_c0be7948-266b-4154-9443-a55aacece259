package com.facishare.crm.fmcg.sales.web.controller.facade;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.sales.apiname.AccountObjApiNames;
import com.facishare.crm.fmcg.sales.apiname.ContactObjApiNames;
import com.facishare.crm.fmcg.sales.model.FindAccountByContactUnionId;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.PaasDescribeProxy;
import com.fmcg.framework.http.contract.paas.data.PaasDataQueryWithFields;
import com.fmcg.framework.http.contract.paas.data.PaasDataWebDetail;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR> liuhaoyu
 * @create : 2024-06-05 16:50
 **/
@RestController
@RequestMapping("/FMCGSales/API/meng_niu_open")
@Slf4j
public class MengNiuOpenController {
    public static final Integer ONE_TERMINAL_TENANT_ID = 777421;
    @Resource
    private PaasDataProxy paasDataProxy;

    @PostMapping("/find_account_by_contact")
    public FindAccountByContactUnionId.Result findAccountByContactUnionId(@RequestBody FindAccountByContactUnionId.Arg arg) {
        log.info("findAccountByContactUnionId arg {}", JSON.toJSONString(arg));
        if (Strings.isNullOrEmpty(arg.getUnionId())) {
            return FindAccountByContactUnionId.Result.error(10001, "The required parameters are null");
        }

        Integer tenantId = ONE_TERMINAL_TENANT_ID;
        if (Objects.nonNull(arg.getTenantId())) {
            tenantId = arg.getTenantId();
        }

        PaasDataQueryWithFields.Arg findContactArg = new PaasDataQueryWithFields.Arg();
        PaasDataQueryWithFields.QueryDTO queryDTO = new PaasDataQueryWithFields.QueryDTO.Builder()
                .limit(1).offset(0).build();
        queryDTO.getWheres().add(
                new PaasDataQueryWithFields.WhereDTO.Builder()
                        .appendFilter("wechat_uid__c", "EQ", arg.getUnionId())
                        .build());
        queryDTO.getWheres().add(
                new PaasDataQueryWithFields.WhereDTO.Builder()
                        .appendFilter("union_id__c", "EQ", arg.getUnionId())
                        .build());
        findContactArg.setQueryString(JSON.toJSONString(queryDTO));
        findContactArg.setFieldList(Lists.newArrayList(ContactObjApiNames.ACCOUNT_ID));
        PaasDataQueryWithFields.Result findContactResult = paasDataProxy.queryWithFields(tenantId, -10000, ContactObjApiNames.OBJECT_API_NAME, findContactArg);
        if (findContactResult.getErrCode() != 0) {
            return FindAccountByContactUnionId.Result.error(findContactResult.getErrCode(), findContactResult.getErrMessage());
        }
        if (Objects.isNull(findContactResult.getResult()) || Objects.isNull(findContactResult.getResult().getQueryResult())
                || CollectionUtils.isEmpty(findContactResult.getResult().getQueryResult().getDataList())) {
            return FindAccountByContactUnionId.Result.error(10002, "No contact was found");
        }

        if (Objects.isNull(findContactResult.getResult().getQueryResult().getDataList().get(0).get(ContactObjApiNames.ACCOUNT_ID))) {
            return FindAccountByContactUnionId.Result.error(10004, "The contact is not associated with any customer");
        }

        String accountId = findContactResult.getResult().getQueryResult().getDataList().get(0).getString(ContactObjApiNames.ACCOUNT_ID);

        PaasDataWebDetail.Arg findAccountArg = new PaasDataWebDetail.Arg();
        findAccountArg.setObjectDataId(accountId);
        findAccountArg.setObjectDescribeApiName(AccountObjApiNames.OBJECT_API_NAME);
        PaasDataWebDetail.Result findAccountResult = paasDataProxy.webDetail(tenantId, -10000, AccountObjApiNames.OBJECT_API_NAME, findAccountArg);

        if (findAccountResult.getErrCode() != 0) {
            return FindAccountByContactUnionId.Result.error(findAccountResult.getErrCode(), findAccountResult.getErrMessage());
        }
        if (Objects.isNull(findAccountResult.getResult()) || Objects.isNull(findAccountResult.getResult().getData())) {
            return FindAccountByContactUnionId.Result.error(10003, "Contact related customer not found");
        }

        FindAccountByContactUnionId.ResultData resultData = new FindAccountByContactUnionId.ResultData();
        resultData.setAccountData(findAccountResult.getResult().getData());
        resultData.setDescribe(findAccountResult.getResult().getObjectDescribeExt());
        return FindAccountByContactUnionId.Result.success(resultData);
    }
}
