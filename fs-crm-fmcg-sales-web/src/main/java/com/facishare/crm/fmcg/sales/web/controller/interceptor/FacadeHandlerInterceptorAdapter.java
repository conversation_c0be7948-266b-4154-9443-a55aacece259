package com.facishare.crm.fmcg.sales.web.controller.interceptor;

import com.facishare.crm.fmcg.sales.web.context.ApiContext;
import com.facishare.crm.fmcg.sales.web.context.ApiContextManager;
import com.facishare.paas.I18N;
import com.google.common.base.Strings;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.AsyncHandlerInterceptor;


@Slf4j
public class FacadeHandlerInterceptorAdapter implements AsyncHandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        ApiContext context = loadContextFromRequest(request);
        ApiContextManager.setContext(context);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        ApiContextManager.removeContext();
    }

    private ApiContext loadContextFromRequest(HttpServletRequest request) {
        String tenantId = request.getHeader("X-fs-Enterprise-Id");
        String tenantAccount = request.getHeader("x-fs-enterprise-account");
        String employeeId = request.getHeader("X-fs-Employee-Id");
        String appId = request.getHeader("x-app-id");
        String postId = request.getHeader("X-fs-Post-Id");
        String local = request.getHeader("X-fs-Locale");
        if (!Strings.isNullOrEmpty(local)) {
            I18N.setContext(tenantId, local);
        }

        return ApiContext.builder()
                .tenantId(tenantId)
                .tenantAccount(tenantAccount)
                .employeeId(null == employeeId ? null : Integer.parseInt(employeeId))
                .appId(appId)
                .postId(postId)
                .build();
    }
}
