package com.facishare.crm.fmcg.sales.web.controller.inner;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.sales.cache.InterconnectionRelationCache;
import com.facishare.crm.fmcg.sales.mq.action.EnterpriseRelationObjAction;
import com.facishare.crm.fmcg.sales.model.interconnection.Relation;
import com.facishare.crm.fmcg.sales.web.controller.service.abstraction.InterconnectionAppService;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> l<PERSON><PERSON><PERSON>
 * @create : 2024-03-25 15:23
 **/
@RestController
@RequestMapping("/FMCGSales/API/inner/interconnection")
@Slf4j
public class InterconnectionAppController {
    @Resource
    private EnterpriseRelationObjAction enterpriseRelationObjAction;
    @Resource
    private InterconnectionAppService interconnectionAppService;
    @Resource
    private InterconnectionRelationCache interconnectionRelationCache;

    @PostMapping("/handle_downstream_data")
    public String handleDownstreamData(@RequestBody JSONObject arg) {
        String tenantId = arg.getString("tenantId");
        List<String> dataIds = arg.getJSONArray("dataIds").toJavaList(String.class);
        for (String dataId : dataIds) {
            try {
                enterpriseRelationObjAction.handleDownstreamData(tenantId, dataId);
            } catch (Exception e) {
                log.error("handleDownstreamData is error ", e);
                log.info("handleDownstreamData is error {}, {}", tenantId, dataId);
            }
        }
        return "success";
    }

    @PostMapping("/relation")
    public Relation.Result relation(@RequestBody Relation.Arg arg) {
        return interconnectionAppService.relation(arg);
    }

    @PostMapping("/clear_relation_cache")
    public String clearRelationCache(@RequestBody JSONObject arg) {
        String tenantId = arg.getString("tenantId");
        if (Strings.isNullOrEmpty(tenantId)) {
            return "tenantId is null";
        }
        try {
            log.info("before clear {}", JSON.toJSONString(interconnectionRelationCache.getNoException(tenantId)));
            interconnectionRelationCache.clear(tenantId);
            log.info("after clear {}", JSON.toJSONString(interconnectionRelationCache.getNoException(tenantId)));
        } catch (Exception e) {
            log.error("clear is error : ", e);
            return "failed";
        }
        return "success";
    }

}
