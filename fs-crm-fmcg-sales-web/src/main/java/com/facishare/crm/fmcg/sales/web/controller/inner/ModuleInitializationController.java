package com.facishare.crm.fmcg.sales.web.controller.inner;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.sales.model.init.InitFmcgSalesField;
import com.facishare.crm.fmcg.sales.web.controller.service.abstraction.IModuleInitializationService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @description:
 * @author: wupf
 * @date: 2023/8/31 16:41
 */
@RestController
@RequestMapping(value = "/FMCGSales/API/inner/module_initialization", produces = "application/json")
public class ModuleInitializationController {

    @Resource
    private IModuleInitializationService moduleInitializationService;

    @Deprecated
    @RequestMapping(value = "/init_fmcg_sales_fields")
    public JSONObject initFmcgSalesFields(@RequestParam String tenantId) {
        return moduleInitializationService.initFmcgSalesFields(tenantId);
    }

    @RequestMapping(value = "/init_fmcg_sales_fields_v2", method = RequestMethod.POST)
    public InitFmcgSalesField.Result initFmcgSalesFieldsV2(@RequestBody InitFmcgSalesField.Arg arg) {
        return moduleInitializationService.initFmcgSalesFieldsV2(arg);
    }

    @RequestMapping(value = "/init_multi_unit_related_obj_fields")
    public JSONObject initMultiUnitRelatedObjFields(@RequestParam String tenantId) {
        return moduleInitializationService.initMultiUnitRelatedObjFields(tenantId);
    }

    @RequestMapping(value = "/init_deadline_day")
    public JSONObject initDeadlineDay(@RequestParam String tenantId) {
        return moduleInitializationService.initDeadlineDay(tenantId);
    }

    @RequestMapping(value = "/init_purchase_return_status")
    public JSONObject initPurchaseReturnStatus(@RequestParam String tenantId) {
        return moduleInitializationService.initPurchaseReturnStatus(tenantId);
    }

    @RequestMapping(value = "/init_order_goods_meeting")
    public JSONObject initOrderGoodsMeeting(@RequestParam String tenantId) {
        return moduleInitializationService.initOrderGoodsMeeting(tenantId);
    }
}