package com.facishare.crm.fmcg.sales.web.controller.service.abstraction;

import com.alibaba.fastjson.JSONObject;

/**
 * @description:
 * @author: wupf
 * @date: 2023/8/31 16:41
 */
public abstract class AbstractInitializationService {

    protected static final int PAAS_SUPER_USER_ID = -10000;

    public JSONObject setSuccess(Object data) {
        JSONObject result = new JSONObject();
        result.put("errorCode", "0");
        result.put("data", data);
        return result;
    }

    public JSONObject setFailure(String errorMsg) {
        JSONObject result = new JSONObject();
        result.put("errorCode", "1");
        result.put("data", errorMsg);
        return result;
    }
}