package com.facishare.crm.fmcg.sales.web.controller.inner;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.sales.apiname.InspectionRecordObjApiNames;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> liu<PERSON>yu
 * @create : 2024-08-01 16:30
 **/
@Slf4j
@RestController
@RequestMapping("/FMCGSales/API/inner/inspection")
public class InspectionController {
    @Resource
    private ServiceFacade serviceFacade;

    @PostMapping("illegally_goods_automatic_confirmation")
    public void illegallyGoodsAutomaticConfirmation(@RequestBody JSONObject arg) {
        log.info("illegallyGoodsAutomaticConfirmation tenantId {} dataId {} ", arg.getString("tenantId"), arg.getString("dataId"));
        if (Objects.isNull(arg.get("tenantId")) || Objects.isNull(arg.get("dataId"))) {
            return;
        }
        String tenantId = arg.getString("tenantId");
        String dataId = arg.getString("dataId");
        User systemUser = User.systemUser(tenantId);

        IObjectData objectData = serviceFacade.findObjectDataIgnoreAll(systemUser, dataId, InspectionRecordObjApiNames.OBJECT_API_NAME);

        if (!"0".equals(objectData.get(InspectionRecordObjApiNames.PROCESSING_STATE, String.class))) {
            return;
        }

        Map<String, Object> updateMap = Maps.newHashMap();
        updateMap.put(InspectionRecordObjApiNames.PROCESSING_STATE, "1");
        updateMap.put(InspectionRecordObjApiNames.APPEAL_TIME, System.currentTimeMillis());
        serviceFacade.updateWithMap(systemUser, objectData, updateMap);
    }
}
