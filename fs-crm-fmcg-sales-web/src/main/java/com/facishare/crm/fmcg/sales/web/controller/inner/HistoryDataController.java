package com.facishare.crm.fmcg.sales.web.controller.inner;

import com.facishare.crm.fmcg.sales.model.history.RefreshRebateAmount;
import com.facishare.crm.fmcg.sales.web.controller.service.abstraction.HistoryDataService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description:
 * @author: wupf
 * @date: 2023/8/31 16:41
 */
@RestController
@RequestMapping(value = "/FMCGSales/API/inner/history_data", produces = "application/json")
public class HistoryDataController {

    @Resource
    private HistoryDataService historyDataService;

    @RequestMapping(value = "/refresh_rebate_amount")
    public RefreshRebateAmount.Result refreshRebateAmount(@RequestBody RefreshRebateAmount.Arg arg) {
        return historyDataService.refreshRebateAmount(arg);
    }
}