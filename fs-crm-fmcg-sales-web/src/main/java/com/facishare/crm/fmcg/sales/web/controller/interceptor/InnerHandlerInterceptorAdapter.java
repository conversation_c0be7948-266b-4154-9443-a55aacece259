package com.facishare.crm.fmcg.sales.web.controller.interceptor;

import com.facishare.crm.fmcg.sales.web.context.ApiContext;
import com.facishare.crm.fmcg.sales.web.context.ApiContextManager;
import com.facishare.paas.I18N;
import com.google.common.base.Strings;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.AsyncHandlerInterceptor;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

//TODO：和FacadeHandlerInterceptorAdapter本质上有区别吗？能合并成一个？
@Slf4j
@Component
public class InnerHandlerInterceptorAdapter implements AsyncHandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        ApiContext context = loadContextFromRequest(request);
        ApiContextManager.setContext(context);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        ApiContextManager.removeContext();
    }

    private ApiContext loadContextFromRequest(HttpServletRequest request) {
        String tenantId = getHeader(request, Arrays.asList("X-fs-Enterprise-Id", "X-fs-ei", "x-tenant-id"));
        String tenantAccount = getHeader(request, Collections.singletonList("x-fs-enterprise-account"));
        String employeeId = getHeader(request, Arrays.asList("X-fs-Employee-Id", "x-fs-userInfo", "x-user-id"));
        String appId = getHeader(request, Collections.singletonList("x-app-id"));
        String postId = getHeader(request, Collections.singletonList("X-fs-Post-Id"));
        String local = getHeader(request, Collections.singletonList("X-fs-Locale"));
        if (!Strings.isNullOrEmpty(local)) {
            I18N.setContext(tenantId, local);
        }
        return ApiContext.builder()
                .tenantId(tenantId)
                .tenantAccount(tenantAccount)
                .employeeId(null == employeeId ? null : Integer.parseInt(employeeId))
                .appId(appId)
                .postId(postId)
                .build();
    }

    public String getHeader(HttpServletRequest request, List<String> keyList) {
        String value = null;
        for (String key : keyList) {
            value = request.getHeader(key);
            if (value != null) {
                break;
            }
        }
        return value;
    }
}
