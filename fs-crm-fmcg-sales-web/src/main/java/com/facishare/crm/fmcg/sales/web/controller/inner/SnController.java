package com.facishare.crm.fmcg.sales.web.controller.inner;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.sales.mq.consumer.model.MessageBodyObj;
import com.facishare.crm.fmcg.sales.mq.service.FMCGSnActionService;
import com.github.trace.executor.MonitorTaskWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> liuhaoyu
 * @create : 2025-05-28 14:17
 **/
@Slf4j
@RestController
@RequestMapping("/FMCGSales/API/inner/sn")
public class SnController {
    @Resource
    private FMCGSnActionService fmcgSnActionService;

    @PostMapping("record_sn_status_by_action")
    public void recordSnStatusByAction(@RequestBody JSONObject arg) {
        new Thread(MonitorTaskWrapper.wrap(() -> {
            for (MessageBodyObj messageBodyObj : arg.getJSONArray("dataList").toJavaList(MessageBodyObj.class)) {
                try {
                    log.info("messageBodyObj: {}", messageBodyObj);
                    fmcgSnActionService.recordSnStatusByAction(messageBodyObj);
                } catch (Exception e) {
                    log.info("msg is fail{}, {}, {}", messageBodyObj.getTenantId(), messageBodyObj.getApiName(), messageBodyObj.getDataId());
                    log.error("record_sn_status_by_action is error", e);
                }
            }
        })).start();
    }
}
