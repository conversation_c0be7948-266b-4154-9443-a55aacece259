package com.facishare.crm.fmcg.sales.web.controller.service;

import com.facishare.crm.fmcg.sales.model.history.RefreshRebateAmount;
import com.facishare.crm.fmcg.sales.mq.action.RebateObjAction;
import com.facishare.crm.fmcg.sales.web.controller.service.abstraction.AbstractInitializationService;
import com.facishare.crm.fmcg.sales.web.controller.service.abstraction.HistoryDataService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description:
 * @author: wupf
 * @date: 2023/8/31 16:41
 */
@Service
public class HistoryDataServiceImpl extends AbstractInitializationService implements HistoryDataService {

    @Resource(name = "rebateObjAction")
    private RebateObjAction rebateObjAction;

    @Override
    public RefreshRebateAmount.Result refreshRebateAmount(RefreshRebateAmount.Arg arg) {
        rebateObjAction.refreshPaymentObjRebateAmount(arg.getTenantId(), arg.getRebateObjId());
        RefreshRebateAmount.Result result = new RefreshRebateAmount.Result();
        result.setErrorCode(0);
        result.setErrorMsg("success");
        return result;
    }
}