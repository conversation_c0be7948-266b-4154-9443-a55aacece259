package com.facishare.crm.fmcg.sales.web.controller.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.sales.apiname.*;
import com.facishare.crm.fmcg.sales.business.MultipleUnitBusiness;
import com.facishare.crm.fmcg.sales.enums.MagicEnum;
import com.facishare.crm.fmcg.sales.exception.ApiException;
import com.facishare.crm.fmcg.sales.model.bo.ObjectRecordType;
import com.facishare.crm.fmcg.sales.model.init.InitFmcgSalesField;
import com.facishare.crm.fmcg.sales.service.ButtonManagerService;
import com.facishare.crm.fmcg.sales.service.DescribeManagerService;
import com.facishare.crm.fmcg.sales.service.RecordTypeManagerService;
import com.facishare.crm.fmcg.sales.utils.I18nUtil;
import com.facishare.crm.fmcg.sales.web.controller.service.abstraction.AbstractInitializationService;
import com.facishare.crm.fmcg.sales.web.controller.service.abstraction.IModuleInitializationService;
import com.fmcg.framework.http.CheckinProxy;
import com.fmcg.framework.http.PaasOptionProxy;
import com.fmcg.framework.http.TPMProxy;
import com.fmcg.framework.http.contract.checkin.OpenOrderFair;
import com.fmcg.framework.http.contract.paas.option.CreateOption;
import com.fmcg.framework.http.contract.paas.option.FindOption;
import com.fmcg.framework.http.contract.tpm.InitOrderGoods;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description:
 * @author: wupf
 * @date: 2023/8/31 16:41
 */
@Service
public class ModuleInitializationServiceImpl extends AbstractInitializationService implements IModuleInitializationService {

    @Resource
    private MultipleUnitBusiness multipleUnitBusiness;

    @Resource
    private DescribeManagerService describeManagerService;

    @Resource
    private PaasOptionProxy paasOptionProxy;

    @Resource
    private EIEAConverter eieaConverter;

    @Resource
    private ButtonManagerService buttonManagerService;

    @Resource
    private CheckinProxy checkinProxy;

    @Resource
    private TPMProxy tpmProxy;

    @Resource
    private RecordTypeManagerService recordTypeManagerService;

    @Deprecated
    @Override
    public JSONObject initFmcgSalesFields(String tenantId) {
        describeManagerService.addFields(tenantId, ReturnedGoodsInvoiceApiNames.OBJECT_API_NAME, ReturnedGoodsInvoiceApiNames.REFUND_METHOD,
                ReturnedGoodsInvoiceApiNames.CAR_SALES_ORDER_ID, ReturnedGoodsInvoiceApiNames.CAR_SALES_DEDUCTION_AMOUNT, ReturnedGoodsInvoiceApiNames.CAR_SALES_RETURNED_AMOUNT);
        describeManagerService.addFields(tenantId, SalesOrderApiNames.OBJECT_API_NAME, SalesOrderApiNames.CAR_SALES_DEDUCTION_AMOUNT, SalesOrderApiNames.HIST_OFFSET_AMOUNT);
        describeManagerService.addFields(tenantId, PaymentApiNames.OBJECT_API_NAME, PaymentApiNames.ORIGIN_OBJECT_API_NAME, PaymentApiNames.ORIGIN_OBJECT_DATA_ID, PaymentApiNames.ORIGIN_OBJECT);
        return setSuccess("success");
    }

    @Override
    public InitFmcgSalesField.Result initFmcgSalesFieldsV2(InitFmcgSalesField.Arg arg) {
        String tenantId = arg.getTenantId();
        boolean showInLayout = arg.isShowInLayout();
        describeManagerService.addFields(showInLayout, tenantId, ReturnedGoodsInvoiceApiNames.OBJECT_API_NAME, ReturnedGoodsInvoiceApiNames.REFUND_METHOD,
                ReturnedGoodsInvoiceApiNames.CAR_SALES_ORDER_ID, ReturnedGoodsInvoiceApiNames.CAR_SALES_DEDUCTION_AMOUNT, ReturnedGoodsInvoiceApiNames.CAR_SALES_RETURNED_AMOUNT);
        describeManagerService.addFields(showInLayout, tenantId, SalesOrderApiNames.OBJECT_API_NAME, SalesOrderApiNames.CAR_SALES_DEDUCTION_AMOUNT,
                SalesOrderApiNames.HIST_OFFSET_AMOUNT, SalesOrderApiNames.SALES_MODE);
        describeManagerService.addFields(showInLayout, tenantId, SalesOrderProductApiNames.OBJECT_API_NAME, SalesOrderProductApiNames.SALES_TYPE,
                SalesOrderProductApiNames.BILLING_LABEL);
        describeManagerService.addFields(showInLayout, tenantId, PaymentApiNames.OBJECT_API_NAME, PaymentApiNames.ORIGIN_OBJECT_API_NAME,
                PaymentApiNames.ORIGIN_OBJECT_DATA_ID, PaymentApiNames.ORIGIN_OBJECT);

        InitFmcgSalesField.Result result = new InitFmcgSalesField.Result();
        result.setErrorCode(0);
        result.setErrorMsg("success");
        return result;
    }

    @Override
    public JSONObject initMultiUnitRelatedObjFields(String tenantId) {
        boolean multipleUnitEnable = multipleUnitBusiness.enableMultipleUnit(Integer.parseInt(tenantId));
        if (!multipleUnitEnable) {
            return setFailure("don`t enable multipleUnit");
        }

        describeManagerService.addFields(tenantId, MultiUnitRelatedApiNames.OBJECT_API_NAME, MultiUnitRelatedApiNames.IS_SCAN_CODE);
        try {
            describeManagerService.addFieldsToListLayoutTableComponent(tenantId, MultiUnitRelatedApiNames.OBJECT_API_NAME,
                    "MultiUnitRelatedObj_table_component", MultiUnitRelatedApiNames.IS_SCAN_CODE);
        } catch (Exception e) {
            e.printStackTrace();
            return setFailure(e.getMessage());
        }
        return setSuccess("success");
    }

    @Override
    public JSONObject initDeadlineDay(String tenantId) {
        // 1.创建通用选项集
        createProductDeadlineDayOption(Integer.parseInt(tenantId));

        // 2.预置字段，绑定通用选项集
        describeManagerService.addFields(tenantId, PriceBookApiNames.OBJECT_API_NAME, PriceBookApiNames.DEADLINE_DAY);
        describeManagerService.addFields(tenantId, SalesOrderProductApiNames.OBJECT_API_NAME, SalesOrderProductApiNames.DEADLINE_DAY);
        describeManagerService.addFields(tenantId, ProductApiNames.OBJECT_API_NAME, ProductApiNames.SHELF_LIFE);
        return setSuccess("success");
    }

    private void createProductDeadlineDayOption(int tenantId) {
        String optionApiName = "product_deadline_day__c";
        // 通用选项集是否存在
        FindOption.Arg arg = new FindOption.Arg();
        arg.setApiName(optionApiName);
        FindOption.Result findResult = paasOptionProxy.findOption(tenantId, PAAS_SUPER_USER_ID, arg);
        if (findResult.getErrCode() != 0) {
            throw new ApiException(findResult.getErrCode(), findResult.getErrMessage());
        }

        if (MapUtils.isEmpty(findResult.getResult()) || MapUtils.isEmpty(findResult.getResult().getJSONObject("options"))) {
            String day = I18nUtil.get(MagicEnum.day);
            // 不存在时创建
            CreateOption.OptionInfo optionInfo = new CreateOption.OptionInfo();
            optionInfo.setApiName(optionApiName);
            optionInfo.setLabel(I18nUtil.get(MagicEnum.daysOfProductExpiration));
            String format = String.format("[{\"label\": \"7%s\",\"value\": \"7\"},{\"label\": \"15%s\",\"value\": \"15\"},{\"label\": \"30%s\",\"value\": \"30\"},{\"label\": \"45%s\",\"value\": \"45\"},{\"label\": \"60%s\",\"value\": \"60\"},{\"label\": \"90%s\",\"value\": \"90\"}]",
                    day, day, day, day, day, day);
            optionInfo.setOptions(JSON.parseArray(format));
            CreateOption.Arg createArg = new CreateOption.Arg();
            createArg.setOption(optionInfo);
            CreateOption.Result createResult = paasOptionProxy.createOption(tenantId, PAAS_SUPER_USER_ID, createArg);
            if (createResult.getErrCode() != 0) {
                throw new ApiException(createResult.getErrCode(), createResult.getErrMessage());
            }
        }
    }

    @Override
    public JSONObject initPurchaseReturnStatus(String tenantId) {
        describeManagerService.addFields(tenantId, GoodsReceivedNoteApiNames.OBJECT_API_NAME, GoodsReceivedNoteApiNames.PURCHASE_RETURN_STATUS);
        return setSuccess("success");
    }

    @Override
    public JSONObject initOrderGoodsMeeting(String tenantId) {
        // 1.新增字段
        describeManagerService.addFields(tenantId, PricePolicyApiNames.OBJECT_API_NAME, PricePolicyApiNames.ORDER_GOODS_MEETING_FLAG);
        describeManagerService.addFields(tenantId, RebatePolicyApiNames.OBJECT_API_NAME, RebatePolicyApiNames.ORDER_GOODS_MEETING_FLAG, RebatePolicyApiNames.ACTIVITY_ID);
        describeManagerService.addFields(tenantId, OrderPaymentApiNames.OBJECT_API_NAME, OrderPaymentApiNames.QUOTE_ID);
        describeManagerService.addFields(tenantId, SalesOrderProductApiNames.OBJECT_API_NAME, SalesOrderProductApiNames.QUOTE_LINE_ID);
        describeManagerService.addFields(tenantId, QuoteLinesApiNames.OBJECT_API_NAME, QuoteLinesApiNames.CUMULATIVE_RETURN_QUANTITY, QuoteLinesApiNames.CUMULATIVE_RETURN_AMOUNT);
        describeManagerService.addFields(tenantId, QuoteApiNames.OBJECT_API_NAME, QuoteApiNames.ORDER_GOODS_MEETING_FLAG, QuoteApiNames.ACTIVITY_ID,
                QuoteApiNames.RETURN_GOODS_STATUS, QuoteApiNames.RECEIVED_AMOUNT, QuoteApiNames.RECEIVING_INTO_ACCOUNT, QuoteApiNames.CUMULATIVE_RETURN_TOTAL_AMOUNT);
        describeManagerService.addFields(tenantId, PaymentApiNames.OBJECT_API_NAME, PaymentApiNames.ORDER_GOODS_MEETING_FLAG, PaymentApiNames.ACTIVITY_ID, PaymentApiNames.REBATE_AMOUNT);
        describeManagerService.addFields(tenantId, SalesOrderApiNames.OBJECT_API_NAME, SalesOrderApiNames.ORDER_GOODS_MEETING_FLAG);

        int ei = Integer.parseInt(tenantId);
        String ea = eieaConverter.enterpriseIdToAccount(ei);

        // 2.自定义业务按钮、业务类型、菜单
        buttonManagerService.addCustomerButton(tenantId, ea, QuoteApiNames.OBJECT_API_NAME, "return_goods__c");
        recordTypeManagerService.createRecordType(ei, ObjectRecordType.AddOne.builder()
                .objectApiName(QuoteApiNames.OBJECT_API_NAME).recordType("order_goods__c").label("订货订单").active(true).build());

        // 3.新增APP自定义应用 和 外勤相关的订货会动作
        checkinProxy.openOrderFair(ei, PAAS_SUPER_USER_ID, ea, new OpenOrderFair.Arg());

        // 4.TPM相关对象、字段、按钮
        tpmProxy.initOrderGoods(ei, PAAS_SUPER_USER_ID, new InitOrderGoods.Arg());
        return setSuccess("success");
    }
}