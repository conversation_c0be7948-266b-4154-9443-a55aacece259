package com.facishare.crm.fmcg.sales.web.controller.service;

import com.facishare.crm.fmcg.sales.cache.InterconnectionRelationCache;
import com.facishare.crm.fmcg.sales.exception.ApiException;
import com.facishare.crm.fmcg.sales.model.interconnection.Relation;
import com.facishare.crm.fmcg.sales.web.controller.service.abstraction.InterconnectionAppService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR> liuhaoyu
 * @create : 2024-04-16 15:12
 **/
@Slf4j
@Service
public class InterconnectionAppServiceImpl implements InterconnectionAppService {
    @Resource
    private InterconnectionRelationCache interconnectionRelationCache;

    @Override
    public Relation.Result relation(Relation.Arg arg) {
        if (Objects.isNull(arg.getTenantId())) {
            return Relation.Result.error(1, "arg tenantId is null");
        }
        Integer tenantId = arg.getTenantId();

        Relation.ResultData resultData = new Relation.ResultData();
        try {
            List<Relation.TenantInfo> relations = Lists.newArrayList();
            findRelation(String.valueOf(tenantId), relations, 1);
            resultData.setRelations(relations);
        } catch (ExecutionException e) {
            log.error("relation is error ", e);
            return Relation.Result.error(1, "Fetch cache exception");
        }
        return Relation.Result.success(resultData);
    }

    private void findRelation(String tenantId, List<Relation.TenantInfo> relations, int deep) throws ExecutionException {
        if (deep > 5) {
            throw new ApiException(10001, "recursion depth > 5");
        }
        Relation.TenantInfo tenantInfo = interconnectionRelationCache.get(tenantId);
        if (Objects.isNull(tenantInfo)) {
            return;
        }
        relations.add(tenantInfo);
        if (Objects.isNull(tenantInfo.getUpstreamTenantId())) {
            return;
        }
        findRelation(String.valueOf(tenantInfo.getUpstreamTenantId()), relations, deep + 1);
    }
}
