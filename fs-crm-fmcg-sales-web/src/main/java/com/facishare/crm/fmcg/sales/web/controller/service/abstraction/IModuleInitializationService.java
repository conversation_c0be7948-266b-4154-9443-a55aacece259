package com.facishare.crm.fmcg.sales.web.controller.service.abstraction;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.sales.model.init.InitFmcgSalesField;

/**
 * @description:
 * @author: wupf
 * @date: 2023/8/31 16:41
 */
public interface IModuleInitializationService {

    JSONObject initFmcgSalesFields(String tenantId);

    InitFmcgSalesField.Result initFmcgSalesFieldsV2(InitFmcgSalesField.Arg arg);

    JSONObject initMultiUnitRelatedObjFields(String tenantId);

    JSONObject initDeadlineDay(String tenantId);

    JSONObject initPurchaseReturnStatus(String tenantId);

    JSONObject initOrderGoodsMeeting(String tenantId);
}