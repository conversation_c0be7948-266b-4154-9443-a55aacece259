package com.facishare.crm.fmcg.sales.config;

import com.facishare.crm.fmcg.sales.web.controller.interceptor.InnerHandlerInterceptorAdapter;
import com.facishare.paas.appframework.fcp.model.FcpRestServerContextListener;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.web.servlet.ServletContextInitializer;
import org.springframework.boot.web.servlet.ServletListenerRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.servlet.ServletContextListener;

@Configuration
@EnableWebMvc
@RequiredArgsConstructor
public class WebBeanConfig implements WebMvcConfigurer {

  private final InnerHandlerInterceptorAdapter handlerInterceptorAdapter;

  /**
   * 提前注入FCP端口号等相关配置
   */
  @Bean("fcpServletContextInitializer")
  public ServletContextInitializer servletContextInitializer() {
    return servletContext -> {
      servletContext.setInitParameter("fcpServerPort", "12016");
      servletContext.setInitParameter("enableFcpFile", "true");
    };
  }

  /**
   * 注入 FCP Server Context Listener，启动FCP Server
   *
   * @return FCP Server Context Listener
   */
  @Bean
  @DependsOn("fcpServletContextInitializer")
  public ServletListenerRegistrationBean<ServletContextListener> configContextListener() {
    ServletListenerRegistrationBean<ServletContextListener> bean = new ServletListenerRegistrationBean<>();
    FcpRestServerContextListener listener = new FcpRestServerContextListener();
    bean.setListener(listener);
    return bean;
  }

  @Override
  public void addInterceptors(InterceptorRegistry registry) {
    // 拦截Controller
    registry.addInterceptor(handlerInterceptorAdapter)
            .addPathPatterns("/FMCGSales/API/**");
  }

}
