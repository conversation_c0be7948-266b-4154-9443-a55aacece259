<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans" xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd">
    <bean id="springContextHolder" class="com.facishare.crm.fmcg.sales.factory.SpringContextHolder"
          lazy-init="false"/>
    <import resource="classpath:fs-uc-rest.xml"/>
    <bean id="redisSupport" class="com.github.jedis.support.JedisFactoryBean" p:configName="checkins-v2-redis"/>
    <bean id="crmNotifyService" class="com.facishare.restful.client.FRestApiProxyFactoryBean">
        <property name="type" value="com.fxiaoke.api.CRMNotifyService"/>
    </bean>
    <bean id="nomonProducer" class="com.fxiaoke.paas.gnomon.api.NomonProducer"/>
</beans>

