{"describe_api_name": "Quote<PERSON><PERSON><PERSON>", "is_index": true, "is_active": true, "is_encrypted": false, "is_unique": false, "is_need_convert": false, "is_required": false, "count_to_zero": true, "description": "累计还货总金额", "label": "累计还货总金额", "type": "count", "sub_object_describe_apiname": "QuoteLinesObj", "field_api_name": "quote_id", "count_type": "sum", "count_field_api_name": "cumulative_return_amount", "count_field_type": "currency", "return_type": "currency", "wheres": [], "default_result": "d_zero", "decimal_places": 2, "round_mode": 4, "api_name": "cumulative_return_total_amount", "define_type": "package", "is_index_field": false, "is_single": false, "status": "new"}