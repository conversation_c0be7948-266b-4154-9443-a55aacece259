{"describe_api_name": "ReturnedGoodsInvoiceObj", "is_index": true, "is_active": true, "is_readonly": false, "is_encrypted": false, "is_unique": false, "is_need_convert": false, "is_required": false, "description": "车销订单号", "label": "车销订单号", "type": "object_reference", "target_api_name": "SalesOrderObj", "target_related_list_name": "car_sales_order_returned_goods_list", "target_related_list_label": "退货单(车销)", "action_on_target_delete": "cascade_delete", "api_name": "car_sales_order_id", "wheres": [{"connector": "OR", "filters": [{"value_type": 2, "field_name": "account_id", "operator": "EQ", "field_values": ["$account_id$"]}]}], "define_type": "package", "is_index_field": false, "is_single": false, "status": "new"}