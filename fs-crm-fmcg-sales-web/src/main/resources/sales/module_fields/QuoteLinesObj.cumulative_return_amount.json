{"describe_api_name": "QuoteLinesObj", "is_index": true, "is_active": true, "is_encrypted": false, "is_unique": false, "is_need_convert": false, "is_required": false, "count_to_zero": true, "description": "累计还货金额", "label": "累计还货金额", "type": "count", "sub_object_describe_apiname": "SalesOrderProductObj", "field_api_name": "quote_line_id", "count_type": "sum", "count_field_api_name": "subtotal", "count_field_type": "currency", "return_type": "currency", "wheres": [{"connector": "OR", "filters": [{"value_type": 0, "field_name": "life_status", "operator": "HASANYOF", "field_values": ["under_review", "normal", "in_change"]}]}], "default_result": "d_zero", "decimal_places": 2, "round_mode": 4, "api_name": "cumulative_return_amount", "define_type": "package", "is_index_field": false, "is_single": false, "status": "new"}