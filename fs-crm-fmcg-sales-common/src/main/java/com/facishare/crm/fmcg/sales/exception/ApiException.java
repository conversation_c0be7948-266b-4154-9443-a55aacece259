package com.facishare.crm.fmcg.sales.exception;

import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class ApiException extends RuntimeException {

    private final String errMsg;
    private final Integer errCode;

    public ApiException(Integer errCode, String errMsg) {
        super(errMsg);
        this.errMsg = errMsg;
        this.errCode = errCode;
    }

    public ApiException(SalesErrorCode error) {
        super(error.getMessage());
        this.errCode = error.getCode();
        this.errMsg = error.getMessage();
    }
}
