package com.facishare.crm.fmcg.sales.enums;

public enum ScanEncryptionTypeEnum {
    NONE(0, "不需要加密"),

    DOUBLE_MD5_UPPERCASE(10, "两次MD5加大写");

    private final Integer type;

    private final String desc;

    ScanEncryptionTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

}
