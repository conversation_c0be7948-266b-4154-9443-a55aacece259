package com.facishare.crm.fmcg.sales.apiname;

/**
 * @description: 快消订货灰度变量
 * @author: yangyx
 * @date: 2023-02-13 11:15
 **/
public interface FmcgSalesOrderGrayConfig {

    /**
     * gray-rel-fmcg配置文件
     */
    String CONFIG_GRAY_REF_FMCG = "gray-rel-fmcg";
    String ROUTE_CONFIG = "fs-fmcg-sdk-apis";
    String FS_METADATA_CONFIG = "fs-metadata";

    /**
     * 客开企业配置
     */
    String HAOLIYOU = "HAOLIYOU";
    String YUANQI = "YUANQI";
    String YINLU_ORDER = "YINLU_ORDER";
    String HAOXIANGNI = "HAOXIANGNI";
    String CHUANHUA = "CHUANHUA";
    String ZHONGKEN = "ZHONGKEN";
    String MENGNIU = "MENGNIU";
    String XIANCHUN = "XIANCHUN";
    String IS_RIO_TENANT = "IS_RIO_TENANT";
    String XINGHENG = "XINGHENG";
    String FUERJIA = "FUERJIA";

    /**
     * 快消订货下单选产品页支持UI自定义组件
     */
    String SUPPORT_PRODUCT_CUSTOM_INFO_COMPONENT = "SUPPORT_PRODUCT_CUSTOM_INFO_COMPONENT";

    /**
     * 中肯预订单灰度
     */
    String RECENT_DEAL_ORDER = "RECENT_DEAL_ORDER";

    /**
     * 订单产品显示布局LAYOUT
     */
    String SALES_ORDER_LAYOUT = "SALES_ORDER_LAYOUT";

    /**
     * 订货确认页面显示总数单位
     */
    String SHOW_ORDER_COUNT_UNIT = "SHOW_ORDER_COUNT_UNIT";

    /**
     * 库存打印过滤库存为0的
     */
    String SHOW_STOCK_QUANTITY_PRINT = "SHOW_STOCK_QUANTITY_PRINT";

    /**
     * 快消订货下单选产品页UI自定义组件函数名称配置
     */
    String PRODUCT_CUSTOM_INFO_COMPONENT_FUNCTION = "PRODUCT_CUSTOM_INFO_COMPONENT_FUNCTION";

    /**
     * 快消订货订单确认页拆分订单函数名称配置
     */
    String SPLIT_ORDERS_FUNCTION = "SPLIT_ORDERS_FUNCTION";

    /**
     * 下单选产品页面支持UI事件的字段
     */
    String CHOOSE_PRODUCT_PAGE_SUPPORT_UI_EVENT_FIELD_LIST = "CHOOSE_PRODUCT_PAGE_SUPPORT_UI_EVENT_FIELD_LIST";

    /**
     * 查询最近的交易产品时间   new Date() - config.date
     */
    String QUERY_RECENT_DEAL_PRODUCT_DATE = "QUERY_RECENT_DEAL_PRODUCT_DATE";

    /**
     * 订单创建服务的对应service bean map
     */
    String SALES_ORDER_ADD_ACTION_BEAN_NAME_MAP = "SALES_ORDER_ADD_ACTION_BEAN_NAME_MAP";
    String SALES_ACTION_BEAN_NAME_MAP = "SALES_ACTION_BEAN_NAME_MAP";

    String PAY_ONLINE_BY_CCB = "PAY_ONLINE_BY_CCB";
    String PAY_ONLINE_SUPPORT_UNION = "PAY_ONLINE_SUPPORT_UNION";
    String PAY_ONLINE_MODIFY_AMOUNT = "PAY_ONLINE_MODIFY_AMOUNT";

    /**
     * 使用独立库的大对象企业配置
     */
    String BIG_OBJECT_USE_NEW_DB = "big-object-use-new-db";

    /**
     * APP图标进入的业务模块 所对应的对象名称
     */
    String BUSINESS_TYPE_TO_OBJ_NAME = "BUSINESS_TYPE_TO_OBJ_NAME";

    /**
     * 自定义对象与标准接口字段映射
     */
    String CUSTOM_OBJ_FIELDS_MAPPING = "CUSTOM_OBJ_FIELDS_MAPPING";

    /**
     * 自定义对象主从对象名
     */
    String CUSTOM_MASTER_SLAVE_MAP = "CUSTOM_MASTER_SLAVE_MAP";

    /**
     * 窜货自动确认延迟时间
     */
    String ILLEGALLY_GOODS_AUTOMATIC_CONFIRMATION_DELAY_TIME = "ILLEGALLY_GOODS_AUTOMATIC_CONFIRMATION_DELAY_TIME";

    String FMCG_SN_CODE_SCAN_RULES = "FMCG_SN_CODE_SCAN_RULES";
}
