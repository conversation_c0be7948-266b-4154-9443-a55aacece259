package com.facishare.crm.fmcg.sales.apiname;

public interface FMCGSerialNumberStatusApiNames {
    String OBJECT_API_NAME = "FMCGSerialNumberStatusObj";
    String BUSINESS_OBJECT_ID = "business_object_id";

    String SOURCE_SYSTEM = "source_system";

    String LATITUDE = "latitude";

    String CURRENT_STATE = "current_state";

    String NEXT_BUSINESS_ID = "next_business_id";

    String PERSONNEL_NAME = "personnel_name";

    String FMCG_SERIAL_NUMBER_ID = "fmcg_serial_number_id";

    String BUSINESS_OCCURRENCE_TIME = "business_occurrence_time";

    String NEXT_BUSINESS_TYPE = "next_business_type";

    String PERSONNEL_ID = "personnel_id";

    String NODE_TYPE = "node_type";

    String NODE_NAME_ID = "node_name_id";

    String BUSINESS_OBJECT = "business_object";

    String PERSONNEL_ROLE = "personnel_role";

    String PRODUCT_ID = "product_id";

    String BUSINESS_TYPE = "business_type";

    String BELONG_BUSINESS_ID = "belong_business_id";

    String NEXT_NODE_ID = "next_node_id";

    String PERSONNEL_PHONE = "personnel_phone";

    String PERSONNEL_HOST_ROLE = "personnel_host_role";

    String LONGITUDE = "longitude";

    String PERSONNEL_HOST_ROLE_NAME = "personnel_host_role_name";

    String CURRENT_TENANT_ID = "current_tenant_id";

    String CURRENT_TENANT_NAME = "current_tenant_name";

    String NEXT_TENANT_NAME = "next_tenant_name";

    String BUSINESS_OBJECT_NAME = "business_object_name";

    String BUSINESS_OBJECT_NUMBER = "business_object_number";
    String PERSONNEL_OBJECT_API_NAME = "personnel_object_api_name";

    String WHETHER_ABNORMAL = "whether_abnormal";
    String EXCEPTION_TYPE = "exception_type";
}