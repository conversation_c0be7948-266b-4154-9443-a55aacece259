package com.facishare.crm.fmcg.sales.exception;

import com.facishare.paas.appframework.core.exception.ErrorCode;
import com.facishare.paas.appframework.core.i18n.I18NExt;

import java.text.MessageFormat;

public enum SalesErrorCode implements ErrorCode {
    ;
    private int code;

    private String i18nKey;

    private String defaultMessage;

    @Override
    public int getCode() {
        return code;
    }

    public String getMessage() {
        return I18NExt.getOrDefault(i18nKey, defaultMessage);
    }
}
