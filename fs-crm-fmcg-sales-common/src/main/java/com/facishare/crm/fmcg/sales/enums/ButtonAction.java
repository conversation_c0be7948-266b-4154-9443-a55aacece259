package com.facishare.crm.fmcg.sales.enums;

import com.facishare.paas.appframework.core.i18n.I18NExt;

public enum ButtonAction {
    Close_Statements("CloseStatements", "paas.udobj.action.close_statements", "确认结清", "CloseStatements_button_default");

    private String actionCode;
    private String actionLabelKey;
    private String defaultActionLabel;
    private String buttonApiName;

    ButtonAction(String actionCode, String actionLabelKey, String defaultActionLabel) {
        this.actionCode = actionCode;
        this.actionLabelKey = actionLabelKey;
        this.defaultActionLabel = defaultActionLabel;
    }

    ButtonAction(String actionCode, String actionLabelKey, String defaultActionLabel, String buttonApiName) {
        this.actionCode = actionCode;
        this.actionLabelKey = actionLabelKey;
        this.defaultActionLabel = defaultActionLabel;
        this.buttonApiName = buttonApiName;
    }

    public String getActionCode() {
        return actionCode;
    }

    public String getActionLabelKey() {
        return actionLabelKey;
    }

    public String getDefaultActionLabel() {
        return defaultActionLabel;
    }

    public String getButtonApiName() {
        return buttonApiName;
    }

    public String getActionLabel() {
        return I18NExt.getOrDefault(actionLabelKey, defaultActionLabel);
    }
}
