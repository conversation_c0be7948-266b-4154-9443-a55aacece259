package com.facishare.crm.fmcg.sales.utils;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.TimeZone;

/**
 * <AUTHOR>
 */
public class DateUtils {

    private DateUtils() {
    }

    public static final String DEFAULT_TIME_ZONE = "GMT+8";

    public static List<Long> getTimeStampStartTimeAndEndTime(Long timeStamp) {
        return getTimeStampStartTimeAndEndTime(timeStamp, DEFAULT_TIME_ZONE);
    }

    public static List<Long> getTimeStampStartTimeAndEndTime(Long timeStamp, String timeZone) {
        Long timeStampStartTime = getTimeStampStartTime(timeStamp, timeZone);
        Long timeStampEndTime = getTimeStampEndTime(timeStamp, timeZone);
        List<Long> list = new ArrayList<>();
        list.add(timeStampStartTime);
        list.add(timeStampEndTime);
        return list;
    }

    /**
     * 获取指定某一天的结束时间戳
     */
    public static Long getTimeStampStartTime(Long timeStamp) {
        return getTimeStampStartTime(timeStamp, DEFAULT_TIME_ZONE);
    }

    public static Long getTimeStampStartTime(Long timeStamp, String timeZone) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeZone(TimeZone.getTimeZone(timeZone));
        calendar.setTimeInMillis(timeStamp);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis();
    }

    /**
     * 获取指定某一天的结束时间戳
     */
    public static Long getTimeStampEndTime(Long timeStamp) {
        return getTimeStampEndTime(timeStamp, DEFAULT_TIME_ZONE);
    }

    public static Long getTimeStampEndTime(Long timeStamp, String timeZone) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeZone(TimeZone.getTimeZone(timeZone));
        calendar.setTimeInMillis(timeStamp);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTimeInMillis();
    }
}
