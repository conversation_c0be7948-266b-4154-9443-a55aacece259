package com.facishare.crm.fmcg.sales.exception;

import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class AdapterException extends RuntimeException {

    private final String errMsg;
    private final Integer errCode;
    private final String service;

    public AdapterException(String service, String errMsg, Integer errCode) {
        super(errMsg);
        this.service = service;
        this.errMsg = errMsg;
        this.errCode = errCode;
    }
}
