package com.facishare.crm.fmcg.sales.apiname;

public interface StockConfigApiNames {
    /**
     * 库存开关 1-未开启 2-开启 3-停用
     */
    String DHT_STOCK_SWITCH = "dht_stock_switch";
    /**
     * 发货单开关 0-未开启 2-开启
     */
    String DELIVERY_NOTE_STATUS = "delivery_note_status";
    /**
     * 采购订单开关 0-未开启 2-开启
     */
    String PURCHASE_ORDER_STATUS = "purchase_order_status";
    /**
     * 退换货单开关 1-未开启 2-开启
     */
    String DHT_EXCHANGE_RETURN_NOTE_SWITCH = "dht_exchange_return_note_switch";
    /**
     * 库存订单校验规则 1-库存不足不允许提交订单 2-库存不足允许提交订单
     */
    String DHT_ORDER_CHECK = "dht_order_check";
    /**
     * 订货通库存显示规则 1-不显示库存 2-精确显示库存 3-模糊显示库存
     */
    String DHT_STOCK_VIEW = "dht_stock_view";
    /**
     * 订单订货规则 1-指定仓库订货 2-合并仓库订货 3-无仓库订货
     */
    String DHT_STOCK_ORDER_TYPE = "dht_stock_order_type";
    /**
     * 库存预警开关 1-未开启 2-开启
     */
    String DHT_STOCK_WARNING_TYPE = "dht_stock_warning_type";
    /**
     * 临到期批次库存预警 1-未开启 2-开启
     */
    String DHT_WILL_EXPIRE_STOCK_WARNING_TYPE = "dht_will_expire_stock_warning_type";
    /**
     * 纷享库存是否对接ERP 1-是 2-否
     */
    String DHT_STOCK_IS_FOR_ERP = "dht_stock_is_for_erp";
    /**
     * 不显示库存为零的记录 1-是 2-否
     */
    String DHT_NOT_SHOW_ZERO_STOCK = "dht_not_show_zero_stock";
    /**
     * 是否只展示已上架的库存记录 1-是 2-否
     */
    String DHT_ONLY_SHOW_ON_SALE_STOCK = "dht_only_show_on_sale_stock";
    /**
     * 安全库存设置 1-统一设置 2-分仓库设置
     */
    String DHT_SAFETY_STOCK_TYPE = "dht_safety_stock_type";
    /**
     * 库存数值型字段精度设置
     */
    String DHT_STOCK_NUMBER_TYPE_DECIMAL = "dht_stock_number_type_decimal";
    /**
     * 批次序列号开关 1-关闭 2-开启
     */
    String DHT_BATCH_AND_SERIAL_NUMBER_SWITCH = "dht_batch_and_serial_number_switch";
    /**
     * 库存扫码类型 1-产品条形码 2-批次 3-序列号
     */
    String DHT_STOCK_SCAN_CODE_TYPE = "dht_stock_scan_code_type";
    /**
     * 订单侧是否展示可用库存 1-是 2-否
     */
    String DHT_IS_SALES_ORDER_SHOW_STOCK = "dht_is_sales_order_show_stock";
    /**
     * 库存报表开关 1-未开启 2-开启
     */
    String STOCK_REPORT_FORM_SWITCH = "stock_report_form_switch";
    /**
     * CPQ组合发货 1-是 2-否
     */
    String DELIVERY_NOTE_CPQ_DELIVER_SWITCH = "delivery_note_cpq_deliver_switch";
    /**
     * 暗盘开关 1-是 2-否
     */
    String STOCK_GREY_CHECK_SWITCH = "stock_grey_check_switch";
    /**
     * 盘点冻结仓库 1-是 2-否
     */
    String STOCK_CHECK_FREEZE_WAREHOUSE = "stock_check_freeze_warehouse";
    /**
     * 发货单退货 1-未开启 2-开启
     */
    String DELIVERY_NOTE_RETURNED_GOODS_SWITCH = "delivery_note_returned_goods_switch";
    /**
     * 无源单退货 1-未开启 2-开启
     */
    String NON_NOTE_RETURNED_GOODS_SWITCH = "non_note_returned_goods_switch";
    /**
     * 换货 1-未开启 2-开启
     */
    String RETURNED_GOODS_EXCHANGE_SWITCH = "returned_goods_exchange_switch";
    /**
     * 快消商品条码 1-未开启 2-开启
     */
    String UNIQUE_PRODUCT_CODE_MANAGEMENT = "unique_product_code_management";
}
