package com.facishare.crm.fmcg.sales.enums;

import lombok.Getter;

/**
 * 用于代替魔法字符串常量
 */
@Getter
public enum MagicEnum {
    day("天", "CRM.FMCG.SALES.day", true),
    daysOfProductExpiration("产品的临期天数", "CRM.FMCG.SALES.daysOfProductExpiration", true),
    cannotDirectlyChangeThisState("不可以直接修改到此状态", "CRM.FMCG.SALES.cannotDirectlyChangeThisState"),
    theActualPaymentShouldBeGreaterThanOrEqualToZero("实交款应大于或等于0，并且不能超过应交款收款", "CRM.FMCG.SALES.theActualPaymentShouldBeGreaterThanOrEqualToZero"),
    theActualPaymentShouldBeLessThanZero("实交款应小于0，并且不能超过应交款退款", "CRM.FMCG.SALES.theActualPaymentShouldBeLessThanZero"),
    abnormalGoodsFlow("窜货异常","CRM.FMCG.SALES.abnormalGoodsFlow"),
    abnormalGoodsFlowNotice("您收到一条窜货异常通知，请尽快处理。","CRM.FMCG.SALES.abnormalGoodsFlowNotice"),
    noOperationPermission("您没有操作权限","CRM.FMCG.SALES.noOperationPermission"),
    currentDataHasBeenProcessed("当前数据已处理","CRM.FMCG.SALES.currentDataHasBeenProcessed"),
    currentDataIsNotAbnormal("当前数据为非异常数据","CRM.FMCG.SALES.currentDataIsNotAbnormal"),
    appealReasonRequired("请填写申诉原因","CRM.FMCG.SALES.appealReasonRequired"),
    deliveryDataNotFound("未获取到当前码的交货单数据","CRM.FMCG.SALES.deliveryDataNotFound"),
    belongEnterpriseUnknown("无法得知当前码的所属企业","CRM.FMCG.SALES.belongEnterpriseUnknown"),
    codeNotFound("没有在系统中找到该码","CRM.FMCG.SALES.codeNotFound"),
    upstreamEnterpriseInfoNotFound("未在上游查询到当前登录企业信息","CRM.FMCG.SALES.upstreamEnterpriseInfoNotFound"),
    theCurrentRuleMustSelectInspectionAccount("当前查询规则必须选择查询客户","CRM.FMCG.SALES.theCurrentRuleMustSelectInspectionAccount"),
    currentLocationNotFound("未获取到当前定位","CRM.FMCG.SALES.currentLocationNotFound"),
    salesAreaNotSet("码归属客户未设置销售区域，暂时无法查询","CRM.FMCG.SALES.salesAreaNotSet"),
    salesAreaFetchFailed("获取当前定位的销售区域失败","CRM.FMCG.SALES.salesAreaFetchFailed"),
    contactSalesManager("请联系您的销售经理设置销售区域","CRM.FMCG.SALES.contactSalesManager"),
    locationCodeInspection("查询当前定位码窜货","CRM.FMCG.SALES.locationCodeInspection"),
    loginAccountCodeInspection("查询登录客户码窜货","CRM.FMCG.SALES.loginAccountCodeInspection"),
    inspectionAccountCodeInspection("查询当前查询客户码窜货","CRM.FMCG.SALES.inspectionAccountCodeInspection"),
    inspectionDesignerIdRequired("稽查设计器ID不能为空","CRM.FMCG.SALES.inspectionDesignerIdRequired"),
    inspectionDesignerNotExist("稽查设计器不存在","CRM.FMCG.SALES.inspectionDesignerNotExist"),
    noAvailableDesigner("没有可用的稽查设计器","CRM.FMCG.SALES.noAvailableDesigner"),
    positionRequired("未传入定位","CRM.FMCG.SALES.positionRequired"),
    interconnectDataNotFound("未在上游中查询到互联企业数据","CRM.FMCG.SALES.interconnectDataNotFound"),
    relatedDataNotFound("未在上游中查询到互联企业关联的数据","CRM.FMCG.SALES.relatedDataNotFound"),
    salesAreaNotConfigured("未设置销售区域，暂时无法查询","CRM.FMCG.SALES.salesAreaNotConfigured"),
    outOfBusinessScope("不能在贵司经营范围外查询","CRM.FMCG.SALES.outOfBusinessScope"),
    nameDuplicate("稽查设计器名称重复","CRM.FMCG.SALES.nameDuplicate"),
    scopeExists("该适用范围已存在","CRM.FMCG.SALES.scopeExists"),
    valueTypeMismatch("值类型不匹配","CRM.FMCG.SALES.valueTypeMismatch"),
    invalidJudgmentRule("窜货判断规则无效","CRM.FMCG.SALES.invalidJudgmentRule"),
    salesAreaNotConfiguredWithContact("未设置销售区域，暂时无法查询, 请联系您的销售经理设置销售区域","CRM.FMCG.SALES.salesAreaNotConfiguredWithContact"),
    undefinedGroupKey("未定义的groupKey","CRM.FMCG.SALES.undefinedGroupKey"),
    undefinedKey("未定义的key","CRM.FMCG.SALES.undefinedKey"),
    ruleIdCannotBeEmpty("ruleId不能为空","CRM.FMCG.SALES.ruleIdCannotBeEmpty"),
    queryMarkCodeRelationInterfaceException("查询中台商品条码关系接口异常","CRM.FMCG.SALES.queryMarkCodeRelationInterfaceException"),
    queryMarkCodeSourceInterfaceException("查询中台商品条码源接口异常","CRM.FMCG.SALES.queryMarkCodeSourceInterfaceException"),
    productNotFound("未在系统中找到该商品","CRM.FMCG.SALES.productNotFound"),
    ;

    MagicEnum(String defaultValue, String i18nKey) {
        this.defaultValue = defaultValue;
        this.i18nKey = i18nKey;
        this.skipI18NCheck = false;
    }

    MagicEnum(String defaultValue, String i18nKey, boolean skipI18NCheck) {
        this.defaultValue = defaultValue;
        this.i18nKey = i18nKey;
        this.skipI18NCheck = skipI18NCheck;
    }

    public static boolean isValidValue(String value) {
        for (MagicEnum magicEnum : MagicEnum.values()) {
            if (magicEnum.name().equalsIgnoreCase(value)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 默认值
     */
    private final String defaultValue;
    /**
     * 多语言key
     */
    private final String i18nKey;
    /**
     * 是否跳过I18N多语校验
     */
    private final boolean skipI18NCheck;
}
