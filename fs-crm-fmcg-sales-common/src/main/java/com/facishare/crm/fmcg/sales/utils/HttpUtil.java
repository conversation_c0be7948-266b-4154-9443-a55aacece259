package com.facishare.crm.fmcg.sales.utils;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.sales.exception.AdapterException;
import com.github.autoconf.ConfigFactory;
import com.github.trace.TraceContext;
import okhttp3.*;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class HttpUtil {
    private static final String CONFIG_NAME = "fs-fmcg-adapter-apis";
    private static final String ENTERPRISE_ID_HEADER = "x-fs-ei";
    private static final String EMPLOYEE_ID_HEADER = "x-fs-userInfo";
    private static final String TRACE_ID_HEADER = "x-fs-trace-id";
    private static final MediaType JSON_MEDIA_TYPE = MediaType.parse("application/json; charset=utf-8");
    private static final OkHttpClient OK_HTTP_CLIENT = new OkHttpClient();

    private static final Logger logger = LoggerFactory.getLogger(HttpUtil.class);

    public static <T, R> R post(String url, Map<String, Object> headers, T arg, Class<R> clazz) throws IOException {
        Request.Builder builder = new Request.Builder();
        String traceId = "";
        if (MapUtils.isNotEmpty(headers)) {
            headers.forEach((k, v) -> builder.addHeader(k, v.toString()));
            traceId = (String) headers.get(TRACE_ID_HEADER);
        }

        String argJson = arg == null ? "{}" : JSON.toJSONString(arg);
        logger.info("http url : {}, traceId : {}, arg : {}", url, traceId, argJson);

        Request request = builder.url(url).post(RequestBody.create(JSON_MEDIA_TYPE, argJson)).build();
        Response response = OK_HTTP_CLIENT.newCall(request).execute();
        if (response.code() != 200) {
            throw new AdapterException("Remote call exception", response.message(), response.code());
        }

        String json = response.body().string();
        // logger.info("http result : {}", json);
        return JSON.parseObject(json, clazz);
    }

    public static <R> R get(String url, Map<String, Object> headers, Class<R> clazz) throws IOException {
        Request.Builder builder = new Request.Builder();
        if (MapUtils.isNotEmpty(headers)) {
            headers.forEach((k, v) -> builder.addHeader(k, v.toString()));
        }

        Request request = builder.url(url).get().build();
        Response response = OK_HTTP_CLIENT.newCall(request).execute();
        if (response.code() != 200) {
            throw new AdapterException("Remote call exception", response.message(), response.code());
        }

        return JSON.parseObject(response.body().string(), clazz);
    }

    public static String initUrl(String key) {
        return ConfigFactory.getConfig(CONFIG_NAME).get(key);
    }

    public static String initUrl(String key, Object... parameters) {
        String url = ConfigFactory.getConfig(CONFIG_NAME).get(key);
        return String.format(url, parameters);
    }

    public static Map<String, Object> initMetadataHeader(Integer tenantId, Integer userId) {
        Map<String, Object> header = new HashMap<>(4);
        header.put(ENTERPRISE_ID_HEADER, tenantId);
        header.put(EMPLOYEE_ID_HEADER, userId.toString());

        TraceContext context = TraceContext.get();
        if (context != null && !StringUtils.isBlank(context.getTraceId())) {
            header.put(TRACE_ID_HEADER, context.getTraceId());
        }
        return header;
    }

    public static Map<String, Object> initMetadataHeader(Integer tenantId) {
        return initMetadataHeader(tenantId, -10000);
    }
}