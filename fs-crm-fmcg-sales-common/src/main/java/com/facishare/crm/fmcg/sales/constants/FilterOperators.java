package com.facishare.crm.fmcg.sales.constants;

/**
 * 详见wiki：https://wiki.firstshare.cn/pages/viewpage.action?pageId=103188998
 */
public interface FilterOperators {
    /**
     * 属于
     */
    String IN = "IN";
    /**
     * 不属于
     */
    String NIN = "NIN";
    /**
     * 等于
     */
    String EQUAL = "EQ";
    /**
     * 包含
     */
    String LIKE = "LIKE";
    /**
     * greater than or equal
     */
    String GTE = "GTE";
    /**
     * less than
     */
    String LT = "LT";
    /**
     * 不等于(可以查出空值)
     */
    String N = "N";
    /**
     * 不等于(不查空值)
     */
    String NEQ = "NEQ";
    /**
     * greater than
     */
    String GT = "GT";
    /**
     * 为空
     */
    String IS = "IS";
    /**
     * 不为空
     */
    String ISN = "ISN";
    /**
     * 有重叠元素
     */
    String HASANYOF = "HASANYOF";
    /**
     * 过去N天内(含当天)
     */
    String LTEO = "LTEO";
    /**
     * 未来N天内(含当天)
     */
    String GTEO = "GTEO";
    /**
     * 介于之间；时间段
     */
    String BETWEEN = "BETWEEN";
}
