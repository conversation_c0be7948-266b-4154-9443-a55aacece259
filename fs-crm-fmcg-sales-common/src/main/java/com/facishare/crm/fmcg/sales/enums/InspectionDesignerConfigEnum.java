package com.facishare.crm.fmcg.sales.enums;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.sales.apiname.InspectionRecordObjApiNames;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Getter
public enum InspectionDesignerConfigEnum {
    //稽查全局配置
    globalType("global", "type", Integer.class, 0, "稽查类型", Lists.newArrayList("常规稽查", "扫码系统稽查")),
    globalMethod("global", "method", Integer.class, 0, "稽查方式", Lists.newArrayList("扫二维码稽查", "扫条形码稽查")),
    globalName("global", "name", String.class, "", "流程名称", Collections.emptyList()),
    globalDescription("global", "description", String.class, "", "流程描述", Collections.emptyList()),
    globalApplicableScopeType("global", "applicableScopeType", Integer.class, 0, "适用范围", Lists.newArrayList("品牌/经销商人员", "下游渠道客户")),
    globalUserIds("global", "userIds", List.class, Collections.emptyList(), "同事", Collections.emptyList()),
    globalDeptIds("global", "deptIds", List.class, Collections.emptyList(), "部门", Collections.emptyList()),
    globalRoleGroupIds("global", "roleGroupIds", List.class, Collections.emptyList(), "角色", Collections.emptyList()),
    globalUserGroupIds("global", "userGroupIds", List.class, Collections.emptyList(), "用户组", Collections.emptyList()),
    globalIsAllOuter("global", "isAllOuter", Boolean.class, Boolean.FALSE, "游渠道客户/全部", Collections.emptyList()),
    globalOuterTenantIds("global", "outerTenantIds", List.class, Collections.emptyList(), "互联企业", Collections.emptyList()),
    globalOuterRoleIds("global", "outerRoleIds", List.class, Collections.emptyList(), "互联角色", Collections.emptyList()),
    globalOuterUserIds("global", "outerUserIds", List.class, Collections.emptyList(), "互联人员", Collections.emptyList()),
    globalTenantGroupIds("global", "tenantGroupIds", List.class, Collections.emptyList(), "企业组", Collections.emptyList()),
    globalRelatedObjectApiName("global", "relatedObjectApiName", String.class, InspectionRecordObjApiNames.OBJECT_API_NAME, "关联业务对象", Collections.emptyList()),
    globalRelatedObjectRecordTypeApiName("global", "relatedObjectRecordTypeApiName", String.class, "", "关联对象业务类型", Collections.emptyList()),
    //稽查主页配置
    pageHomeEnable("pageHome", "enable", Boolean.class, Boolean.TRUE, "是否开启", Collections.emptyList()),
    pageHomePageName("pageHome", "pageName", String.class, "", "页面名称", Collections.emptyList()),
    pageHomeNodeType("pageHome", "nodeType", Integer.class, 0, "节点类型 0", Lists.newArrayList("H5页面")),
    pageHomeAutoQueryCustomer("pageHome", "autoQueryCustomer", Integer.class, 0, "自动获取客户", Lists.newArrayList("不自动获取", "当前登陆人员所属企业对应客户", "客户销售区域覆盖当前地区的客户")),
    pageHomeTopImageNPath("pageHome", "topImageNPath", String.class, "", "顶部照片", Collections.emptyList()),
    pageHomeScanButtonTip("pageHome", "scanButtonTip", String.class, "", "扫描按钮提示文字", Collections.emptyList()),
    pageHomeBottomImageNPath("pageHome", "bottomImageNPath", String.class, "", "底部照片", Collections.emptyList()),
    //稽查扫码配置
    scanCodeEnable("scanCode", "enable", Boolean.class, Boolean.TRUE, "是否开启", Collections.emptyList()),
    scanCodePageName("scanCode", "pageName", String.class, "", "页面名称", Collections.emptyList()),
    scanCodeNodeType("scanCode", "nodeType", Integer.class, 0, "节点类型 0/其他", Lists.newArrayList("其他")),
    scanCodeJudgmentRuleId("scanCode", "judgmentRuleId", String.class, null, "窜货判断规则Id", Collections.emptyList()),
    scanCodeContinuous("scanCode", "continuous", Boolean.class, Boolean.FALSE, "连续扫码", Collections.emptyList()),
    scanCodeAllowableDeviationDistance("scanCode", "allowableDeviationDistance", BigDecimal.class, BigDecimal.valueOf(-1), "扫码位置允许距离偏差(米)", Collections.emptyList()),
    scanCodeRestrictedType("scanCode", "restrictedType", Integer.class, 1, "限制类型", Lists.newArrayList("限制不允许扫码", "仅提醒，不限制扫码")),
    scanCodeShowStatus("scanCode", "showStatus", List.class, Collections.emptyList(), "扫码展示状态", Collections.emptyList()),
    //稽查产品信息收集
    productInfoCollectionEnable("productInfoCollection", "enable", Boolean.class, Boolean.FALSE, "是否开启", Collections.emptyList()),
    productInfoCollectionPageName("productInfoCollection", "pageName", String.class, "", "页面名称", Collections.emptyList()),
    productInfoCollectionNodeType("productInfoCollection", "nodeType", Integer.class, 0, "节点类型 0", Lists.newArrayList("对象信息")),
    productInfoCollectionDescription("productInfoCollection", "description", String.class, "", "节点描述", Collections.emptyList()),
    productInfoCollectionBusinessObjectApiName("productInfoCollection", "businessObjectApiName", String.class, InspectionRecordObjApiNames.OBJECT_API_NAME, "对象ApiName", Collections.emptyList()),
    productInfoCollectionSourceLayoutApiName("productInfoCollection", "sourceLayoutApiName", String.class, "", "源字段来源布局", Collections.emptyList()),
    productInfoCollectionShowFields("productInfoCollection", "showFields", List.class, Collections.emptyList(), "展示字段", Collections.emptyList()),
    productInfoCollectionBottomButtonName("productInfoCollection", "bottomButtonName", String.class, "", "底部按钮名称", Collections.emptyList()),
    //稽查复核配置
    reviewEnable("review", "enable", Boolean.class, Boolean.TRUE, "是否开启", Collections.emptyList()),
    reviewPageName("review", "pageName", String.class, "", "页面名称", Collections.emptyList()),
    reviewNodeType("review", "nodeType", Integer.class, 0, "节点类型 0", Lists.newArrayList("对象信息")),
    reviewDescription("review", "description", String.class, "", "节点描述", Collections.emptyList()),
    reviewBusinessObjectApiName("review", "businessObjectApiName", String.class, InspectionRecordObjApiNames.OBJECT_API_NAME, "对象ApiName", Collections.emptyList()),
    reviewSourceLayoutApiName("review", "sourceLayoutApiName", String.class, "", "源字段来源布局", Collections.emptyList()),
    reviewShowFields("review", "showFields", List.class, Collections.emptyList(), "展示字段", Collections.emptyList()),
    reviewBottomButtonName("review", "bottomButtonName", String.class, "", "底部按钮名称", Collections.emptyList()),
    reviewResultStatus("review", "resultStatus", Integer.class, 0, "结果状态", Lists.newArrayList("异常", "未知")),
    //稽查结果配置
    resultEnable("result", "enable", Boolean.class, Boolean.TRUE, "是否开启", Collections.emptyList()),
    resultPageName("result", "pageName", String.class, "", "页面名称", Collections.emptyList()),
    resultNodeType("result", "nodeType", Integer.class, 0, "节点类型", Lists.newArrayList("对象详情信息")),
    resultBusinessObjectApiName("result", "businessObjectApiName", String.class, InspectionRecordObjApiNames.OBJECT_API_NAME, "对象ApiName", Collections.emptyList()),
    resultSourceLayoutApiName("result", "sourceLayoutApiName", String.class, "", "源字段来源布局", Collections.emptyList()),
    resultCloseButtonName("result", "closeButtonName", String.class, "", "关闭按钮名称", Collections.emptyList()),
    resultContinuedScanButtonName("result", "continuedScanButtonName", String.class, "", "继续扫码按钮名称", Collections.emptyList()),
    //稽查申诉配置
    appealEnable("appeal", "enable", Boolean.class, Boolean.TRUE, "是否开启", Collections.emptyList()),
    appealPageName("appeal", "pageName", String.class, "", "页面名称", Collections.emptyList()),
    appealNodeType("appeal", "nodeType", Integer.class, 0, "节点类型", Lists.newArrayList("关联对象新建")),
    appealBusinessObjectApiName("appeal", "businessObjectApiName", String.class, InspectionRecordObjApiNames.OBJECT_API_NAME, "业务对象ApiName", Collections.emptyList()),
    appealSourceLayoutApiName("appeal", "sourceLayoutApiName", String.class, "", "源字段来源布局", Collections.emptyList()),
    //稽查额外收集
    extraEnable("extra", "enable", Boolean.class, Boolean.TRUE, "是否开启", Collections.emptyList()),
    extraPageName("extra", "pageName", String.class, "", "页面名称", Collections.emptyList()),
    extraNodeType("extra", "nodeType", Integer.class, 0, "节点类型", Lists.newArrayList("对象详情信息")),
    extraDescription("extra", "description", String.class, "", "节点描述", Collections.emptyList()),
    extraBusinessObjectApiName("extra", "businessObjectApiName", String.class, "", "对象ApiName", Collections.emptyList()),
    extraSourceLayoutApiName("extra", "sourceLayoutApiName", String.class, "", "源字段来源布局", Collections.emptyList()),
    extraShowFields("extra", "showFields", List.class, Collections.emptyList(), "展示字段", Collections.emptyList()),
    extraBottomButtonName("extra", "bottomButtonName", String.class, "", "底部按钮名称", Collections.emptyList()),
    //稽查审核配置
    auditsEnable("audits", "enable", Boolean.class, Boolean.TRUE, "是否开启", Collections.emptyList()),

    //系统配置
    systemCreateBy("system", "createBy", Integer.class, -10000, "创建人", Collections.emptyList()),
    systemModifyBy("system", "modifyBy", Integer.class, -10000, "修改人", Collections.emptyList()),
    systemModifyTime("system", "modifyTime", Long.class, null, "修改时间", Collections.emptyList()),

    //业务配置
    businessUnEffectiveUserIds("business", "unEffectiveUserIds", List.class, Collections.emptyList(), "强制未使用配置的人", Collections.emptyList()),

    ;
    private final String groupKey;
    private final String key;
    private final Class<?> valueClass;
    private final Object defaultValue;
    private final String description;
    private final List<String> items;

    public static final Map<String, InspectionDesignerConfigEnum> inspectionDesignerConfigEnumMap = new HashMap<>();

    static {
        for (InspectionDesignerConfigEnum value : InspectionDesignerConfigEnum.values()) {
            inspectionDesignerConfigEnumMap.put(value.getGroupKey() + "_" + value.getKey(), value);
        }
    }

    <E> InspectionDesignerConfigEnum(String groupKey, String key, Class<E> valueClass, E defaultValue, String description, List<String> items) {
        this.groupKey = groupKey;
        this.key = key;
        this.valueClass = valueClass;
        this.defaultValue = defaultValue;
        this.description = description;
        this.items = items;
    }


    public Object convertValue(String value) {
        if (Objects.isNull(value)) {
            return defaultValue;
        }
        return JSON.parseObject(value, this.valueClass);
    }

    @SuppressWarnings("unchecked")
    public static <T> T getConfigValue(Map<String, JSONObject> configMap, InspectionDesignerConfigEnum configEnum) {
        if (MapUtils.isEmpty(configMap) || configEnum == null) {
            return null;
        }
        String groupKey = configEnum.getGroupKey();
        String key = configEnum.getKey();
        Class<T> targetType = (Class<T>) configEnum.getValueClass();

        if (!configMap.containsKey(groupKey)) {
            return null;
        }
        JSONObject groupConfig = configMap.get(groupKey);
        if (Objects.isNull(groupConfig) || !groupConfig.containsKey(key)) {
            return null;
        }
        try {
            return groupConfig.getObject(key, targetType);
        } catch (Exception e) {
            log.info("getConfigValue error, groupKey: {}, key: {}, targetType: {}", groupKey, key, targetType);
            log.error("getConfigValue error", e);
            return null;
        }
    }

    public String getItemByIndex(int index) {
        if (this.getItems().size() <= index) {
            return null;
        } else {
            return this.getItems().get(index);
        }
    }

    public static Map<String, JSONObject> getDefaultConfigMap() {
        Map<String, JSONObject> data = new HashMap<>();
        for (InspectionDesignerConfigEnum configEnum : InspectionDesignerConfigEnum.values()) {
            String groupKey = configEnum.getGroupKey();
            if (!data.containsKey(groupKey)) {
                data.put(groupKey, new JSONObject().fluentPut(configEnum.getKey(), configEnum.getDefaultValue()));
            } else {
                data.get(groupKey).fluentPut(configEnum.getKey(), configEnum.getDefaultValue());
            }
        }
        return data;
    }

    public static List<String> getScopeRelationKeys() {
        List<String> keys = Lists.newArrayList();
        keys.add(globalApplicableScopeType.getKey());
        keys.add(globalUserIds.getKey());
        keys.add(globalDeptIds.getKey());
        keys.add(globalRoleGroupIds.getKey());
        keys.add(globalUserGroupIds.getKey());
        keys.add(globalOuterTenantIds.getKey());
        keys.add(globalOuterRoleIds.getKey());
        keys.add(globalOuterUserIds.getKey());
        keys.add(globalTenantGroupIds.getKey());
        keys.add(businessUnEffectiveUserIds.getKey());
        keys.add(globalIsAllOuter.getKey());
        return keys;
    }

    public static List<String> getScopeRelationGroupKeys() {
        List<String> keys = Lists.newArrayList();
        keys.add(globalUserIds.getGroupKey());
        keys.add(businessUnEffectiveUserIds.getGroupKey());
        return keys;
    }

}
