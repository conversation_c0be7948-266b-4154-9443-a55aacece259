package com.facishare.crm.fmcg.sales.apiname;

public interface ProductApiNames {

    String OBJECT_API_NAME = "ProductObj";

    String SHELF_LIFE = "shelf_life";

    String NAME = "name";

    String DISPLAY_NAME = "display_name";

    String BARCODE = "barcode";

    String UNIT = "unit";

    String IS_MULTIPLE_UNIT = "is_multiple_unit";

    String PRODUCT_CODE = "product_code";

    String SPU_ID = "spu_id";

    String PRODUCT_CUSTOM_INFO_COMPONENT = "productCustomInfoComponent";

    String CATEGORY = "category";

    String SHOP_CATEGORY_ID = "shop_category_id";

    String PICTURE_PATH = "picture_path";

    String MNEMONIC_CODE = "mnemonic_code";

    String PRODUCT_SPEC = "product_spec";

    String UNIQUE_PRODUCT_CODE_MANAGEMENT = "unique_product_code_management";

    String TURNOVER_BOX_OF_NUMBERS = "turnover_box_of_numbers";

    String PRODUCT_STATUS = "product_status";

    String BATCH_SN = "batch_sn";

    String PRICE = "price";
}
