<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.fxiaoke.cloud</groupId>
        <artifactId>fxiaoke-spring-cloud-parent</artifactId>
        <version>2.7.0-SNAPSHOT</version>
    </parent>

    <groupId>com.facishare</groupId>
    <artifactId>fs-crm-fmcg-sales</artifactId>
    <packaging>pom</packaging>
    <version>8.6.0-SNAPSHOT</version>
    <modules>
        <module>fs-crm-fmcg-sales-web</module>
        <module>fs-crm-fmcg-sales-core</module>
        <module>fs-crm-fmcg-sales-common</module>
        <module>fs-crm-fmcg-sales-mq</module>
    </modules>

    <properties>
        <crm.fmcg.version>8.6.0-SNAPSHOT</crm.fmcg.version>
        <java.version>21</java.version>
        <maven.compiler.release>8</maven.compiler.release>
        <appframework.version>9.6.0-SNAPSHOT</appframework.version>
        <i18n-util.version>1.4-SNAPSHOT</i18n-util.version>
        <okio.version>3.2.0</okio.version>
        <paas-auth.version>2.4.0-SNAPSHOT</paas-auth.version>
        <resteasy.version>3.15.1.Final</resteasy.version>
        <rest-es7-support.version>3.6.0-SNAPSHOT</rest-es7-support.version>
        <webApp.contextPath/>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-core</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-api</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-metadata</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-common</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-flow</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-log</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-license</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-web</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-metadata-restdriver</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-privilege</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-privilege-temp</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-fcp</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-config</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-metadata-util</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-coordination</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <!--租户级配置-->
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-bizconf-api</artifactId>
                <version>2.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>fs-wechat-union-core-api</artifactId>
                <version>0.0.3-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>fs-wechat-proxy-core-api</artifactId>
                <version>0.0.8-SNAPSHOT</version>
            </dependency>


            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>i18n-util</artifactId>
                <version>${i18n-util.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fxiaoke</groupId>
                <artifactId>fs-hosts-record</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <!-- 指定 okio 的版本 -->
            <dependency>
                <groupId>com.squareup.okio</groupId>
                <artifactId>okio</artifactId>
                <version>${okio.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-pod-client</artifactId>
                <version>9.0.1-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>